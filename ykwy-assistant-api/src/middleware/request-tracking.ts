import { randomUUID } from 'crypto';
import type { Context, Next } from 'hono';

import { logger } from '../lib/logger';

/**
 * 请求追踪中间件
 * 为每个请求生成唯一ID，记录请求开始和结束时间，计算处理时长
 */
export async function requestTracking(c: Context, next: Next) {
  const requestId = randomUUID();
  const startTime = Date.now();

  // 设置请求ID到上下文
  c.set('requestId', requestId);

  // 设置响应头，方便前端追踪
  c.header('X-Request-ID', requestId);

  // 记录请求开始
  const contextLogger = logger.withContext(c);
  contextLogger.info('Request started', {
    method: c.req.method,
    url: c.req.url,
    userAgent: c.req.header('user-agent'),
    ip: c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown',
    contentType: c.req.header('content-type'),
  });

  try {
    await next();

    // 记录请求成功完成
    const duration = Date.now() - startTime;
    const status = c.res.status;

    contextLogger.info('Request completed', {
      status,
      duration: `${duration}ms`,
      success: status < 400,
    });
  } catch (error) {
    // 记录请求错误
    const duration = Date.now() - startTime;

    contextLogger.error(
      'Request failed',
      {
        duration: `${duration}ms`,
        method: c.req.method,
        url: c.req.url,
      },
      error instanceof Error ? error : new Error(String(error)),
    );

    // 重新抛出错误，让其他错误处理中间件处理
    throw error;
  }
}

/**
 * 性能监控中间件
 * 监控慢请求和资源使用情况
 */
export async function performanceMonitoring(c: Context, next: Next) {
  const startTime = process.hrtime.bigint();
  const startMemory = process.memoryUsage();

  await next();

  const endTime = process.hrtime.bigint();
  const endMemory = process.memoryUsage();
  const duration = Number(endTime - startTime) / 1000000; // 转换为毫秒

  // 记录慢请求（超过1秒）
  if (duration > 1000) {
    const contextLogger = logger.withContext(c);
    contextLogger.warn('Slow request detected', {
      method: c.req.method,
      url: c.req.url,
      duration: `${duration.toFixed(2)}ms`,
      memoryUsage: {
        heapUsed: `${Math.round(((endMemory.heapUsed - startMemory.heapUsed) / 1024 / 1024) * 100) / 100}MB`,
        heapTotal: `${Math.round((endMemory.heapTotal / 1024 / 1024) * 100) / 100}MB`,
        external: `${Math.round((endMemory.external / 1024 / 1024) * 100) / 100}MB`,
      },
    });
  }

  // 在开发环境中记录所有请求的性能信息
  if (Bun.env.NODE_ENV === 'development' && duration > 100) {
    const contextLogger = logger.withContext(c);
    contextLogger.debug('Request performance', {
      method: c.req.method,
      url: c.req.url,
      duration: `${duration.toFixed(2)}ms`,
      status: c.res.status,
    });
  }
}

/**
 * 错误上下文增强中间件
 * 为错误添加更多上下文信息
 */
export async function errorContextEnhancement(c: Context, next: Next) {
  try {
    await next();
  } catch (error) {
    // 增强错误信息
    if (error instanceof Error) {
      // 添加请求上下文到错误对象
      const enhancedError = error as Error & {
        requestId?: string;
        method?: string;
        url?: string;
        userAgent?: string;
        timestamp?: string;
      };

      enhancedError.requestId = c.get('requestId');
      enhancedError.method = c.req.method;
      enhancedError.url = c.req.url;
      enhancedError.userAgent = c.req.header('user-agent');
      enhancedError.timestamp = new Date().toISOString();
    }

    throw error;
  }
}
