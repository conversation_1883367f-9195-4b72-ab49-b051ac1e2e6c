import { zValida<PERSON> } from '@hono/zod-validator';
import { Hono } from 'hono';
import { z } from 'zod';

import { createErrorResponse, createResponse, handleError } from '../lib/utils';
import { OrganizationInvitationService } from '../services/organization-invitation.service';

const app = new Hono();

// 验证Schema
const CreateInvitationSchema = z.object({
  email: z.string().email('邮箱格式不正确'),
  role: z.enum(['SYSTEM_ADMIN', 'ORGANIZATION_ADMIN', 'TEAM_MANAGER', 'CUSTOMER_SERVICE']).optional(),
  organizationId: z.string().uuid('无效的组织ID'),
  teamId: z.string().uuid('无效的团队ID').optional(),
  inviterId: z.string().uuid('无效的邀请人ID'),
});

const AcceptInvitationSchema = z.object({
  token: z.string().min(1, '邀请令牌不能为空'),
  name: z.string().min(1, '姓名不能为空'),
  password: z.string().min(6, '密码至少6位'),
});

const InvitationTokenSchema = z.object({
  token: z.string().min(1, '邀请令牌不能为空'),
});

const InvitationIdSchema = z.object({
  invitationId: z.string().uuid('无效的邀请ID'),
});

const OrganizationIdSchema = z.object({
  organizationId: z.string().uuid('无效的组织ID'),
});

/**
 * POST /organization-invitations - 创建组织邀请
 */
app.post('/', zValidator('json', CreateInvitationSchema), async (c) => {
  try {
    const data = c.req.valid('json');

    const result = await OrganizationInvitationService.createInvitation(data);

    return c.json(createResponse(result, '邀请创建成功'), 201);
  } catch (error) {
    const message = error instanceof Error ? error.message : '创建邀请失败';
    return c.json(createErrorResponse(message), 400);
  }
});

/**
 * GET /organization-invitations/token/:token - 获取邀请详情
 */
app.get('/token/:token', zValidator('param', InvitationTokenSchema), async (c) => {
  try {
    const { token } = c.req.valid('param');

    const invitation = await OrganizationInvitationService.getInvitation(token);

    return c.json(createResponse(invitation, '获取邀请详情成功'));
  } catch (error) {
    const message = error instanceof Error ? error.message : '获取邀请详情失败';
    return c.json(createErrorResponse(message), 404);
  }
});

/**
 * POST /organization-invitations/accept - 接受邀请
 */
app.post('/accept', zValidator('json', AcceptInvitationSchema), async (c) => {
  try {
    const data = c.req.valid('json');

    const result = await OrganizationInvitationService.acceptInvitation(data);

    return c.json(createResponse(result, '邀请接受成功，用户创建完成'));
  } catch (error) {
    const message = error instanceof Error ? error.message : '接受邀请失败';
    return c.json(createErrorResponse(message), 400);
  }
});

/**
 * PUT /organization-invitations/:invitationId/revoke - 撤销邀请
 */
app.put(
  '/:invitationId/revoke',
  zValidator('param', InvitationIdSchema),
  zValidator(
    'json',
    z.object({
      userId: z.string().uuid('无效的用户ID'),
    }),
  ),
  async (c) => {
    try {
      const { invitationId } = c.req.valid('param');
      const { userId } = c.req.valid('json');

      const result = await OrganizationInvitationService.revokeInvitation(invitationId, userId);

      return c.json(createResponse(result, '邀请已撤销'));
    } catch (error) {
      const message = error instanceof Error ? error.message : '撤销邀请失败';
      return c.json(createErrorResponse(message), 400);
    }
  },
);

/**
 * GET /organization-invitations/organization/:organizationId - 获取组织邀请列表
 */
app.get('/organization/:organizationId', zValidator('param', OrganizationIdSchema), async (c) => {
  try {
    const { organizationId } = c.req.valid('param');

    const invitations = await OrganizationInvitationService.getOrganizationInvitations(organizationId);

    return c.json(createResponse(invitations, '获取邀请列表成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

export default app;
