import { z<PERSON><PERSON><PERSON><PERSON> } from '@hono/zod-validator';
import { Hono } from 'hono';
import { z } from 'zod';

import { prisma } from '../lib/db';
import { logger } from '../lib/logger';
import { createErrorResponse, createResponse, handleError } from '../lib/utils';
import { qianniuTcpServer } from '../services/qianniuTcpServer';

const app = new Hono();

// 验证schemas
const RegisterConnectionSchema = z.object({
  connectionId: z.string().min(1, '连接ID不能为空'),
  tcpClientId: z.string().optional(),
});

/**
 * GET /qianniu-tcp/connections - 获取TCP连接状态
 */
app.get('/connections', async (c) => {
  try {
    logger.info('收到获取TCP连接状态请求');

    const connectionsInfo = qianniuTcpServer.getConnectionsInfo();
    logger.debug('TCP连接信息', { connectionsInfo });

    // 获取数据库中的在线客户端信息，包括当前活跃的conversation
    const onlineClients = await prisma.qianniuClient.findMany({
      where: {
        isOnline: true,
        connectionId: { not: '' },
      },
      select: {
        id: true,
        name: true,
        connectionId: true,
        accounts: {
          select: {
            accountName: true,
            shopName: true,
            platformType: true,
            conversations: {
              where: {
                status: { not: 'CLOSED' },
              },
              orderBy: {
                lastActivity: 'desc',
              },
              take: 1,
              select: {
                id: true,
                conversationCode: true, // 使用新的conversationCode字段
                title: true, // 保留title作为向后兼容
                lastActivity: true,
                customer: {
                  select: {
                    nickname: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    logger.info('数据库中的在线客户端数量', { count: onlineClients.length });

    // 构建增强的connectionMappings，包含conversationCode信息
    const enhancedConnectionMappings: Record<string, any> = {};
    for (const [connectionId, clientId] of Object.entries(connectionsInfo.connectionMappings)) {
      const client = onlineClients.find((c) => c.connectionId === connectionId);
      const activeConversation = client?.accounts?.[0]?.conversations?.[0];

      enhancedConnectionMappings[connectionId] = {
        clientId,
        currentConversationId: activeConversation?.conversationCode || activeConversation?.title || null, // 优先使用新的conversationCode字段
        currentConversationTitle: activeConversation?.conversationCode || activeConversation?.title,
        customerName: activeConversation?.customer?.nickname,
        lastActivity: activeConversation?.lastActivity,
      };
    }

    const responseData = {
      ...connectionsInfo,
      connectionMappings: enhancedConnectionMappings, // 使用增强的映射
      onlineClients,
      unmappedTcpConnections: qianniuTcpServer.getUnmappedTcpConnections(),
    };

    logger.debug('返回TCP连接状态数据', { responseData });
    return c.json(createResponse(responseData, '获取TCP连接状态成功'));
  } catch (error) {
    logger.error('获取TCP连接状态失败', {}, error instanceof Error ? error : new Error(String(error)));
    return handleError(c, error);
  }
});

/**
 * POST /qianniu-tcp/register - 手动注册连接映射
 */
app.post('/register', zValidator('json', RegisterConnectionSchema), async (c) => {
  try {
    const { connectionId, tcpClientId } = c.req.valid('json');
    logger.info('收到注册连接映射请求', { connectionId, tcpClientId: tcpClientId || '未指定' });

    // 检查connectionId是否存在于数据库中
    const client = await prisma.qianniuClient.findFirst({
      where: { connectionId },
    });

    if (!client) {
      logger.warn('连接ID不存在于数据库中', { connectionId });
      return c.json(createErrorResponse('连接ID不存在'), 404);
    }

    logger.info('找到对应的客户端', {
      clientId: client.id,
      clientName: client.name,
      connectionId: client.connectionId,
    });

    // 尝试注册连接映射
    logger.info('开始注册连接映射', { connectionId, tcpClientId });
    const success = qianniuTcpServer.manualRegisterConnection(connectionId, tcpClientId);

    if (!success) {
      logger.error('注册连接映射失败', { connectionId, tcpClientId });
      return c.json(createErrorResponse('注册连接映射失败'), 400);
    }

    const responseData = {
      connectionId,
      tcpClientId: tcpClientId || '自动选择',
      success: true,
    };

    logger.info('连接映射注册成功', { responseData });
    return c.json(createResponse(responseData, '连接映射注册成功'));
  } catch (error) {
    logger.error('注册连接映射异常', {}, error instanceof Error ? error : new Error(String(error)));
    return handleError(c, error);
  }
});

/**
 * DELETE /qianniu-tcp/connections/:connectionId - 删除连接映射
 */
app.delete('/connections/:connectionId', async (c) => {
  try {
    const connectionId = c.req.param('connectionId');

    if (!connectionId) {
      return c.json(createErrorResponse('连接ID不能为空'), 400);
    }

    // 删除映射关系
    const tcpClientId = qianniuTcpServer['connectionIdToClientId'].get(connectionId);
    if (tcpClientId) {
      qianniuTcpServer['connectionIdToClientId'].delete(connectionId);
      qianniuTcpServer['clientIdToConnectionId'].delete(tcpClientId);

      return c.json(
        createResponse(
          {
            connectionId,
            tcpClientId,
            deleted: true,
          },
          '连接映射删除成功',
        ),
      );
    } else {
      return c.json(createErrorResponse('连接映射不存在'), 404);
    }
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * POST /qianniu-tcp/test-send - 测试发送消息
 */
app.post(
  '/test-send',
  zValidator(
    'json',
    z.object({
      connectionId: z.string().min(1, '连接ID不能为空'),
      targetId: z.string().min(1, '目标ID不能为空'),
      message: z.string().min(1, '消息内容不能为空'),
    }),
  ),
  async (c) => {
    try {
      const { connectionId, targetId, message } = c.req.valid('json');
      logger.info('收到测试发送消息请求', {
        connectionId,
        targetId,
        message,
      });

      // 检查连接映射是否存在
      logger.debug('检查连接映射是否存在', { connectionId });
      if (!qianniuTcpServer.hasConnectionMapping(connectionId)) {
        logger.warn('连接映射不存在', { connectionId });
        return c.json(createErrorResponse('连接映射不存在，请先注册连接'), 400);
      }

      logger.info('连接映射存在，开始发送消息', { connectionId });

      // 尝试发送消息
      const success = await qianniuTcpServer.sendTextMessage(connectionId, targetId, message);

      const responseData = {
        connectionId,
        targetId,
        message,
        success,
      };

      logger.info(`消息发送${success ? '成功' : '失败'}`, { responseData });
      return c.json(createResponse(responseData, success ? '消息发送成功' : '消息发送失败'));
    } catch (error) {
      logger.error('测试发送消息异常', {}, error instanceof Error ? error : new Error(String(error)));
      return handleError(c, error);
    }
  },
);

// SSE连接存储
const sseConnections = new Map<
  string,
  {
    organizationId: string;
    controller: any;
    lastHeartbeat: number;
  }
>();

/**
 * OPTIONS /qianniu-tcp/conversation-switch-stream - 处理CORS预检请求
 */
app.options('/conversation-switch-stream', async (c) => {
  // 设置CORS头部
  c.header('Access-Control-Allow-Origin', '*');
  c.header('Access-Control-Allow-Methods', 'GET, OPTIONS');
  c.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Cache-Control');
  c.header('Access-Control-Max-Age', '86400'); // 24小时

  c.status(204);
  return c.body(null);
});

/**
 * GET /qianniu-tcp/conversation-switch-stream - 监听会话切换事件（SSE）
 */
app.get('/conversation-switch-stream', async (c) => {
  try {
    const organizationId = c.req.query('organizationId');

    if (!organizationId) {
      return c.json(createErrorResponse('organizationId参数不能为空'), 400);
    }

    logger.info('新的会话切换监听连接', { organizationId });

    // 设置SSE响应头
    c.header('Content-Type', 'text/event-stream');
    c.header('Cache-Control', 'no-cache');
    c.header('Connection', 'keep-alive');
    c.header('Access-Control-Allow-Origin', '*');
    c.header('Access-Control-Allow-Headers', 'Cache-Control');

    // 使用Hono的流响应
    const { readable, writable } = new TransformStream();
    const writer = writable.getWriter();

    const connectionId = `sse_${Date.now()}_${Math.random().toString(36).slice(2)}`;

    // 存储连接
    sseConnections.set(connectionId, {
      organizationId,
      controller: writer,
      lastHeartbeat: Date.now(),
    });

    // 发送初始连接确认
    try {
      await writer.write(
        new TextEncoder().encode(
          `data: ${JSON.stringify({
            type: 'connection_established',
            connectionId,
            timestamp: new Date().toISOString(),
          })}\n\n`,
        ),
      );

      // 发送心跳
      const heartbeatInterval = setInterval(async () => {
        try {
          await writer.write(
            new TextEncoder().encode(
              `data: ${JSON.stringify({
                type: 'heartbeat',
                timestamp: new Date().toISOString(),
              })}\n\n`,
            ),
          );

          // 更新心跳时间
          const conn = sseConnections.get(connectionId);
          if (conn) {
            conn.lastHeartbeat = Date.now();
          }
        } catch (error) {
          logger.debug('心跳发送失败，连接可能已关闭', { connectionId });
          clearInterval(heartbeatInterval);
          sseConnections.delete(connectionId);
          await writer.close();
        }
      }, 30000); // 30秒心跳

      // 监听连接关闭
      const cleanup = () => {
        logger.info('SSE连接关闭', { connectionId, organizationId });
        clearInterval(heartbeatInterval);
        sseConnections.delete(connectionId);
        writer.close();
      };

      // 设置清理监听器
      c.req.raw.signal?.addEventListener('abort', cleanup);

      logger.info('SSE连接建立成功', { connectionId, organizationId });
    } catch (error) {
      logger.error('SSE连接初始化失败', { connectionId }, error instanceof Error ? error : new Error(String(error)));
      sseConnections.delete(connectionId);
      await writer.close();
    }

    return new Response(readable, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
      },
    });
  } catch (error) {
    logger.error('建立SSE连接失败', {}, error instanceof Error ? error : new Error(String(error)));
    return handleError(c, error);
  }
});

/**
 * POST /qianniu-tcp/broadcast-conversation-switch - 手动广播会话切换事件（测试用）
 */
app.post(
  '/broadcast-conversation-switch',
  zValidator(
    'json',
    z.object({
      organizationId: z.string().min(1, '组织ID不能为空'),
      conversationId: z.string().min(1, '会话ID不能为空'),
      customerInfo: z.object({
        id: z.string(),
        nickname: z.string(),
        avatar: z.string().optional(),
      }),
      connectionId: z.string().min(1, '连接ID不能为空'),
    }),
  ),
  async (c) => {
    try {
      const { organizationId, conversationId, customerInfo, connectionId } = c.req.valid('json');

      // 广播会话切换事件
      await broadcastConversationSwitch(organizationId, {
        currentConversationId: conversationId,
        customerInfo,
        connectionId,
        timestamp: new Date().toISOString(),
      });

      return c.json(createResponse({ success: true }, '会话切换事件广播成功'));
    } catch (error) {
      logger.error('广播会话切换事件失败', {}, error instanceof Error ? error : new Error(String(error)));
      return handleError(c, error);
    }
  },
);

/**
 * 广播会话切换事件到所有SSE连接
 */
export async function broadcastConversationSwitch(
  organizationId: string,
  conversationData: {
    currentConversationId: string;
    customerInfo: { id: string; nickname: string; avatar?: string };
    connectionId: string;
    timestamp: string;
    previousConversationId?: string;
  },
) {
  const message = JSON.stringify({
    type: 'qianniu-conversation-switch',
    payload: conversationData,
  });

  let sentCount = 0;
  const connections = Array.from(sseConnections.entries()).filter(([_, connection]) => connection.organizationId === organizationId);

  for (const [connectionId, connection] of connections) {
    try {
      await connection.controller.write(new TextEncoder().encode(`data: ${message}\n\n`));
      sentCount++;
    } catch (error) {
      logger.debug('SSE消息发送失败，移除连接', { connectionId });
      sseConnections.delete(connectionId);
      connection.controller.close();
    }
  }

  logger.info('会话切换事件SSE广播完成', {
    organizationId,
    conversationId: conversationData.currentConversationId,
    sentCount,
    totalConnections: connections.length,
  });
}

export default app;
