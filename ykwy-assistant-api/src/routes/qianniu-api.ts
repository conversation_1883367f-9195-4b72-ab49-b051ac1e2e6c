/**
 * 千牛API集成路由
 * 提供HTTP接口用于调用千牛智能客服API
 */

import { type Context, Hono } from 'hono';

import { logger } from '../lib/logger';
import { platformWsManager } from '../lib/platform-websocket';
import { type ApiCallRequest, qianniuApiWebSocketService } from '../services/qianniuApiWebSocketService';

const app = new Hono();

// 通用响应格式
const createResponse = (data: unknown, message = 'Success') => ({
  success: true,
  message,
  data,
  timestamp: new Date().toISOString(),
});

const createErrorResponse = (message: string, error?: unknown) => ({
  success: false,
  message,
  error: error instanceof Error ? error.message : String(error),
  timestamp: new Date().toISOString(),
});

/**
 * 获取客户的encryptId
 * 通过客户昵称查询千牛API获取encryptId
 */
export async function getCustomerEncryptId(customerId: string, connectionId: string): Promise<string | null> {
  try {
    const { prisma } = await import('../lib/db');

    // 1. 获取客户信息
    const customer = await prisma.customer.findUnique({
      where: { id: customerId },
      include: {
        qianniuAccount: {
          include: {
            client: true,
          },
        },
      },
    });

    if (!customer) {
      logger.warn('客户不存在', { customerId });
      return null;
    }

    logger.debug('找到客户信息', {
      customerId,
      nickname: customer.nickname,
      hasEncryptId: !!customer.encryptId,
      qianniuAccountId: customer.qianniuAccountId,
      clientConnectionId: customer.qianniuAccount?.client?.connectionId,
      providedConnectionId: connectionId
    });

    // 验证连接ID是否匹配（放宽验证，只记录警告）
    if (customer.qianniuAccount.client.connectionId !== connectionId) {
      logger.warn('连接ID不匹配，但继续处理', {
        customerId,
        expectedConnectionId: customer.qianniuAccount.client.connectionId,
        providedConnectionId: connectionId,
      });
      // 不直接返回null，继续处理
    }

    // 2. 如果数据库中已有 encryptId，直接返回
    if (customer.encryptId) {
      logger.debug('从数据库获取 encryptId', { customerId, encryptId: customer.encryptId });
      return customer.encryptId;
    }

    // 3. 如果没有 encryptId，通过内部API调用获取
    logger.info('数据库中没有 encryptId，通过内部API查询', { customerId, nickname: customer.nickname });

    try {
      // 直接调用内部的search-buyer-id端点
      const response = await fetch(`http://localhost:${process.env.PORT || 3002}/api/v1/qianniu-api/search-buyer-id`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          connectionId: connectionId,
          searchQuery: customer.nickname
        })
      });

      const result = await response.json();

      logger.debug('内部API调用结果', {
        customerId,
        nickname: customer.nickname,
        success: result.success,
        status: response.status,
        dataPreview: result.data ? JSON.stringify(result.data).substring(0, 200) : null
      });

      if (!response.ok || !result.success || !result.data) {
        logger.error('内部API调用失败', {
          customerId,
          nickname: customer.nickname,
          status: response.status,
          result
        });
        return null;
      }

      // 从内部API响应中提取encryptId
      const encryptId = extractEncryptIdFromSearchResult(result.data);

      if (!encryptId) {
        logger.warn('未找到匹配的买家', { customerId, nickname: customer.nickname });
        return null;
      }

      logger.info('成功提取encryptId', { customerId, nickname: customer.nickname, encryptId });

      // 4. 将获取到的 encryptId 保存到数据库
      try {
        await prisma.customer.update({
          where: { id: customerId },
          data: { encryptId }
        });
        logger.info('成功获取并缓存 encryptId', { customerId, encryptId });
      } catch (updateError) {
        logger.error('保存 encryptId 到数据库失败', { customerId, encryptId, error: updateError });
        // 即使保存失败，也返回获取到的 encryptId
      }

      return encryptId;

    } catch (fetchError) {
      logger.error('调用内部API失败', {
        customerId,
        nickname: customer.nickname,
        error: fetchError instanceof Error ? fetchError.message : String(fetchError)
      });
      return null;
    }

  } catch (error) {
    logger.error('获取encryptId失败', {
      customerId,
      connectionId,
      errorMessage: error instanceof Error ? error.message : String(error),
      errorStack: error instanceof Error ? error.stack : undefined
    }, error instanceof Error ? error : new Error(String(error)));
    return null;
  }
}

/**
 * 从千牛API搜索结果中提取encryptId
 */
function extractEncryptIdFromSearchResult(searchResult: any): string | null {
  try {
    logger.debug('开始解析搜索结果', {
      searchResult: JSON.stringify(searchResult).substring(0, 500)
    });

    // 根据实际API响应格式解析encryptId
    // 从您提供的响应看，数据结构是：searchResult.data.data.data
    let dataArray = null;

    if (searchResult?.data?.data?.data && Array.isArray(searchResult.data.data.data)) {
      dataArray = searchResult.data.data.data;
    }

    if (!dataArray || !Array.isArray(dataArray)) {
      logger.warn('搜索结果中没有找到数据数组', { searchResult });
      return null;
    }

    logger.debug('找到数据数组', {
      arrayLength: dataArray.length,
      firstItem: dataArray[0] ? JSON.stringify(dataArray[0]) : null
    });

    // 查找买家类型的账号
    const buyerAccount = dataArray.find((account: any) =>
      account.accountRoles && account.accountRoles.includes('buyer')
    );

    if (buyerAccount && buyerAccount.encryptAccountId) {
      logger.info('找到买家账号的encryptId', {
        encryptAccountId: buyerAccount.encryptAccountId,
        nick: buyerAccount.nick
      });
      return buyerAccount.encryptAccountId;
    }

    // 如果没有找到买家类型，使用第一个结果
    const firstResult = dataArray[0];
    if (firstResult && firstResult.encryptAccountId) {
      logger.info('使用第一个结果的encryptId', {
        encryptAccountId: firstResult.encryptAccountId,
        nick: firstResult.nick
      });
      return firstResult.encryptAccountId;
    }

    logger.warn('无法从搜索结果中提取encryptId', {
      dataArrayLength: dataArray.length,
      firstResult: firstResult ? JSON.stringify(firstResult) : null
    });
    return null;
  } catch (error) {
    logger.error('解析搜索结果失败', { searchResult }, error instanceof Error ? error : new Error(String(error)));
    return null;
  }
}

// 通用错误处理
const handleError = (c: Context, error: unknown) => {
  logger.error('千牛API路由错误', {}, error instanceof Error ? error : new Error(String(error)));
  return c.json(createErrorResponse('Internal Server Error', error), 500);
};

/**
 * GET /qianniu-api/connections - 获取可用的千牛连接
 */
app.get('/connections', async (c) => {
  try {
    const connections = platformWsManager.getPlatformConnections('QIANNIU');
    const connectionList = connections.map((conn) => ({
      connectionId: conn.connectionId,
      organizationId: conn.organizationId,
      teamId: conn.teamId,
      connectedAt: conn.connectedAt,
      lastHeartbeat: conn.lastHeartbeat,
      platformData: conn.platformData,
    }));

    return c.json(createResponse(connectionList, '千牛连接列表'));
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * GET /qianniu-api/accounts - 获取千牛账号列表
 */
app.get('/accounts', async (c) => {
  try {
    const { prisma } = await import('../lib/db');

    // 获取查询参数
    const connectionId = c.req.query('connectionId');
    const organizationId = c.req.query('organizationId');
    const teamId = c.req.query('teamId');

    // 构建查询条件
    const where: any = {};

    if (connectionId) {
      where.client = { connectionId };
    } else if (organizationId) {
      where.client = { organizationId };
      if (teamId) {
        where.client.teamId = teamId;
      }
    }

    const accounts = await prisma.qianniuAccount.findMany({
      where,
      include: {
        client: {
          select: {
            id: true,
            name: true,
            connectionId: true,
            isOnline: true,
            organizationId: true,
            teamId: true,
          },
        },
        brand: {
          select: {
            id: true,
            name: true,
            description: true,
          },
        },
        _count: {
          select: {
            customers: true,
            conversations: true,
          },
        },
      },
      orderBy: {
        updatedAt: 'desc',
      },
    });

    const formattedAccounts = accounts.map((account) => ({
      id: account.id,
      accountName: account.accountName,
      accountId: account.accountId,
      shopName: account.shopName,
      shopId: account.shopId,
      platformType: account.platformType,
      isActive: account.isActive,
      isLoggedIn: account.isLoggedIn,
      lastLoginAt: account.lastLoginAt,
      createdAt: account.createdAt,
      updatedAt: account.updatedAt,
      client: account.client,
      brand: account.brand,
      customerCount: account._count.customers,
      conversationCount: account._count.conversations,
    }));

    return c.json(createResponse(formattedAccounts, '千牛账号列表'));
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * GET /qianniu-api/customers - 获取连接对应的客户列表
 */
app.get('/customers', async (c) => {
  try {
    const { prisma } = await import('../lib/db');

    // 获取查询参数
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const search = c.req.query('search');
    const connectionId = c.req.query('connectionId');

    const skip = (page - 1) * limit;

    // 构建查询条件
    const where: any = {};

    // 如果提供了连接ID，通过连接ID找到对应的千牛客户端下的所有客户
    if (connectionId) {
      where.qianniuAccount = {
        client: {
          connectionId: connectionId,
        },
      };
    } else {
      // 如果没有指定连接ID，返回错误
      return c.json(createErrorResponse('请指定connectionId参数'), 400);
    }

    // 搜索条件
    if (search) {
      where.OR = [
        { nickname: { contains: search, mode: 'insensitive' } },
        { realName: { contains: search, mode: 'insensitive' } },
        { phone: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { platformCustomerId: { contains: search, mode: 'insensitive' } },
      ];
    }

    // 查询客户列表
    const [customers, total] = await Promise.all([
      prisma.customer.findMany({
        where,
        include: {
          qianniuAccount: {
            include: {
              client: {
                select: {
                  name: true,
                  organizationId: true,
                  teamId: true,
                },
              },
              brand: {
                select: {
                  name: true,
                },
              },
            },
          },
        },
        orderBy: {
          lastActiveAt: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.customer.count({ where }),
    ]);

    // 格式化返回数据
    const formattedCustomers = customers.map((customer) => ({
      id: customer.id,
      platformCustomerId: customer.platformCustomerId,
      nickname: customer.nickname,
      avatar: customer.avatar,
      phone: customer.phone,
      email: customer.email,
      realName: customer.realName,
      gender: customer.gender,
      location: customer.location,
      vipLevel: customer.vipLevel,
      tags: customer.tags,
      notes: customer.notes,
      lastActiveAt: customer.lastActiveAt,
      totalOrders: customer.totalOrders,
      totalAmount: customer.totalAmount,
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt,
      // 关联信息
      qianniuAccount: {
        accountName: customer.qianniuAccount.accountName,
        shopName: customer.qianniuAccount.shopName,
        platformType: customer.qianniuAccount.platformType,
        client: customer.qianniuAccount.client,
        brand: customer.qianniuAccount.brand,
      },
    }));

    const result = {
      customers: formattedCustomers,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1,
      },
    };

    return c.json(createResponse(result, '客户列表获取成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * GET /qianniu-api/customers/:id - 获取单个客户详情
 */
app.get('/customers/:id', async (c) => {
  try {
    const { prisma } = await import('../lib/db');
    const customerId = c.req.param('id');

    const customer = await prisma.customer.findUnique({
      where: { id: customerId },
      include: {
        qianniuAccount: {
          include: {
            client: {
              select: {
                name: true,
                organizationId: true,
                teamId: true,
                isOnline: true,
                lastOnlineAt: true,
              },
            },
            brand: {
              select: {
                name: true,
                description: true,
              },
            },
          },
        },
      },
    });

    if (!customer) {
      return c.json(createErrorResponse('客户不存在'), 400);
    }

    // 格式化返回数据
    const formattedCustomer = {
      id: customer.id,
      platformCustomerId: customer.platformCustomerId,
      nickname: customer.nickname,
      avatar: customer.avatar,
      phone: customer.phone,
      email: customer.email,
      realName: customer.realName,
      gender: customer.gender,
      location: customer.location,
      vipLevel: customer.vipLevel,
      tags: customer.tags,
      notes: customer.notes,
      lastActiveAt: customer.lastActiveAt,
      totalOrders: customer.totalOrders,
      totalAmount: customer.totalAmount,
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt,
      // 关联信息
      qianniuAccount: {
        accountName: customer.qianniuAccount.accountName,
        shopName: customer.qianniuAccount.shopName,
        platformType: customer.qianniuAccount.platformType,
        client: customer.qianniuAccount.client,
        brand: customer.qianniuAccount.brand,
      },
    };

    return c.json(createResponse(formattedCustomer, '客户详情获取成功'));
  } catch (error) {
    return handleError(c, error);
  }
});

// 通用API调用处理函数
const handleApiCall = async (c: any, apiType: string, requiredParams: string[] = []) => {
  try {
    const body = await c.req.json();
    const { connectionId, ...params } = body;

    // 验证必需参数
    for (const param of requiredParams) {
      if (!(param in params)) {
        return c.json(createErrorResponse(`缺少必需参数: ${param}`), 400);
      }
    }

    // 验证连接ID
    if (!connectionId) {
      return c.json(createErrorResponse('缺少connectionId参数'), 400);
    }

    const connection = platformWsManager.getConnection(connectionId);
    if (!connection) {
      return c.json(createErrorResponse(`连接不存在: ${connectionId}`), 400);
    }

    // 构建API调用请求
    const request: ApiCallRequest = {
      requestId: `test_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      apiType,
      params,
      connectionId,
      organizationId: connection.organizationId,
      teamId: connection.teamId,
    };

    logger.info('处理千牛API请求', {
      apiType,
      connectionId,
      requestId: request.requestId,
      params,
    });

    // 调用API
    const response = await qianniuApiWebSocketService.handleApiCall(connectionId, request);

    return c.json(createResponse(response, `${apiType} API调用完成`));
  } catch (error) {
    return handleError(c, error);
  }
};

// 支持customerId自动获取encryptId的API调用处理函数
const handleApiCallWithCustomerId = async (c: any, apiType: string, requiredParams: string[] = []) => {
  try {
    const body = await c.req.json();
    const { connectionId, customerId, ...params } = body;

    // 验证必需参数
    if (!connectionId) {
      return c.json(createErrorResponse('缺少connectionId参数'), 400);
    }

    if (!customerId) {
      return c.json(createErrorResponse('缺少customerId参数'), 400);
    }

    // 自动获取encryptId
    const encryptId = await getCustomerEncryptId(customerId, connectionId);
    if (!encryptId) {
      return c.json(createErrorResponse('无法获取客户的encryptId'), 400);
    }

    // 将encryptId添加到参数中
    params.encryptId = encryptId;

    // 验证其他必需参数
    for (const param of requiredParams) {
      if (!(param in params)) {
        return c.json(createErrorResponse(`缺少必需参数: ${param}`), 400);
      }
    }

    const connection = platformWsManager.getConnection(connectionId);
    if (!connection) {
      return c.json(createErrorResponse(`连接不存在: ${connectionId}`), 400);
    }

    // 构建API调用请求
    const request: ApiCallRequest = {
      requestId: `test_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      apiType,
      params,
      connectionId,
      organizationId: connection.organizationId,
      teamId: connection.teamId,
    };

    logger.info('处理千牛API请求（使用customerId）', {
      apiType,
      connectionId,
      customerId,
      encryptId,
      requestId: request.requestId,
      params,
    });

    // 调用API
    const response = await qianniuApiWebSocketService.handleApiCall(connectionId, request);

    return c.json(createResponse(response, `${apiType} API调用完成`));
  } catch (error) {
    return handleError(c, error);
  }
};

/**
 * POST /qianniu-api/invite-order - 邀请下单（使用customerId）
 */
app.post('/invite-order', async (c) => {
  return handleApiCallWithCustomerId(c, 'inviteOrder', ['itemProps']);
});

/**
 * POST /qianniu-api/query-recent-orders - 查询近三个月订单（使用customerId）
 */
app.post('/query-recent-orders', async (c) => {
  try {
    const body = await c.req.json();
    const { connectionId, customerId, ...params } = body;

    if (!connectionId || !customerId) {
      return c.json(createErrorResponse('缺少必需参数: connectionId, customerId'), 400);
    }

    // 自动获取encryptId
    const encryptId = await getCustomerEncryptId(customerId, connectionId);
    if (!encryptId) {
      return c.json(createErrorResponse('无法获取客户的encryptId'), 400);
    }

    // 将encryptId设置为securityBuyerUid
    params.securityBuyerUid = encryptId;

    const connection = platformWsManager.getConnection(connectionId);
    if (!connection) {
      return c.json(createErrorResponse(`连接不存在: ${connectionId}`), 400);
    }

    const request: ApiCallRequest = {
      requestId: `test_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      apiType: 'queryRecentOrders',
      params,
      connectionId,
      organizationId: connection.organizationId,
      teamId: connection.teamId,
    };

    const response = await qianniuApiWebSocketService.handleApiCall(connectionId, request);
    return c.json(createResponse(response, 'queryRecentOrders API调用完成'));
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * POST /qianniu-api/query-history-orders - 查询历史订单（使用customerId）
 */
app.post('/query-history-orders', async (c) => {
  try {
    const body = await c.req.json();
    const { connectionId, customerId, ...params } = body;

    if (!connectionId || !customerId) {
      return c.json(createErrorResponse('缺少必需参数: connectionId, customerId'), 400);
    }

    // 自动获取encryptId
    const encryptId = await getCustomerEncryptId(customerId, connectionId);
    if (!encryptId) {
      return c.json(createErrorResponse('无法获取客户的encryptId'), 400);
    }

    // 将encryptId设置为securityBuyerUid
    params.securityBuyerUid = encryptId;

    const connection = platformWsManager.getConnection(connectionId);
    if (!connection) {
      return c.json(createErrorResponse(`连接不存在: ${connectionId}`), 400);
    }

    const request: ApiCallRequest = {
      requestId: `test_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      apiType: 'queryHistoryOrders',
      params,
      connectionId,
      organizationId: connection.organizationId,
      teamId: connection.teamId,
    };

    const response = await qianniuApiWebSocketService.handleApiCall(connectionId, request);
    return c.json(createResponse(response, 'queryHistoryOrders API调用完成'));
  } catch (error) {
    return handleError(c, error);
  }
});

/**
 * POST /qianniu-api/send-item-card - 发送商品卡片（使用customerId）
 */
app.post('/send-item-card', async (c) => {
  return handleApiCallWithCustomerId(c, 'sendItemCard', ['batchItemIds']);
});

/**
 * POST /qianniu-api/query-order-logistics - 查询订单物流信息
 */
app.post('/query-order-logistics', async (c) => {
  return handleApiCall(c, 'queryOrderLogistics', ['bizOrderId']);
});

/**
 * POST /qianniu-api/query-item-record - 查询商品记录（使用customerId）
 */
app.post('/query-item-record', async (c) => {
  return handleApiCallWithCustomerId(c, 'queryItemRecord', []);
});

/**
 * POST /qianniu-api/query-customer-info - 查询客户信息（使用customerId）
 */
app.post('/query-customer-info', async (c) => {
  return handleApiCallWithCustomerId(c, 'queryCustomerInfo', []);
});

/**
 * POST /qianniu-api/query-shop-coupons - 查询店铺优惠券
 */
app.post('/query-shop-coupons', async (c) => {
  return handleApiCall(c, 'queryShopCoupons');
});

/**
 * POST /qianniu-api/send-coupon - 发送优惠券（使用customerId）
 */
app.post('/send-coupon', async (c) => {
  return handleApiCallWithCustomerId(c, 'sendCoupon', ['name', 'activityId', 'description']);
});

/**
 * POST /qianniu-api/search-buyer-id - 查询买家ID
 */
app.post('/search-buyer-id', async (c) => {
  return handleApiCall(c, 'searchBuyerId', ['searchQuery']);
});

/**
 * POST /qianniu-api/set-suspend - 挂起/取消挂起
 */
app.post('/set-suspend', async (c) => {
  return handleApiCall(c, 'setSuspend', ['accountId', 'isSuspend']);
});

/**
 * POST /qianniu-api/decrypt-order - 订单解密
 */
app.post('/decrypt-order', async (c) => {
  return handleApiCall(c, 'decryptOrder', ['tid']);
});

/**
 * POST /qianniu-api/search-shop-items - 获取店铺商品（使用customerId）
 */
app.post('/search-shop-items', async (c) => {
  return handleApiCallWithCustomerId(c, 'searchShopItems', []);
});

/**
 * POST /qianniu-api/get-shop-customer-service - 获取店铺客服
 */
app.post('/get-shop-customer-service', async (c) => {
  return handleApiCall(c, 'getShopCustomerService');
});

/**
 * POST /qianniu-api/get-dispatch-groups - 获取客服分组列表
 */
app.post('/get-dispatch-groups', async (c) => {
  return handleApiCall(c, 'getDispatchGroups');
});

/**
 * POST /qianniu-api/forward-to-person - 转接到个人
 */
app.post('/forward-to-person', async (c) => {
  return handleApiCall(c, 'forwardToPerson', ['buyerId', 'toId']);
});

/**
 * POST /qianniu-api/forward-to-group - 转接到分组
 */
app.post('/forward-to-group', async (c) => {
  return handleApiCall(c, 'forwardToGroup', ['buyerId', 'toId', 'groupId']);
});

/**
 * POST /qianniu-api/call-api - 通用API调用
 */
app.post('/call-api', async (c) => {
  return handleApiCall(c, 'callApi', ['method', 'param']);
});

export default app;
