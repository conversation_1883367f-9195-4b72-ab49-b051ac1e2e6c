/**
 * 千牛API WebSocket服务
 * 处理通过WebSocket发送的千牛API调用请求
 */

import { randomUUID } from 'crypto';

import { logger } from '../lib/logger';
import { platformWsManager } from '../lib/platform-websocket';
import { type QianniuApiParams, qianniuApiService } from './qianniuApiService';

/**
 * API调用请求接口
 */
export interface ApiCallRequest {
  requestId: string;
  apiType: string;
  params: Record<string, unknown>;
  connectionId?: string;
  organizationId?: string;
  teamId?: string;
}

/**
 * API调用响应接口
 */
export interface ApiCallResponse {
  requestId: string;
  success: boolean;
  data?: unknown;
  error?: string;
  method?: string;
}

/**
 * 千牛API WebSocket服务类
 */
export class QianniuApiWebSocketService {
  private pendingRequests = new Map<string, {
    resolve: (value: ApiCallResponse) => void;
    reject: (reason: Error) => void;
    timeout: NodeJS.Timeout;
  }>();

  /**
   * 处理千牛API调用请求
   * @param connectionId 连接ID
   * @param request API调用请求
   */
  async handleApiCall(connectionId: string, request: ApiCallRequest): Promise<ApiCallResponse> {
    const requestId = request.requestId || randomUUID();
    logger.info('处理千牛API调用请求', {
      requestId,
      connectionId,
      apiType: request.apiType,
      params: request.params,
    });

    try {
      // 根据API类型构建API参数
      const apiParams = await this.buildApiParams(request.apiType, request.params);
      if (!apiParams) {
        throw new Error(`不支持的API类型: ${request.apiType}`);
      }

      // 发送API调用请求到千牛客户端
      const response = await this.sendApiCallToClient(connectionId, requestId, apiParams);

      logger.info('千牛API调用成功', {
        requestId,
        connectionId,
        apiType: request.apiType,
        method: apiParams.method,
      });

      return response;
    } catch (error) {
      logger.error('千牛API调用失败', {
        requestId,
        connectionId,
        apiType: request.apiType,
        error: error instanceof Error ? error.message : String(error),
      }, error instanceof Error ? error : new Error(String(error)));

      return {
        requestId,
        success: false,
        error: error instanceof Error ? error.message : String(error),
        method: request.apiType,
      };
    }
  }

  /**
   * 根据API类型构建API参数
   */
  private async buildApiParams(apiType: string, params: Record<string, unknown>): Promise<QianniuApiParams | null> {
    switch (apiType) {
      case 'inviteOrder':
        return await qianniuApiService.inviteOrder({
          encryptId: params['encryptId'] as string,
          itemProps: params['itemProps'] as string,
          buyerNick: params['buyerNick'] as string,
          bizDomain: params['bizDomain'] as string,
          encrypType: params['encrypType'] as string,
        });

      case 'queryRecentOrders':
        return await qianniuApiService.queryRecentOrders({
          securityBuyerUid: params['securityBuyerUid'] as string,
          orderStatus: params['orderStatus'] as string,
        });

      case 'queryHistoryOrders':
        return await qianniuApiService.queryHistoryOrders({
          securityBuyerUid: params['securityBuyerUid'] as string,
          pageNum: params['pageNum'] as number,
          pageSize: params['pageSize'] as number,
        });

      case 'sendItemCard':
        return await qianniuApiService.sendItemCard({
          encryptId: params['encryptId'] as string,
          batchItemIds: params['batchItemIds'] as string,
          type: params['type'] as number,
        });

      case 'queryOrderLogistics':
        return await qianniuApiService.queryOrderLogistics({
          bizOrderId: params['bizOrderId'] as string,
        });

      case 'queryItemRecord':
        return await qianniuApiService.queryItemRecord({
          encryptId: params['encryptId'] as string,
        });

      case 'queryCustomerInfo':
        return await qianniuApiService.queryCustomerInfo({
          encryptId: params['encryptId'] as string,
        });

      case 'queryShopCoupons':
        return await qianniuApiService.queryShopCoupons();

      case 'sendCoupon':
        return await qianniuApiService.sendCoupon({
          name: params['name'] as string,
          activityId: params['activityId'] as string,
          description: params['description'] as string,
          encryptId: params['encryptId'] as string,
        });

      case 'searchBuyerId':
        return await qianniuApiService.searchBuyerId({
          searchQuery: params['searchQuery'] as string,
        });

      case 'setSuspend':
        return await qianniuApiService.setSuspend({
          accountId: params['accountId'] as number,
          isSuspend: params['isSuspend'] as boolean,
          source: params['source'] as number,
        });

      case 'decryptOrder':
        return await qianniuApiService.decryptOrder({
          tid: params['tid'] as string,
          bizType: params['bizType'] as string,
          queryByTid: params['queryByTid'] as boolean,
        });

      case 'searchShopItems':
        return await qianniuApiService.searchShopItems({
          pageSize: params['pageSize'] as number,
          pageNo: params['pageNo'] as number,
          keyWord: params['keyWord'] as string,
          sortKey: params['sortKey'] as string,
          desc: params['desc'] as boolean,
          type: params['type'] as number,
          queryGift: params['queryGift'] as boolean,
          encryptId: params['encryptId'] as string,
        });

      case 'getShopCustomerService':
        return await qianniuApiService.getShopCustomerService({
          pageSize: params['pageSize'] as number,
        });

      case 'getDispatchGroups':
        return await qianniuApiService.getDispatchGroups({
          loginDomain: params['loginDomain'] as string,
        });

      case 'forwardToPerson':
        return await qianniuApiService.forwardToPerson({
          buyerId: params['buyerId'] as number,
          toId: params['toId'] as number,
          reason: params['reason'] as string,
          appCid: params['appCid'] as string,
          buyerDomain: params['buyerDomain'] as string,
          loginDomain: params['loginDomain'] as string,
        });

      case 'forwardToGroup':
        return await qianniuApiService.forwardToGroup({
          buyerId: params['buyerId'] as number,
          toId: params['toId'] as number,
          groupId: params['groupId'] as number,
          reason: params['reason'] as string,
          appCid: params['appCid'] as string,
          forwardType: params['forwardType'] as number,
          charset: params['charset'] as string,
          exceptUsers: params['exceptUsers'] as string,
          buyerDomain: params['buyerDomain'] as string,
          loginDomain: params['loginDomain'] as string,
        });

      case 'callApi':
        return await qianniuApiService.callApi({
          method: params['method'] as string,
          param: params['param'] as Record<string, unknown>,
          httpMethod: params['httpMethod'] as string,
          version: params['version'] as string,
        });

      default:
        logger.warn('不支持的API类型', { apiType });
        return null;
    }
  }

  /**
   * 发送API调用请求到千牛客户端
   */
  private async sendApiCallToClient(connectionId: string, requestId: string, apiParams: QianniuApiParams): Promise<ApiCallResponse> {
    return new Promise((resolve, reject) => {
      // 设置超时
      const timeout = setTimeout(() => {
        this.pendingRequests.delete(requestId);
        reject(new Error('API调用超时'));
      }, 30000); // 30秒超时

      // 存储请求回调
      this.pendingRequests.set(requestId, {
        resolve,
        reject,
        timeout,
      });

      try {
        // 获取连接
        const connection = platformWsManager.getConnection(connectionId);
        if (!connection) {
          throw new Error(`连接不存在: ${connectionId}`);
        }

        // 发送API调用请求
        const message = {
          method: 'qianniu_api_call',
          requestId,
          apiParams,
        };

        connection.ws.send(JSON.stringify(message));
        logger.debug('已发送API调用请求到客户端', {
          connectionId,
          requestId,
          method: apiParams.method,
        });
      } catch (error) {
        clearTimeout(timeout);
        this.pendingRequests.delete(requestId);
        reject(error);
      }
    });
  }

  /**
   * 处理来自千牛客户端的API响应
   */
  handleApiResponse(data: { requestId: string; response: string }) {
    const { requestId, response } = data;
    const pendingRequest = this.pendingRequests.get(requestId);

    if (!pendingRequest) {
      logger.warn('收到未知请求的API响应', { requestId });
      return;
    }

    try {
      clearTimeout(pendingRequest.timeout);
      this.pendingRequests.delete(requestId);

      const parsedResponse = JSON.parse(response);
      logger.debug('收到千牛API响应', {
        requestId,
        success: parsedResponse.success !== false,
        method: parsedResponse.method,
      });

      pendingRequest.resolve({
        requestId,
        success: parsedResponse.success !== false,
        data: parsedResponse,
        method: parsedResponse.method,
      });
    } catch (error) {
      logger.error('解析API响应失败', {
        requestId,
        response,
        error: error instanceof Error ? error.message : String(error),
      });

      pendingRequest.reject(new Error('解析API响应失败'));
    }
  }

  /**
   * 清理超时的请求
   */
  cleanup() {
    const now = Date.now();
    for (const [requestId, request] of this.pendingRequests.entries()) {
      // 清理超过5分钟的请求
      if (now - parseInt(requestId.split('_')[1] || '0') > 5 * 60 * 1000) {
        clearTimeout(request.timeout);
        this.pendingRequests.delete(requestId);
        request.reject(new Error('请求已过期'));
      }
    }
  }
}

// 导出单例
export const qianniuApiWebSocketService = new QianniuApiWebSocketService();

// 定期清理超时请求
setInterval(() => {
  qianniuApiWebSocketService.cleanup();
}, 60000); // 每分钟清理一次
