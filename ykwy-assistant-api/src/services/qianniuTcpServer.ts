/**
 * @prettier
 */
import { EventEmitter } from 'events';
import * as net from 'net';

import { connectionTracker } from '../lib/connection-tracker';
import { prisma } from '../lib/db';
import { logger } from '../lib/logger';

/**
 * TCP消息接口 - 参考C#实现
 */
export interface TcpMessage {
  method: string;
  id?: string;
  text?: string;
  image?: string;
  video?: string;
  keep?: string;
  flash?: string;
  username?: string;
  success?: boolean;
  message?: string;
  [key: string]: unknown;
}

/**
 * 响应等待器 - 模拟C#的ManualResetEventSlim
 */
class ResponseWaiter {
  private resolveCallback: ((value: string) => void) | null = null;
  private rejectCallback: ((reason: Error) => void) | null = null;
  private timeout: NodeJS.Timeout | null = null;

  wait(timeoutMs: number = 5000): Promise<string> {
    return new Promise((resolve, reject) => {
      this.resolveCallback = resolve;
      this.rejectCallback = reject;

      this.timeout = setTimeout(() => {
        this.rejectCallback?.(new Error('Response timeout'));
        this.cleanup();
      }, timeoutMs);
    });
  }

  signal(response: string) {
    if (this.resolveCallback) {
      this.resolveCallback(response);
      this.cleanup();
    }
  }

  private cleanup() {
    if (this.timeout) {
      clearTimeout(this.timeout);
      this.timeout = null;
    }
    this.resolveCallback = null;
    this.rejectCallback = null;
  }
}

/**
 * 千牛TCP服务器
 * 完全参考C# TouchSocket实现
 */
export class QianniuTcpServer extends EventEmitter {
  private server: net.Server | null = null;
  private clients: Map<string, net.Socket> = new Map(); // clientId -> Socket
  private connectionIdToClientId: Map<string, string> = new Map(); // connectionId -> clientId
  private clientIdToConnectionId: Map<string, string> = new Map(); // clientId -> connectionId
  private port: number = Number(process.env['TCP_PORT']) || 9997; // 可配置端口，默认9997
  private requestWaitHandles: ResponseWaiter[] = [];
  private responses: string[] = [];

  constructor(port?: number) {
    super();
    if (port) {
      this.port = port;
    }
  }

  /**
   * 启动TCP服务器 - 参考C#的Setup().Start()
   */
  start(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.server = net.createServer((socket) => {
        this.handleClientConnecting(socket);
        this.handleClientConnected(socket);
        this.handleClientDisconnected(socket);
      });

      this.server.on('error', (error) => {
        logger.error('千牛TCP服务器错误', { port: this.port }, error);
        reject(error);
      });

      // 监听所有接口 - 支持IPv4和IPv6
      this.server.listen(this.port, () => {
        logger.info('千牛TCP服务器启动成功', { port: this.port });

        // 启动定期状态日志输出 (每30秒)
        setInterval(() => {
          this.logConnectionStatus();
        }, 30000);

        resolve();
      });
    });
  }

  /**
   * 停止TCP服务器
   */
  stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          logger.info('千牛TCP服务器已停止');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  /**
   * 处理客户端正在连接 - 参考C#的service.Connecting
   */
  private handleClientConnecting(socket: net.Socket) {
    logger.info('千牛TCP客户端正在连接', {
      remoteAddress: socket.remoteAddress,
      remotePort: socket.remotePort,
    });
  }

  /**
   * 处理客户端已连接 - 参考C#的service.Connected
   */
  private handleClientConnected(socket: net.Socket) {
    const clientId = `${socket.remoteAddress}:${socket.remotePort}`;
    logger.info('千牛TCP客户端已连接', { clientId });
    logger.info('千牛TCP连接总数更新', { totalConnections: this.clients.size + 1 });

    this.clients.set(clientId, socket);

    // 立即发送获取用户名请求 - 参考C#实现
    const payload = JSON.stringify({ method: 'getusername' });
    logger.debug('向千牛TCP客户端发送获取用户名请求', { clientId, payload });
    socket.write(payload + '\r\n\r\n');

    // 设置数据处理回调 - 参考C#的service.Received
    socket.on('data', (data) => {
      this.handleReceived(socket, clientId, data);
    });

    // 记录连接详细信息
    logger.debug('千牛TCP连接详情', {
      localAddress: socket.localAddress,
      localPort: socket.localPort,
      remoteAddress: socket.remoteAddress,
      remotePort: socket.remotePort,
    });
  }

  /**
   * 处理客户端断开连接 - 参考C#的service.Disconnected
   */
  private handleClientDisconnected(socket: net.Socket) {
    const clientId = `${socket.remoteAddress}:${socket.remotePort}`;

    socket.on('close', (hadError) => {
      logger.info('千牛TCP客户端断开连接', { clientId, hadError });
      logger.debug('断开前连接总数', { totalConnections: this.clients.size });
      this.cleanupConnection(clientId);
      logger.debug('断开后连接总数', { totalConnections: this.clients.size });
    });

    socket.on('error', (error) => {
      logger.error('千牛TCP客户端错误', { clientId }, error);

      // 记录网络错误的详细信息
      const errorDetails: Record<string, unknown> = {
        name: error.name,
        message: error.message,
        stack: error.stack,
      };

      // 添加网络相关的错误信息（如果存在）
      if ('code' in error) errorDetails['code'] = error.code;
      if ('errno' in error) errorDetails['errno'] = error.errno;
      if ('syscall' in error) errorDetails['syscall'] = error.syscall;
      if ('address' in error) errorDetails['address'] = error.address;
      if ('port' in error) errorDetails['port'] = error.port;

      logger.debug('千牛TCP错误详情', { clientId, errorDetails });
      this.cleanupConnection(clientId);
    });

    socket.on('timeout', () => {
      logger.warn('千牛TCP客户端连接超时', { clientId });
      socket.destroy();
    });
  }

  /**
   * 清理连接映射
   */
  private cleanupConnection(clientId: string) {
    logger.debug('开始清理千牛TCP连接', { clientId });

    // 记录清理前的状态
    const connectionId = this.clientIdToConnectionId.get(clientId);
    logger.debug('清理前状态', {
      clientsCount: this.clients.size,
      mappingsCount: this.connectionIdToClientId.size,
    });

    // 清理客户端连接
    const wasDeleted = this.clients.delete(clientId);
    logger.debug('千牛TCP客户端连接删除结果', { clientId, wasDeleted });

    // 清理连接ID映射
    if (connectionId) {
      const mappingDeleted1 = this.connectionIdToClientId.delete(connectionId);
      const mappingDeleted2 = this.clientIdToConnectionId.delete(clientId);
      logger.debug('清理千牛TCP连接映射', {
        clientId,
        connectionId,
        mappingDeleted: mappingDeleted1 && mappingDeleted2,
      });
    } else {
      logger.debug('该千牛TCP客户端没有关联的connectionId映射', { clientId });
    }

    // 记录清理后的状态
    logger.debug('清理后状态', {
      clientsCount: this.clients.size,
      mappingsCount: this.connectionIdToClientId.size,
    });

    // 列出剩余的连接
    if (this.clients.size > 0) {
      logger.debug('剩余活跃千牛TCP连接', {
        activeConnections: Array.from(this.clients.keys()),
      });
    }
  }

  /**
   * 处理接收到的数据 - 参考C#的service.Received回调
   */
  private handleReceived(_socket: net.Socket, clientId: string, data: Buffer) {
    try {
      const response = data.toString();
      const timestamp = new Date().toISOString();
      logger.debug('从千牛TCP客户端接收到数据', {
        clientId,
        dataLength: data.length,
        response,
        timestamp,
      });

      // 尝试解析响应，看是否包含用户名信息
      try {
        const messageData = JSON.parse(response);
        logger.debug('千牛TCP JSON解析成功', { clientId, messageData });

        // 处理getusername响应 - 通过用户名查找对应的connectionId
        if (messageData.username || messageData.nick || messageData.loginID) {
          logger.debug('检测到千牛用户名响应，开始处理用户信息', { clientId });
          this.handleUsernameResponse(clientId, messageData);
          return;
        }

        // 检查是否是其他类型的响应
        if (messageData.success !== undefined || messageData.message !== undefined) {
          logger.debug('收到千牛操作响应', { clientId, messageData });
        }
      } catch (parseError) {
        logger.debug('千牛TCP JSON解析失败', {
          clientId,
          error: parseError instanceof Error ? parseError.message : String(parseError),
        });
        // 如果不是JSON格式，可能是纯文本响应
        // 尝试作为用户名处理
        if (response.trim() && !response.includes('\r\n')) {
          logger.debug('尝试将纯文本响应作为用户名处理', {
            clientId,
            response: response.trim(),
          });
          this.handleUsernameResponse(clientId, { username: response.trim() });
          return;
        }
        logger.debug('收到非JSON且非用户名的响应', { clientId, response });
      }

      // 处理响应队列 - 参考C#的响应处理机制
      logger.debug('将响应添加到队列', {
        clientId,
        queueLength: this.responses.length,
      });
      this.responses.push(response);

      if (this.requestWaitHandles.length > 0) {
        logger.debug('处理等待中的请求', {
          clientId,
          waitingRequests: this.requestWaitHandles.length,
        });
        const waiter = this.requestWaitHandles.shift();
        waiter?.signal(response);
      } else {
        logger.debug('没有等待中的请求', { clientId });
      }
    } catch (error) {
      logger.error(
        '处理千牛TCP接收数据失败',
        {
          clientId,
          rawDataHex: data.toString('hex'),
        },
        error instanceof Error ? error : new Error(String(error)),
      );
    }
  }

  /**
   * 处理用户名响应，查找对应的connectionId
   */
  private async handleUsernameResponse(clientId: string, userData: Record<string, unknown>) {
    try {
      logger.debug('处理千牛用户名响应', { clientId, userData });

      // 从响应中提取可能的标识信息
      const username = userData['username'] || userData['nick'] || userData['loginID'];
      const loginID = userData['loginID'];

      if (!username && !loginID) {
        logger.warn('用户名响应中没有有效的标识信息', { clientId });
        return;
      }

      // 查找对应的connectionId
      const connectionId = await this.findConnectionIdByUserInfo(username as string, loginID as string);
      if (connectionId) {
        this.registerConnection(clientId, connectionId);
        logger.info('通过用户信息找到connectionId', {
          clientId,
          connectionId,
          username: username as string,
          loginID: loginID as string,
        });

        // 记录TCP连接成功追踪
        connectionTracker.trackTcpConnection(clientId, username as string, loginID as string, connectionId);
      } else {
        logger.warn('未找到用户对应的connectionId', {
          clientId,
          username: username as string,
          loginID: loginID as string,
        });

        // 记录TCP连接匹配失败追踪
        const onlineClients = await prisma.qianniuClient.findMany({
          where: { isOnline: true },
          select: { connectionId: true },
        });

        connectionTracker.trackTcpMatchingFailure(clientId, username as string, loginID as string, {
          onlineClientsCount: onlineClients.length,
          accountsChecked: 0, // 这里可以根据实际查询次数调整
          availableConnectionIds: onlineClients.map((c) => c.connectionId).filter(Boolean) as string[],
        });
      }
    } catch (error) {
      logger.error('处理用户名响应失败', { clientId }, error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * 根据用户信息查找对应的connectionId
   * 这是关键方法：通过千牛用户信息找到对应的WebSocket connectionId
   */
  private async findConnectionIdByUserInfo(username?: string, loginID?: string): Promise<string | null> {
    try {
      if (!username && !loginID) {
        return null;
      }

      logger.debug('开始查找connectionId', { username, loginID });

      // 优先方案: 通过loginID(accountId)查找 - 更可靠，唯一性更强
      if (loginID) {
        logger.debug('尝试通过loginID查找', { loginID });
        const account = await prisma.qianniuAccount.findFirst({
          where: {
            accountId: loginID,
          },
          include: {
            client: {
              select: {
                id: true,
                connectionId: true,
                isOnline: true,
                name: true,
              },
            },
          },
        });

        logger.debug(
          'loginID查找结果',
          account
            ? {
              accountName: account.accountName,
              clientId: account.client?.id,
              connectionId: account.client?.connectionId,
              isOnline: account.client?.isOnline,
            }
            : { result: 'null' },
        );

        if (account?.client?.connectionId) {
          logger.info('通过loginID找到connectionId', { loginID, connectionId: account.client.connectionId });
          return account.client.connectionId;
        }
      }

      // 备用方案: 通过username(accountName)查找 - 仅在loginID匹配失败时使用
      if (username) {
        logger.debug('尝试通过username查找', { username });
        const account = await prisma.qianniuAccount.findFirst({
          where: {
            accountName: username,
          },
          include: {
            client: {
              select: {
                id: true,
                connectionId: true,
                isOnline: true,
                name: true,
              },
            },
          },
        });

        logger.debug(
          'username查找结果',
          account
            ? {
              accountName: account.accountName,
              clientId: account.client?.id,
              connectionId: account.client?.connectionId,
              isOnline: account.client?.isOnline,
            }
            : { result: 'null' },
        );

        if (account?.client?.connectionId) {
          logger.info('通过username找到connectionId', { username, connectionId: account.client.connectionId });
          return account.client.connectionId;
        }
      }

      // 新增方案: 直接查找在线的千牛客户端（当账号关联失败时）
      logger.debug('账号关联查找失败，尝试直接查找在线客户端');
      const onlineClients = await prisma.qianniuClient.findMany({
        where: {
          isOnline: true,
          connectionId: {
            not: '',
          },
        },
        select: {
          id: true,
          name: true,
          connectionId: true,
          accounts: {
            select: {
              accountName: true,
              accountId: true,
            },
          },
        },
      });

      logger.debug('找到在线客户端', {
        count: onlineClients.length,
        clients: onlineClients.map((client, index) => ({
          index: index + 1,
          name: client.name,
          connectionId: client.connectionId,
          accounts: client.accounts.map((account, i) => ({
            index: i + 1,
            accountName: account.accountName,
            accountId: account.accountId,
          })),
        })),
      });

      // 如果只有一个在线客户端，直接使用它
      if (onlineClients.length === 1 && onlineClients[0]?.connectionId) {
        logger.info('只有一个在线客户端，使用其connectionId', {
          connectionId: onlineClients[0].connectionId,
          clientName: onlineClients[0].name,
        });
        return onlineClients[0].connectionId;
      }

      // 如果有多个在线客户端，尝试通过账号信息匹配
      if (onlineClients.length > 1) {
        for (const client of onlineClients) {
          const matchingAccount = client.accounts.find((account) => (loginID && account.accountId === loginID) || (username && account.accountName === username));

          if (matchingAccount) {
            logger.info('通过账号匹配找到客户端', {
              clientName: client.name,
              connectionId: client.connectionId,
              matchingAccount: matchingAccount.accountName,
            });
            return client.connectionId;
          }
        }
      }

      logger.warn('无法找到匹配的connectionId', {
        username,
        loginID,
        onlineClientsCount: onlineClients.length,
      });
      return null;
    } catch (error) {
      logger.error('查找connectionId失败', { username, loginID }, error instanceof Error ? error : new Error(String(error)));
      return null;
    }
  }

  /**
   * 发送图片消息 - 参考C#的SendImage方法
   */
  async sendImage(connectionId: string, targetId: string, imagePath: string, keep: string = 'off'): Promise<boolean> {
    try {
      const socket = this.getSocketByConnectionId(connectionId);
      if (!socket) {
        logger.warn('未找到connectionId对应的连接', { connectionId, method: 'sendImage' });
        return false;
      }

      const payload = JSON.stringify({
        method: 'sendimage',
        id: targetId,
        image: imagePath,
        keep: keep,
      });

      socket.write(payload + '\r\n\r\n');
      logger.info('发送图片消息', { connectionId, targetId, imagePath });
      return true;
    } catch (error) {
      logger.error('发送图片消息失败', { connectionId, targetId, imagePath }, error instanceof Error ? error : new Error(String(error)));
      return false;
    }
  }

  /**
   * 发送视频消息 - 参考C#的SendVideo方法
   */
  async sendVideo(connectionId: string, targetId: string, videoPath: string, keep: string = 'off'): Promise<boolean> {
    try {
      const socket = this.getSocketByConnectionId(connectionId);
      if (!socket) {
        logger.warn('未找到connectionId对应的连接', { connectionId, method: 'sendVideo' });
        return false;
      }

      const payload = JSON.stringify({
        method: 'sendvideo',
        id: targetId,
        video: videoPath,
        keep: keep,
      });

      socket.write(payload + '\r\n\r\n');
      logger.info('发送视频消息', { connectionId, targetId, videoPath });
      return true;
    } catch (error) {
      logger.error('发送视频消息失败', { connectionId, targetId, videoPath }, error instanceof Error ? error : new Error(String(error)));
      return false;
    }
  }

  /**
   * 设置高亮提醒 - 参考C#的SetLight方法
   */
  async setHighlight(connectionId: string, targetId: string, flash: string = 'close'): Promise<boolean> {
    try {
      const socket = this.getSocketByConnectionId(connectionId);
      if (!socket) {
        logger.warn('未找到connectionId对应的连接', { connectionId, method: 'setHighlight' });
        return false;
      }

      const payload = JSON.stringify({
        method: 'highlight',
        id: targetId,
        flash: flash,
      });

      socket.write(payload + '\r\n\r\n');
      logger.info('设置高亮提醒', { connectionId, targetId, flash });
      return true;
    } catch (error) {
      logger.error('设置高亮提醒失败', { connectionId, targetId, flash }, error instanceof Error ? error : new Error(String(error)));
      return false;
    }
  }

  /**
   * 关闭会话 - 参考C#的CloseTalker方法
   */
  async closeTalker(connectionId: string, targetId: string): Promise<boolean> {
    try {
      const socket = this.getSocketByConnectionId(connectionId);
      if (!socket) {
        logger.warn('未找到connectionId对应的连接', { connectionId, method: 'closeTalker' });
        return false;
      }

      const payload = JSON.stringify({
        method: 'closetalker',
        id: targetId,
      });

      socket.write(payload + '\r\n\r\n');
      logger.info('关闭会话', { connectionId, targetId });
      return true;
    } catch (error) {
      logger.error('关闭会话失败', { connectionId, targetId }, error instanceof Error ? error : new Error(String(error)));
      return false;
    }
  }

  /**
   * 发送文本消息到指定客户 - 公共API（支持多段消息分割发送）
   */
  async sendTextMessage(connectionId: string, targetId: string, text: string, keep: string = 'on'): Promise<boolean> {
    const timestamp = new Date().toISOString();
    logger.info('开始发送千牛文本消息', {
      connectionId,
      targetId,
      text,
      keep,
      timestamp,
    });

    try {
      // 查找对应的Socket连接
      logger.debug('查找connectionId对应的Socket连接', { connectionId });
      const socket = this.getSocketByConnectionId(connectionId);
      if (!socket) {
        logger.warn('未找到connectionId对应的连接', {
          connectionId,
          availableMappings: Array.from(this.connectionIdToClientId.entries()),
        });
        return false;
      }

      const clientId = this.connectionIdToClientId.get(connectionId);
      logger.debug('找到对应的TCP客户端', { connectionId, clientId });

      // 🔧 消息拦截器：检查是否为多段消息
      const messageSegments = this.splitMessageByNewlines(text);

      if (messageSegments.length > 1) {
        logger.info('检测到多段消息，开始分段发送', {
          connectionId,
          targetId,
          totalSegments: messageSegments.length,
          segments: messageSegments.map((seg, idx) => `[${idx + 1}] ${seg.substring(0, 50)}${seg.length > 50 ? '...' : ''}`),
        });

        // 分段发送消息
        return await this.sendMessageSegments(socket, targetId, messageSegments, keep, connectionId, clientId);
      } else {
        // 单段消息，直接发送
        return await this.sendSingleMessage(socket, targetId, text, keep, connectionId, clientId);
      }
    } catch (error) {
      logger.error('发送文本消息失败', { connectionId, targetId, text }, error instanceof Error ? error : new Error(String(error)));
      return false;
    }
  }

  /**
   * 按换行符分割消息
   */
  private splitMessageByNewlines(text: string): string[] {
    // 按换行符分割，过滤空行
    const segments = text
      .split('\n')
      .map((segment) => segment.trim())
      .filter((segment) => segment.length > 0);

    logger.debug('消息分割结果', {
      originalLength: text.length,
      segmentCount: segments.length,
      segments: segments.map((seg, idx) => `[${idx + 1}] ${seg.substring(0, 100)}${seg.length > 100 ? '...' : ''}`),
    });

    return segments;
  }

  /**
   * 分段发送消息（带随机延迟）
   */
  private async sendMessageSegments(
    socket: net.Socket,
    targetId: string,
    segments: string[],
    keep: string,
    connectionId: string,
    clientId: string | undefined,
  ): Promise<boolean> {
    let allSuccess = true;

    for (let i = 0; i < segments.length; i++) {
      const segment = segments[i];
      if (!segment) {
        logger.warn('跳过空的消息段落', { connectionId, targetId, segmentIndex: i + 1 });
        continue;
      }

      const segmentInfo = `[${i + 1}/${segments.length}]`;

      try {
        // 发送当前段落
        const success = await this.sendSingleMessage(socket, targetId, segment, keep, connectionId, clientId, segmentInfo);

        if (!success) {
          allSuccess = false;
          logger.error('分段消息发送失败', {
            connectionId,
            targetId,
            segmentIndex: i + 1,
            totalSegments: segments.length,
            segment: segment.substring(0, 100),
          });
        }

        // 如果不是最后一段，添加随机延迟
        if (i < segments.length - 1) {
          const delay = Math.floor(Math.random() * 2000) + 2000; // 2-4秒随机延迟
          logger.debug('分段消息延迟', {
            connectionId,
            targetId,
            segmentIndex: i + 1,
            nextSegmentIndex: i + 2,
            delayMs: delay,
          });

          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      } catch (error) {
        allSuccess = false;
        logger.error(
          '分段消息发送异常',
          {
            connectionId,
            targetId,
            segmentIndex: i + 1,
            segment: segment ? segment.substring(0, 100) : '(empty)',
          },
          error instanceof Error ? error : new Error(String(error)),
        );
      }
    }

    logger.info('分段消息发送完成', {
      connectionId,
      targetId,
      totalSegments: segments.length,
      allSuccess,
    });

    return allSuccess;
  }

  /**
   * 发送单条消息
   */
  private async sendSingleMessage(
    socket: net.Socket,
    targetId: string,
    text: string,
    keep: string,
    connectionId: string,
    clientId: string | undefined,
    segmentInfo?: string,
  ): Promise<boolean> {
    try {
      const payload = JSON.stringify({
        method: 'sendtext',
        id: targetId,
        text: text,
        keep: keep,
      });

      logger.debug('准备发送千牛JSON payload', {
        connectionId,
        payload,
        segmentInfo,
      });

      const fullMessage = payload + '\r\n\r\n';
      logger.debug('千牛完整消息（包含终止符）', {
        connectionId,
        fullMessage: JSON.stringify(fullMessage),
        segmentInfo,
      });

      socket.write(fullMessage);
      logger.info('千牛消息已写入Socket', {
        clientId,
        targetId,
        textLength: text.length,
        segmentInfo,
      });

      return true;
    } catch (error) {
      logger.error(
        '发送单条消息失败',
        {
          connectionId,
          targetId,
          text: text.substring(0, 100),
          segmentInfo,
        },
        error instanceof Error ? error : new Error(String(error)),
      );
      return false;
    }
  }

  /**
   * 注册千牛客户端连接ID
   * @param clientId TCP客户端ID (IP:Port)
   * @param connectionId 千牛业务连接ID
   */
  registerConnection(clientId: string, connectionId: string) {
    logger.debug('开始注册连接映射', { connectionId, clientId });

    // 检查是否已存在映射
    const existingClientId = this.connectionIdToClientId.get(connectionId);
    const existingConnectionId = this.clientIdToConnectionId.get(clientId);

    if (existingClientId && existingClientId !== clientId) {
      logger.warn('connectionId已映射到其他客户端，将覆盖', {
        connectionId,
        existingClientId,
        newClientId: clientId,
      });
    }

    if (existingConnectionId && existingConnectionId !== connectionId) {
      logger.warn('clientId已映射到其他连接，将覆盖', {
        clientId,
        existingConnectionId,
        newConnectionId: connectionId,
      });
    }

    this.connectionIdToClientId.set(connectionId, clientId);
    this.clientIdToConnectionId.set(clientId, connectionId);

    logger.info('连接映射注册成功', {
      connectionId,
      clientId,
      totalMappings: this.connectionIdToClientId.size,
    });

    // 列出所有当前映射
    const allMappings = Array.from(this.connectionIdToClientId.entries());
    logger.debug('所有活跃映射', { mappings: allMappings });
  }

  /**
   * 手动注册连接映射 - 供外部调用
   * 当我们知道某个connectionId对应哪个TCP连接时使用
   */
  manualRegisterConnection(connectionId: string, tcpClientId?: string): boolean {
    logger.info('开始手动注册连接映射', { connectionId, tcpClientId: tcpClientId || '未指定' });

    let finalTcpClientId = tcpClientId;

    // 如果没有指定TCP客户端ID，尝试使用第一个可用的
    if (!finalTcpClientId) {
      const availableClients = Array.from(this.clients.keys());
      logger.debug('未指定TCP客户端ID，当前可用客户端', { availableClients });

      if (availableClients.length === 1) {
        finalTcpClientId = availableClients[0];
        logger.info('自动选择唯一可用客户端', { finalTcpClientId });
      } else if (availableClients.length === 0) {
        logger.warn('没有可用的TCP连接');
        return false;
      } else {
        logger.warn('有多个TCP连接，无法自动选择', { availableClients });
        return false;
      }
    }

    // 检查TCP客户端是否存在
    if (!finalTcpClientId || !this.clients.has(finalTcpClientId)) {
      logger.warn('TCP客户端不存在', {
        finalTcpClientId,
        availableClients: Array.from(this.clients.keys()),
      });
      return false;
    }

    logger.debug('准备注册映射', { connectionId, finalTcpClientId });
    this.registerConnection(finalTcpClientId, connectionId);
    logger.info('手动注册连接映射成功', { connectionId, finalTcpClientId });
    return true;
  }

  /**
   * 根据connectionId获取对应的Socket连接
   */
  private getSocketByConnectionId(connectionId: string): net.Socket | null {
    const clientId = this.connectionIdToClientId.get(connectionId);
    if (!clientId) {
      logger.warn('未找到connectionId对应的clientId', { connectionId });
      return null;
    }

    const socket = this.clients.get(clientId);
    if (!socket) {
      logger.warn('未找到clientId对应的socket', { clientId });
      return null;
    }

    return socket;
  }

  /**
   * 获取所有TCP连接信息
   */
  getConnectionsInfo() {
    const info = {
      tcpConnections: Array.from(this.clients.keys()),
      connectionMappings: Object.fromEntries(this.connectionIdToClientId),
      totalConnections: this.clients.size,
    };

    logger.debug('获取连接信息', info);
    return info;
  }

  /**
   * 检查connectionId是否有对应的TCP连接
   */
  hasConnectionMapping(connectionId: string): boolean {
    const hasMapping = this.connectionIdToClientId.has(connectionId);
    logger.debug('检查连接映射', { connectionId, hasMapping });
    return hasMapping;
  }

  /**
   * 输出详细的连接状态日志 - 用于调试
   */
  logConnectionStatus() {
    const timestamp = new Date().toISOString();

    // TCP连接状态
    const tcpConnections = Array.from(this.clients.entries()).map(([clientId, socket]) => ({
      clientId,
      status: socket.destroyed ? '已断开' : '活跃',
    }));

    // 连接映射状态
    const connectionMappings = Array.from(this.connectionIdToClientId.entries()).map(([connectionId, clientId]) => ({
      connectionId,
      clientId,
      socketExists: this.clients.has(clientId),
    }));

    logger.info('千牛TCP连接状态报告', {
      timestamp,
      tcpConnections: {
        total: this.clients.size,
        connections: tcpConnections,
      },
      connectionMappings: {
        total: this.connectionIdToClientId.size,
        mappings: connectionMappings,
      },
      responseQueue: {
        responseQueueLength: this.responses.length,
        waitingRequestsCount: this.requestWaitHandles.length,
      },
    });
  }

  /**
   * 获取所有未映射的TCP连接
   */
  getUnmappedTcpConnections(): string[] {
    const allTcpClients = Array.from(this.clients.keys());
    const mappedTcpClients = Array.from(this.clientIdToConnectionId.keys());
    return allTcpClients.filter((clientId) => !mappedTcpClients.includes(clientId));
  }

  /**
   * 获取连接的客户端数量
   */
  getConnectedClientsCount(): number {
    return this.clients.size;
  }

  /**
   * 获取所有连接的客户端ID
   */
  getConnectedClients(): string[] {
    return Array.from(this.clients.keys());
  }

  /**
   * 获取所有已注册的连接ID
   */
  getRegisteredConnections(): string[] {
    return Array.from(this.connectionIdToClientId.keys());
  }

  /**
   * 检查指定connectionId是否在线
   */
  isConnectionOnline(connectionId: string): boolean {
    const clientId = this.connectionIdToClientId.get(connectionId);
    return clientId ? this.clients.has(clientId) : false;
  }

  /**
   * 获取连接状态信息
   */
  getConnectionStatus(): {
    totalClients: number;
    registeredConnections: number;
    connections: Array<{ clientId: string; connectionId?: string }>;
  } {
    const connections = Array.from(this.clients.keys()).map((clientId) => ({
      clientId,
      connectionId: this.clientIdToConnectionId.get(clientId),
    }));

    return {
      totalClients: this.clients.size,
      registeredConnections: this.connectionIdToClientId.size,
      connections,
    };
  }
}

// 创建并导出单例
export const qianniuTcpServer = new QianniuTcpServer();
