import { UserRole } from '@prisma/client';
import crypto from 'crypto';

import { prisma } from '../lib/db';

export interface CreateInvitationData {
  email: string;
  role?: string;
  organizationId: string;
  teamId?: string;
  inviterId: string;
}

export interface AcceptInvitationData {
  token: string;
  name: string;
  password: string;
}

export class OrganizationInvitationService {
  /**
   * 创建组织邀请
   */
  static async createInvitation(data: CreateInvitationData) {
    const { email, role = 'CUSTOMER_SERVICE', organizationId, teamId, inviterId } = data;

    // 检查用户是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      // 检查用户是否已属于组织
      const existingMember = await prisma.member.findFirst({
        where: { userId: existingUser.id },
      });

      if (existingMember) {
        throw new Error('用户已属于其他组织');
      }

      // 如果用户存在但未属于组织，直接添加到组织
      await prisma.user.update({
        where: { id: existingUser.id },
        data: {
          teamId,
          role: role as UserRole,
        },
      });

      // 创建 Member 记录
      await prisma.member.create({
        data: {
          organizationId,
          userId: existingUser.id,
          role: role,
        },
      });

      return {
        success: true,
        message: '用户已添加到组织',
        userId: existingUser.id,
      };
    }

    // 生成邀请令牌
    const token = crypto.randomBytes(32).toString('hex');
    const inviteCode = crypto.randomBytes(8).toString('hex').toUpperCase();

    // 创建邀请记录
    const invitation = await prisma.invitation.create({
      data: {
        organizationId,
        email,
        role,
        status: 'PENDING',
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天过期
        inviterId,
        teamId,
        token,
        inviteCode,
      },
      include: {
        organization: {
          select: { id: true, name: true },
        },
        team: {
          select: { id: true, name: true },
        },
      },
    });

    // 生成邀请链接
    const inviteUrl = `${Bun.env.FRONTEND_URL}/invite/${token}`;

    return {
      success: true,
      invitation,
      inviteUrl,
      inviteCode,
    };
  }

  /**
   * 接受邀请并注册用户
   */
  static async acceptInvitation(data: AcceptInvitationData) {
    const { token, name, password } = data;

    // 查找邀请
    const invitation = await prisma.invitation.findUnique({
      where: { token },
      include: {
        organization: true,
        team: true,
      },
    });

    if (!invitation) {
      throw new Error('邀请不存在或已失效');
    }

    if (invitation.status !== 'PENDING') {
      throw new Error('邀请已被使用或已过期');
    }

    if (invitation.expiresAt < new Date()) {
      throw new Error('邀请已过期');
    }

    // 检查邮箱是否已被注册
    const existingUser = await prisma.user.findUnique({
      where: { email: invitation.email },
    });

    if (existingUser) {
      throw new Error('该邮箱已被注册');
    }

    // 创建用户（使用 JWT 认证系统）
    const hashedPassword = await Bun.password.hash(password);

    const user = await prisma.user.create({
      data: {
        name,
        email: invitation.email,
        emailVerified: true, // 通过邀请注册的用户自动验证邮箱
        role: invitation.role as UserRole,
        teamId: invitation.teamId,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    // 创建账户记录
    await prisma.account.create({
      data: {
        userId: user.id,
        accountId: user.id,
        providerId: 'credential',
        password: hashedPassword,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    // 创建 Member 记录
    await prisma.member.create({
      data: {
        organizationId: invitation.organizationId,
        userId: user.id,
        role: invitation.role || UserRole.CUSTOMER_SERVICE,
      },
    });

    // 标记邀请为已使用
    await prisma.invitation.update({
      where: { id: invitation.id },
      data: {
        status: 'ACCEPTED',
      },
    });

    return {
      success: true,
      user: user,
      organization: invitation.organization,
      team: invitation.team,
    };
  }

  /**
   * 获取邀请详情
   */
  static async getInvitation(token: string) {
    const invitation = await prisma.invitation.findUnique({
      where: { token },
      include: {
        organization: {
          select: { id: true, name: true, description: true },
        },
        team: {
          select: { id: true, name: true },
        },
        inviter: {
          select: { id: true, name: true, email: true },
        },
      },
    });

    if (!invitation) {
      throw new Error('邀请不存在');
    }

    if (invitation.expiresAt < new Date()) {
      throw new Error('邀请已过期');
    }

    return invitation;
  }

  /**
   * 撤销邀请
   */
  static async revokeInvitation(invitationId: string, userId: string) {
    const invitation = await prisma.invitation.findUnique({
      where: { id: invitationId },
    });

    if (!invitation) {
      throw new Error('邀请不存在');
    }

    if (invitation.inviterId !== userId) {
      throw new Error('只有邀请人可以撤销邀请');
    }

    await prisma.invitation.update({
      where: { id: invitationId },
      data: {
        status: 'REVOKED',
      },
    });

    return { success: true };
  }

  /**
   * 获取组织的邀请列表
   */
  static async getOrganizationInvitations(organizationId: string) {
    return await prisma.invitation.findMany({
      where: { organizationId },
      include: {
        team: {
          select: { id: true, name: true },
        },
        inviter: {
          select: { id: true, name: true, email: true },
        },
      },
      orderBy: { id: 'desc' },
    });
  }
}
