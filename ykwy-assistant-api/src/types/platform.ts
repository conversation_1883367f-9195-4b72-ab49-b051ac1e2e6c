/**
 * 平台相关类型定义
 */

// 平台类型（目前只支持千牛）
export type PlatformType = 'QIANNIU';

// 注意：现在不再使用配置文件，WebSocket URL直接在CDN脚本中生成

// 平台适配器接口
export interface PlatformAdapter {
  transformInbound(platformData: Record<string, unknown>, rawMessage: Record<string, unknown>): StandardMessage | null;
  transformOutbound(platformData: Record<string, unknown>, message: Record<string, unknown>): Record<string, unknown> | null;
  validateConnection(connectionData: Record<string, unknown>): boolean;
}
// 原始消息接口（来自平台的消息）
export interface RawMessage {
  type?: string;
  response?: string;
  msgId?: string;
  id?: string;
  senderId?: string;
  from?: string;
  senderName?: string;
  nick?: string;
  content?: string;
  text?: string;
  message?: string;
  timestamp?: number;
  time?: number;
  metadata?: Record<string, unknown>;
  [key: string]: unknown;
}

// 千牛特定消息类型
export interface QianniuMessage extends RawMessage {
  type: 'receiveNewMsg' | 'onConversationChange' | 'onShopRobotReceriveNewMsgs' | 'heartbeat' | 'hi';
  response?: string;
}

// 平台消息发送格式（简化版，只支持execute方法）
export interface PlatformOutboundMessage {
  method: 'execute';
  expression: string;
}

// 消息类型（注意：发送时只支持text，但可以接收image和file）
export type MessageType = 'text' | 'image' | 'file' | 'system';
export type SenderType = 'customer' | 'service' | 'system';

// 标准化消息格式
export interface StandardMessage {
  // 基础信息
  id?: string;
  platformMessageId?: string;

  // 发送者信息
  senderId: string;
  senderName?: string;
  senderType: SenderType;

  // 接收者信息
  receiverId?: string;

  // 消息内容
  content: string;
  messageType: MessageType;

  // 平台信息
  platformId: string;
  platformType: string;
  organizationId: string;

  // 时间信息
  timestamp: Date;

  // 扩展信息
  metadata?: Record<string, unknown>;
}

// 消息投递状态
export type MessageDeliveryStatus = 'PENDING' | 'SENT' | 'DELIVERED' | 'FAILED';

// 数据库消息接口
export interface DatabaseMessage {
  id: string;
  conversationId: string;
  senderId?: string;
  senderType: SenderType;
  content: string;
  messageType: MessageType;
  metadata?: Record<string, unknown>;
  platformMessageId?: string;
  deliveryStatus: MessageDeliveryStatus;
  deliveredAt?: Date;
  failureReason?: string;
  sequenceNumber: number;
  isRecalled: boolean;
  recalledAt?: Date;
  recalledBy?: string;
  recallReason?: string;
  parentMessageId?: string;
  createdAt: Date;
  updatedAt: Date;
  conversation?: {
    id: string;
    qianniuAccount: {
      id: string;
      accountName: string;
      platformType: PlatformType;
    };
    customer: {
      id: string;
      name: string;
      platformCustomerId: string;
    };
  };
}

// ============ 千牛相关类型定义 ============

// 千牛客户端接口
export interface QianniuClient {
  id: string;
  organizationId: string;
  teamId: string;
  name: string;
  description?: string;
  connectionId?: string;
  isOnline: boolean;
  lastOnlineAt?: Date;
  clientInfo?: Record<string, unknown>;
  createdAt: Date;
  updatedAt: Date;
}

// 千牛账号接口
export interface QianniuAccount {
  id: string;
  clientId: string;
  brandId: string;
  accountName: string;
  accountId: string;
  shopName?: string;
  shopId?: string;
  platformType: PlatformType;
  isActive: boolean;
  isLoggedIn: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// 注意：WebSocket连接数据现在使用 UnifiedPlatformConnection 接口（在 platform-websocket.ts 中定义）
// 千牛特定的连接数据通过 platformData 字段传递
