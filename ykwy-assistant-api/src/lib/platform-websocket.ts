import type { ServerWebSocket } from 'bun';

import { notifyQianniuClientListChanged } from '../services/eventService';
import type { PlatformType } from '../types/platform';
import { prisma } from './db';
import { logger } from './logger';

/**
 * 统一的平台连接数据
 */
export interface UnifiedPlatformConnection {
  connectionId: string;
  platformType: PlatformType;
  organizationId: string;
  teamId: string;
  platformData: Record<string, unknown>;
  ws: ServerWebSocket<Record<string, unknown>>;
  connectedAt: Date;
  lastHeartbeat?: Date;
}

/**
 * 统一的平台WebSocket连接管理器
 */
class PlatformWebSocketManager {
  // 存储所有平台连接
  // Map<connectionId, UnifiedPlatformConnection>
  private connections = new Map<string, UnifiedPlatformConnection>();

  // 按平台类型索引连接
  // Map<platformType, Set<connectionId>>
  private connectionsByType = new Map<string, Set<string>>();

  // 按组织索引连接
  // Map<organizationId, Set<connectionId>>
  private connectionsByOrg = new Map<string, Set<string>>();

  // 按团队索引连接
  // Map<teamId, Set<connectionId>>
  private connectionsByTeam = new Map<string, Set<string>>();

  /**
   * 添加平台连接
   */
  addConnection(connection: UnifiedPlatformConnection) {
    const { connectionId, platformType, organizationId, teamId } = connection;

    // 存储连接
    this.connections.set(connectionId, connection);

    // 按平台类型索引
    if (!this.connectionsByType.has(platformType)) {
      this.connectionsByType.set(platformType, new Set());
    }
    this.connectionsByType.get(platformType)!.add(connectionId);

    // 按组织索引
    if (!this.connectionsByOrg.has(organizationId)) {
      this.connectionsByOrg.set(organizationId, new Set());
    }
    this.connectionsByOrg.get(organizationId)!.add(connectionId);

    // 按团队索引
    if (!this.connectionsByTeam.has(teamId)) {
      this.connectionsByTeam.set(teamId, new Set());
    }
    this.connectionsByTeam.get(teamId)!.add(connectionId);

    logger.info('平台WebSocket连接已添加', {
      platformType,
      connectionId,
      teamId,
      organizationId,
    });

    // 通知前端千牛客户端列表变化
    if (connection.platformData?.['clientId']) {
      notifyQianniuClientListChanged(organizationId);
    }
  }

  /**
   * 移除平台连接
   */
  async removeConnection(connectionId: string) {
    const connection = this.connections.get(connectionId);
    if (!connection) {
      logger.warn('平台WebSocket连接未找到', { connectionId });
      return;
    }

    // 从各种索引中移除
    this.connectionsByType.get(connection.platformType)?.delete(connectionId);
    this.connectionsByOrg.get(connection.organizationId)?.delete(connectionId);
    this.connectionsByTeam.get(connection.teamId)?.delete(connectionId);

    // 移除连接
    this.connections.delete(connectionId);

    logger.info('平台WebSocket连接已移除', {
      platformType: connection.platformType,
      connectionId,
    });

    // 如果是千牛连接，更新数据库中的客户端状态
    if (connection.platformType === 'QIANNIU' && connection.platformData?.['clientId']) {
      try {
        await prisma.qianniuClient.update({
          where: { id: connection.platformData['clientId'] as string },
          data: {
            isOnline: false,
            connectionId: undefined,
            lastOnlineAt: new Date(),
          },
        });
        logger.info('已更新千牛客户端离线状态', { clientId: connection.platformData['clientId'] });
      } catch (error) {
        logger.error('更新千牛客户端状态失败', { clientId: connection.platformData['clientId'] }, error instanceof Error ? error : new Error(String(error)));
      }
    }

    // 通知前端千牛客户端列表变化
    if (connection.platformData?.['clientId']) {
      notifyQianniuClientListChanged(connection.organizationId);
    }
  }

  /**
   * 获取连接
   */
  getConnection(connectionId: string): UnifiedPlatformConnection | undefined {
    return this.connections.get(connectionId);
  }

  /**
   * 获取团队的所有连接
   */
  getTeamConnections(teamId: string): UnifiedPlatformConnection[] {
    const connectionIds = this.connectionsByTeam.get(teamId) || new Set();
    return Array.from(connectionIds)
      .map((id) => this.connections.get(id))
      .filter((conn): conn is UnifiedPlatformConnection => conn !== undefined);
  }

  /**
   * 获取组织的所有连接
   */
  getOrgConnections(organizationId: string): UnifiedPlatformConnection[] {
    const connectionIds = this.connectionsByOrg.get(organizationId) || new Set();
    return Array.from(connectionIds)
      .map((id) => this.connections.get(id))
      .filter((conn): conn is UnifiedPlatformConnection => conn !== undefined);
  }

  /**
   * 获取平台类型的所有连接
   */
  getPlatformConnections(platformType: PlatformType): UnifiedPlatformConnection[] {
    const connectionIds = this.connectionsByType.get(platformType) || new Set();
    return Array.from(connectionIds)
      .map((id) => this.connections.get(id))
      .filter((conn): conn is UnifiedPlatformConnection => conn !== undefined);
  }

  /**
   * 向团队发送消息
   */
  sendToTeam(teamId: string, message: Record<string, unknown>) {
    const connections = this.getTeamConnections(teamId);
    for (const connection of connections) {
      try {
        connection.ws.send(JSON.stringify(message));
      } catch (error) {
        logger.error('向团队发送消息失败', { teamId }, error instanceof Error ? error : new Error(String(error)));
      }
    }
    logger.debug('已向团队发送消息', { teamId, connectionCount: connections.length });
  }

  /**
   * 向组织发送消息
   */
  sendToOrg(organizationId: string, message: Record<string, unknown>) {
    const connections = this.getOrgConnections(organizationId);
    for (const connection of connections) {
      try {
        connection.ws.send(JSON.stringify(message));
      } catch (error) {
        logger.error('向组织发送消息失败', { organizationId }, error instanceof Error ? error : new Error(String(error)));
      }
    }
    logger.debug('已向组织发送消息', { organizationId, connectionCount: connections.length });
  }

  /**
   * 更新心跳时间
   */
  updateHeartbeat(connectionId: string) {
    const connection = this.connections.get(connectionId);
    if (connection) {
      connection.lastHeartbeat = new Date();
    }
  }

  /**
   * 清理过期连接
   */
  async cleanupExpiredConnections(timeoutMs: number = 60000) {
    const now = new Date();
    const expiredConnections: string[] = [];

    for (const [connectionId, connection] of this.connections) {
      if (connection.lastHeartbeat) {
        const timeSinceHeartbeat = now.getTime() - connection.lastHeartbeat.getTime();
        if (timeSinceHeartbeat > timeoutMs) {
          expiredConnections.push(connectionId);
        }
      }
    }

    // 并行清理所有过期连接
    const cleanupPromises = expiredConnections.map(async (connectionId) => {
      logger.debug('清理过期的平台WebSocket连接', { connectionId });
      try {
        await this.removeConnection(connectionId);
      } catch (error) {
        logger.error('清理平台WebSocket连接失败', { connectionId }, error instanceof Error ? error : new Error(String(error)));
      }
    });

    await Promise.all(cleanupPromises);
    return expiredConnections.length;
  }

  /**
   * 获取连接统计信息
   */
  getStats() {
    return {
      totalConnections: this.connections.size,
      connectionsByType: Object.fromEntries(Array.from(this.connectionsByType.entries()).map(([type, connections]) => [type, connections.size])),
      connectionsByOrg: Object.fromEntries(Array.from(this.connectionsByOrg.entries()).map(([orgId, connections]) => [orgId, connections.size])),
      connectionsByTeam: Object.fromEntries(Array.from(this.connectionsByTeam.entries()).map(([teamId, connections]) => [teamId, connections.size])),
    };
  }

  /**
   * 获取所有连接
   */
  getAllConnections(): UnifiedPlatformConnection[] {
    return Array.from(this.connections.values());
  }
}

// 导出单例
export const platformWsManager = new PlatformWebSocketManager();
export default platformWsManager;
