import { PrismaClient } from '@prisma/client';

declare global {
  var __prisma: PrismaClient | undefined;
}

const prisma =
  globalThis.__prisma ||
  new PrismaClient({
    log: Bun.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
  });

if (Bun.env.NODE_ENV === 'development') {
  globalThis.__prisma = prisma;
}

export { prisma };

process.on('beforeExit', async () => {
  await prisma.$disconnect();
});
