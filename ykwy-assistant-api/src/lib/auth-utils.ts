import type { Context } from 'hono';

import { prisma } from './db';
import { extractTokenFromHeader, verifyToken } from './jwt';
import { logger } from './logger';

/**
 * 从请求中获取当前用户信息
 */
export async function getCurrentUser(c: Context) {
  try {
    // 首先尝试从上下文中获取用户信息（如果已经通过中间件验证）
    const contextUser = c.get('user');
    if (contextUser) {
      // 获取完整的用户信息
      const user = await prisma.user.findUnique({
        where: { id: contextUser.id },
        include: {
          members: {
            include: {
              organization: {
                select: { id: true, name: true },
              },
            },
          },
          team: {
            select: { id: true, name: true },
          },
        },
      });
      return user;
    }

    // 如果上下文中没有用户信息，尝试从请求头中验证 JWT
    const authHeader = c.req.header('authorization');
    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      return null;
    }

    const payload = await verifyToken(token);
    if (!payload || payload.type !== 'access') {
      return null;
    }

    // 获取完整的用户信息（包含组织和团队信息）
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      include: {
        members: {
          include: {
            organization: {
              select: { id: true, name: true },
            },
          },
        },
        team: {
          select: { id: true, name: true },
        },
      },
    });

    return user;
  } catch (error) {
    logger.error('获取当前用户失败', {}, error instanceof Error ? error : new Error(String(error)));
    return null;
  }
}

/**
 * 检查用户权限
 */
export function checkPermission(userRole: string, requiredRole: string): boolean {
  const roleHierarchy = {
    SYSTEM_ADMIN: 4,
    ORGANIZATION_ADMIN: 3,
    TEAM_MANAGER: 2,
    CUSTOMER_SERVICE: 1,
  };

  const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0;
  const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;

  return userLevel >= requiredLevel;
}

/**
 * 检查用户是否可以管理指定团队
 */
export async function canManageTeam(userId: string, teamId: string): Promise<boolean> {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      role: true,
      canManageTeams: true,
      managedTeams: true,
      teamId: true,
    },
  });

  if (!user) return false;

  // 系统管理员和组织管理员可以管理所有团队
  if (user.role === 'SYSTEM_ADMIN' || user.role === 'ORGANIZATION_ADMIN') {
    return true;
  }

  // 团队管理员只能管理自己管理的团队
  if (user.role === 'TEAM_MANAGER' && user.canManageTeams) {
    return user.managedTeams.includes(teamId);
  }

  return false;
}

/**
 * 获取用户可访问的组织ID
 */
export async function getUserOrganizationId(userId: string): Promise<string | null> {
  const member = await prisma.member.findFirst({
    where: { userId },
    select: { organizationId: true },
  });

  return member?.organizationId || null;
}

/**
 * 获取用户的组织信息（包含组织详情）
 */
export async function getUserOrganization(userId: string) {
  const member = await prisma.member.findFirst({
    where: { userId },
    include: {
      organization: true,
    },
  });

  return member?.organization || null;
}

/**
 * 检查用户是否属于指定组织
 */
export async function checkUserOrganization(userId: string, organizationId: string): Promise<boolean> {
  const userOrgId = await getUserOrganizationId(userId);
  return userOrgId === organizationId;
}

/**
 * 获取用户的团队ID
 */
export async function getUserTeamId(userId: string): Promise<string | null> {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { teamId: true },
  });

  return user?.teamId || null;
}

/**
 * 认证中间件 - 确保用户已登录
 */
export async function requireAuth(c: Context) {
  const user = await getCurrentUser(c);
  if (!user) {
    throw new Error('未登录');
  }
  return user;
}

/**
 * 组织权限中间件 - 确保用户属于组织
 */
export async function requireOrganization(c: Context) {
  const user = await requireAuth(c);
  const organizationId = await getUserOrganizationId(user.id);
  if (!organizationId) {
    throw new Error('缺少组织信息');
  }
  return { ...user, organizationId };
}

/**
 * 角色权限中间件 - 确保用户有足够权限
 */
export async function requireRole(c: Context, requiredRole: string) {
  const user = await requireAuth(c);
  if (!checkPermission(user.role, requiredRole)) {
    throw new Error('权限不足');
  }
  return user;
}
