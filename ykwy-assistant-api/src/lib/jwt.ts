import { sign, verify } from 'hono/jwt';

import { logger } from './logger';

// JWT 配置
const JWT_SECRET = Bun.env['JWT_SECRET'] || 'your-super-secret-jwt-key-at-least-32-characters-long';

// JWT Payload 接口
export interface UserJWTPayload {
  userId: string;
  email: string;
  role: string;
  organizationId?: string;
  teamId?: string;
  type: 'access' | 'refresh';
  exp?: number;
  iat?: number;
  [key: string]: any;
}

/**
 * 生成访问令牌
 */
export async function generateAccessToken(user: { id: string; email: string; role: string; organizationId?: string; teamId?: string }): Promise<string> {
  const payload: UserJWTPayload = {
    userId: user.id,
    email: user.email,
    role: user.role,
    organizationId: user.organizationId,
    teamId: user.teamId,
    type: 'access',
    exp: Math.floor(Date.now() / 1000) + 7 * 24 * 60 * 60, // 7天
    iat: Math.floor(Date.now() / 1000),
  };

  return await sign(payload, JWT_SECRET);
}

/**
 * 生成刷新令牌
 */
export async function generateRefreshToken(userId: string): Promise<string> {
  const payload: UserJWTPayload = {
    userId,
    email: '', // 刷新令牌不需要详细信息
    role: '',
    type: 'refresh',
    exp: Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60, // 30天
    iat: Math.floor(Date.now() / 1000),
  };

  return await sign(payload, JWT_SECRET);
}

/**
 * 验证 JWT 令牌
 */
export async function verifyToken(token: string): Promise<UserJWTPayload | null> {
  try {
    const payload = (await verify(token, JWT_SECRET)) as unknown as UserJWTPayload;

    // 检查令牌是否过期
    if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
      return null;
    }

    return payload;
  } catch (error) {
    logger.error('JWT验证失败', {}, error instanceof Error ? error : new Error(String(error)));
    return null;
  }
}

/**
 * 从 Authorization 头中提取令牌
 */
export function extractTokenFromHeader(authHeader: string | undefined): string | null {
  if (!authHeader) return null;

  const parts = authHeader.split(' ');
  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    return null;
  }

  return parts[1] || null;
}

/**
 * 生成令牌对（访问令牌 + 刷新令牌）
 */
export async function generateTokenPair(user: { id: string; email: string; role: string; organizationId?: string; teamId?: string }) {
  const [accessToken, refreshToken] = await Promise.all([generateAccessToken(user), generateRefreshToken(user.id)]);

  return {
    accessToken,
    refreshToken,
    expiresIn: 7 * 24 * 60 * 60, // 7天（秒）
    tokenType: 'Bearer',
  };
}
