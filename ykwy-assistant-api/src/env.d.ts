declare global {
  namespace NodeJS {
    interface ProcessEnv {
      // 服务器配置
      PORT?: string;
      HOST?: string;
      NODE_ENV?: 'development' | 'production' | 'test';

      // API 配置
      API_HOST?: string;
      API_PORT?: string;
      FRONTEND_URL?: string;
      YKWY_ASSISTANT_API_URL?: string;

      // 数据库
      DATABASE_URL: string;

      // JWT 认证
      JWT_SECRET: string;

      // CDN 配置
      CDN_BASE_URL?: string;

      // MinIO 配置
      MINIO_BUCKET_NAME?: string;
      MINIO_ACCESS_KEY?: string;
      MINIO_SECRET_KEY?: string;

      // RAGFlow 配置
      RAGFLOW_API_URL?: string;
      RAGFLOW_API_KEY?: string;

      // 千牛配置
      TCP_PORT?: string;
    }
  }
}

export {};
