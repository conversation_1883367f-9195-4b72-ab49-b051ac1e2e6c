name: 易康无忧-客服助手API接口

on:
  push:
    branches:
      - master
  workflow_dispatch:

jobs:
  Deployment:
    runs-on: ubuntu-latest
    steps:
      - name: Record start time
        run: echo "START_TIME=$(date +%s)" >> $GITHUB_ENV

      - name: Checkout the latest code
        uses: actions/checkout@v4

      - name: Generate .env file
        shell: bash
        run: |
          echo ${{secrets.ENV_CONTENT}} | tee .env

      - name: get-npm-version
        id: package-version
        uses: martinbeentjes/npm-get-version-action@v1.3.1
        with:
          path: .

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}

      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./Dockerfile
          platforms: linux/amd64
          push: ${{ github.event_name != 'pull_request' }}
          tags: |
            "${{ secrets.DOCKERHUB_USERNAME }}/${{ github.event.repository.name }}:latest"
            "${{ secrets.DOCKERHUB_USERNAME }}/${{ github.event.repository.name }}:${{ steps.package-version.outputs.current-version }}"

      - name: executing remote ssh commands using ssh key
        uses: appleboy/ssh-action@v1
        with:
          host: ${{ vars.REMOTE_HOST }}
          username: ${{ vars.REMOTE_USER }}
          key: ${{ secrets.REMOTE_SSH_KEY }}
          script: |
            cd ~/deploy/${{ github.event.repository.name }}
            bash up.sh

      - name: Get the output of duration
        run: |
          END_TIME=$(date +%s)
          DURATION=$((END_TIME - $START_TIME))
          echo "DURATION=$DURATION" >> $GITHUB_ENV

      - name: WeChat Work sent a failure notification
        if: failure()
        uses: chf007/action-wechat-work@master
        env:
          WECHAT_WORK_BOT_WEBHOOK: ${{ secrets.WECHAT_WORK_BOT_WEBHOOK }}
        with:
          msgtype: markdown
          content: |
            # 部署失败通知
            **仓库：** ${{ github.repository }}
            **工作流：** ${{ github.workflow }}
            **触发事件：** ${{ github.event_name }}
            [查看详情](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})

      - name: WeChat Work notification by markdown
        uses: chf007/action-wechat-work@master
        env:
          WECHAT_WORK_BOT_WEBHOOK: ${{secrets.WECHAT_WORK_BOT_WEBHOOK}}
        with:
          msgtype: markdown
          content: "${{github.actor}} 刚刚把 ${{github.event.repository.name}} 项目部署到 ${{vars.REMOTE_HOST}} 服务器，最新版本：v${{ steps.package-version.outputs.current-version }}，操作状态：${{job.status}}，耗时：${{env.DURATION}} 秒，请各位留意！\n > <@田坤> 请抽空 [code review](${{github.event.compare}})，演示地址：[${{github.event.repository.name}}.wuyoutansuo.com](${{github.event.repository.name}}.wuyoutansuo.com)"
