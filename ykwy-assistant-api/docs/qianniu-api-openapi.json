{"openapi": "3.0.3", "info": {"title": "千牛智能客服API", "description": "千牛智能客服API接口文档，支持订单、商品、用户、优惠券、客服操作等功能", "version": "1.1.0", "contact": {"name": "千牛API支持", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:3002/api/v1", "description": "本地开发环境"}, {"url": "https://ykwy-assistant-api.wuyoutansuo.com/api/v1", "description": "生产环境"}], "tags": [{"name": "订单管理", "description": "订单相关操作"}, {"name": "商品管理", "description": "商品相关操作"}, {"name": "用户管理", "description": "用户相关操作"}, {"name": "优惠券管理", "description": "优惠券相关操作"}, {"name": "客服操作", "description": "客服转接、挂起等操作"}, {"name": "客户ID接口", "description": "支持客户ID自动获取encryptId的接口"}, {"name": "通用接口", "description": "通用API调用接口"}], "paths": {"/qianniu-api/invite-order": {"post": {"tags": ["订单管理"], "summary": "邀请下单", "description": "向客户发送下单邀请", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["connectionId", "encryptId", "items"], "properties": {"connectionId": {"type": "string", "description": "千牛连接ID"}, "encryptId": {"type": "string", "description": "客户加密ID"}, "items": {"type": "string", "description": "商品信息JSON字符串"}, "changePrice": {"type": "boolean", "default": true, "description": "是否允许改价"}, "changeType": {"type": "integer", "default": 1, "description": "改价类型"}, "channelFee": {"type": "number", "default": 2.22, "description": "渠道费用"}}}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/qianniu-api/customer-info": {"post": {"tags": ["客户ID接口"], "summary": "通过客户ID查询客户信息", "description": "通过客户ID自动获取encryptId并查询客户信息", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["connectionId", "customerId"], "properties": {"connectionId": {"type": "string", "description": "千牛连接ID"}, "customerId": {"type": "string", "description": "客户ID"}}}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/qianniu-api/call-api": {"post": {"tags": ["通用接口"], "summary": "通用API调用", "description": "通用的千牛API调用接口", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["connectionId", "method", "param"], "properties": {"connectionId": {"type": "string", "description": "千牛连接ID"}, "method": {"type": "string", "description": "API方法名", "example": "mtop.taobao.qianniu.cs.user.query"}, "param": {"type": "object", "description": "API参数"}, "httpMethod": {"type": "string", "default": "post", "description": "HTTP方法"}, "version": {"type": "string", "default": "1.0", "description": "API版本"}}}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}}, "components": {"schemas": {"ApiResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "请求是否成功"}, "message": {"type": "string", "description": "响应消息"}, "data": {"type": "object", "description": "响应数据"}, "timestamp": {"type": "string", "format": "date-time", "description": "响应时间戳"}}}}}}