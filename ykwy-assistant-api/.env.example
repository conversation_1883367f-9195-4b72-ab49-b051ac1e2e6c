# 数据库配置
# docker 启动 ykwy-api，此时localhost指向docker容器的地址，所以需要通过docker网络访问
DATABASE_URL="********************************************/ykwy-assistant"

# JWT 认证配置
JWT_SECRET="your-super-secret-jwt-key-at-least-32-characters-long-for-security"

# CDN配置 - MinIO
MINIO_ACCESS_KEY=
MINIO_SECRET_KEY=
MINIO_BUCKET_NAME=
CDN_BASE_URL=

YKWY_ASSISTANT_WEB_URL=
YKWY_ASSISTANT_API_URL="https://ykwy-assistant-api.wuyoutansuo.com"
PORT=3000 #开发环境不建议
HOST=0.0.0.0 #开发环境不建议
TCP_PORT=9997
NODE_ENV=development

# Loki日志服务配置
LOKI_URL=
LOKI_USERNAME=
LOKI_PASSWORD=

# 销售智能体配置
SALES_AGENT_URL="http://localhost:8000"
SALES_AGENT_AUTO_REPLY="true"
