/*
  Warnings:

  - The values [<PERSON><PERSON><PERSON>,<PERSON>LOC<PERSON>] on the enum `ConversationAction` will be removed. If these variants are still used in the database, this will fail.
  - The values [ADMIN] on the enum `UserRole` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `isLocked` on the `conversation` table. All the data in the column will be lost.
  - You are about to drop the column `lockExpiresAt` on the `conversation` table. All the data in the column will be lost.
  - You are about to drop the column `lockedAt` on the `conversation` table. All the data in the column will be lost.
  - You are about to drop the column `lockedBy` on the `conversation` table. All the data in the column will be lost.
  - You are about to drop the column `platformId` on the `conversation` table. All the data in the column will be lost.
  - You are about to drop the column `platformId` on the `customer` table. All the data in the column will be lost.
  - You are about to drop the `platform` table. If the table is not empty, all the data it contains will be lost.
  - A unique constraint covering the columns `[qianniuAccountId,platformCustomerId]` on the table `customer` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[token]` on the table `invitation` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[inviteCode]` on the table `invitation` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `qianniuAccountId` to the `conversation` table without a default value. This is not possible if the table is not empty.
  - Added the required column `qianniuAccountId` to the `customer` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "MessageDeliveryStatus" AS ENUM ('PENDING', 'SENT', 'DELIVERED', 'FAILED');

-- CreateEnum
CREATE TYPE "InvitationStatus" AS ENUM ('PENDING', 'ACTIVATED', 'EXPIRED', 'REVOKED');

-- AlterEnum
BEGIN;
CREATE TYPE "ConversationAction_new" AS ENUM ('ENTER', 'TYPING', 'LEAVE', 'HEARTBEAT');
ALTER TABLE "conversation_activity" ALTER COLUMN "action" TYPE "ConversationAction_new" USING ("action"::text::"ConversationAction_new");
ALTER TYPE "ConversationAction" RENAME TO "ConversationAction_old";
ALTER TYPE "ConversationAction_new" RENAME TO "ConversationAction";
DROP TYPE "ConversationAction_old";
COMMIT;

-- AlterEnum
BEGIN;
CREATE TYPE "UserRole_new" AS ENUM ('SYSTEM_ADMIN', 'ORGANIZATION_ADMIN', 'TEAM_MANAGER', 'CUSTOMER_SERVICE');
ALTER TABLE "user" ALTER COLUMN "role" DROP DEFAULT;
ALTER TABLE "user" ALTER COLUMN "role" TYPE "UserRole_new" USING ("role"::text::"UserRole_new");
ALTER TYPE "UserRole" RENAME TO "UserRole_old";
ALTER TYPE "UserRole_new" RENAME TO "UserRole";
DROP TYPE "UserRole_old";
ALTER TABLE "user" ALTER COLUMN "role" SET DEFAULT 'CUSTOMER_SERVICE';
COMMIT;

-- DropForeignKey
ALTER TABLE "conversation" DROP CONSTRAINT "conversation_lockedBy_fkey";

-- DropForeignKey
ALTER TABLE "conversation" DROP CONSTRAINT "conversation_platformId_fkey";

-- DropForeignKey
ALTER TABLE "customer" DROP CONSTRAINT "customer_platformId_fkey";

-- DropForeignKey
ALTER TABLE "platform" DROP CONSTRAINT "platform_organizationId_fkey";

-- DropIndex
DROP INDEX "conversation_isLocked_lockExpiresAt_idx";

-- DropIndex
DROP INDEX "customer_platformId_platformCustomerId_key";

-- AlterTable
ALTER TABLE "conversation" DROP COLUMN "isLocked",
DROP COLUMN "lockExpiresAt",
DROP COLUMN "lockedAt",
DROP COLUMN "lockedBy",
DROP COLUMN "platformId",
ADD COLUMN     "autoReplyEnabled" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "conversationCode" TEXT,
ADD COLUMN     "qianniuAccountId" UUID NOT NULL;

-- AlterTable
ALTER TABLE "customer" DROP COLUMN "platformId",
ADD COLUMN     "encryptId" TEXT,
ADD COLUMN     "qianniuAccountId" UUID NOT NULL;

-- AlterTable
ALTER TABLE "invitation" ADD COLUMN     "inviteCode" TEXT,
ADD COLUMN     "teamId" UUID,
ADD COLUMN     "token" TEXT;

-- AlterTable
ALTER TABLE "message" ADD COLUMN     "deliveredAt" TIMESTAMP(3),
ADD COLUMN     "deliveryStatus" "MessageDeliveryStatus" NOT NULL DEFAULT 'PENDING',
ADD COLUMN     "failureReason" TEXT;

-- AlterTable
ALTER TABLE "user" ADD COLUMN     "canManageTeams" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "managedTeams" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "permissionLevel" INTEGER NOT NULL DEFAULT 1,
ADD COLUMN     "teamId" UUID;

-- DropTable
DROP TABLE "platform";

-- DropEnum
DROP TYPE "OrganizationRole";

-- CreateTable
CREATE TABLE "brand" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "logo" TEXT,
    "website" TEXT,
    "contactInfo" JSONB,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "brand_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "qianniu_client" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "teamId" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "connectionId" TEXT NOT NULL,
    "isOnline" BOOLEAN NOT NULL DEFAULT false,
    "lastOnlineAt" TIMESTAMP(3),
    "clientInfo" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "qianniu_client_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "qianniu_account" (
    "id" UUID NOT NULL,
    "clientId" UUID NOT NULL,
    "brandId" UUID NOT NULL,
    "accountName" TEXT NOT NULL,
    "accountId" TEXT NOT NULL,
    "shopName" TEXT,
    "shopId" TEXT,
    "platformType" "PlatformType" NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "isLoggedIn" BOOLEAN NOT NULL DEFAULT false,
    "lastLoginAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "qianniu_account_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "organization_setting" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "key" TEXT NOT NULL,
    "value" JSONB,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "organization_setting_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "team" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "organizationId" UUID NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3),
    "managerId" UUID,
    "maxMembers" INTEGER,
    "workingHours" JSONB,

    CONSTRAINT "team_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "connection_invitation" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "teamId" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "cdnUrl" TEXT,
    "status" "InvitationStatus" NOT NULL DEFAULT 'PENDING',
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "activatedAt" TIMESTAMP(3),
    "qianniuClientId" UUID,
    "boundUserId" UUID,
    "boundAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "connection_invitation_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "brand_organizationId_name_key" ON "brand"("organizationId", "name");

-- CreateIndex
CREATE UNIQUE INDEX "qianniu_client_connectionId_key" ON "qianniu_client"("connectionId");

-- CreateIndex
CREATE UNIQUE INDEX "qianniu_account_clientId_accountId_key" ON "qianniu_account"("clientId", "accountId");

-- CreateIndex
CREATE UNIQUE INDEX "qianniu_account_brandId_platformType_key" ON "qianniu_account"("brandId", "platformType");

-- CreateIndex
CREATE UNIQUE INDEX "organization_setting_organizationId_key_key" ON "organization_setting"("organizationId", "key");

-- CreateIndex
CREATE UNIQUE INDEX "connection_invitation_qianniuClientId_key" ON "connection_invitation"("qianniuClientId");

-- CreateIndex
CREATE INDEX "conversation_qianniuAccountId_status_idx" ON "conversation"("qianniuAccountId", "status");

-- CreateIndex
CREATE INDEX "conversation_autoReplyEnabled_lastActivity_idx" ON "conversation"("autoReplyEnabled", "lastActivity");

-- CreateIndex
CREATE INDEX "conversation_conversationCode_idx" ON "conversation"("conversationCode");

-- CreateIndex
CREATE INDEX "conversation_qianniuAccountId_conversationCode_idx" ON "conversation"("qianniuAccountId", "conversationCode");

-- CreateIndex
CREATE UNIQUE INDEX "customer_qianniuAccountId_platformCustomerId_key" ON "customer"("qianniuAccountId", "platformCustomerId");

-- CreateIndex
CREATE UNIQUE INDEX "invitation_token_key" ON "invitation"("token");

-- CreateIndex
CREATE UNIQUE INDEX "invitation_inviteCode_key" ON "invitation"("inviteCode");

-- AddForeignKey
ALTER TABLE "user" ADD CONSTRAINT "user_teamId_fkey" FOREIGN KEY ("teamId") REFERENCES "team"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "brand" ADD CONSTRAINT "brand_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "qianniu_client" ADD CONSTRAINT "qianniu_client_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "qianniu_client" ADD CONSTRAINT "qianniu_client_teamId_fkey" FOREIGN KEY ("teamId") REFERENCES "team"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "qianniu_account" ADD CONSTRAINT "qianniu_account_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "qianniu_client"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "qianniu_account" ADD CONSTRAINT "qianniu_account_brandId_fkey" FOREIGN KEY ("brandId") REFERENCES "brand"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "customer" ADD CONSTRAINT "customer_qianniuAccountId_fkey" FOREIGN KEY ("qianniuAccountId") REFERENCES "qianniu_account"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "conversation" ADD CONSTRAINT "conversation_qianniuAccountId_fkey" FOREIGN KEY ("qianniuAccountId") REFERENCES "qianniu_account"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "invitation" ADD CONSTRAINT "invitation_teamId_fkey" FOREIGN KEY ("teamId") REFERENCES "team"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "organization_setting" ADD CONSTRAINT "organization_setting_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "team" ADD CONSTRAINT "team_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "connection_invitation" ADD CONSTRAINT "connection_invitation_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "connection_invitation" ADD CONSTRAINT "connection_invitation_teamId_fkey" FOREIGN KEY ("teamId") REFERENCES "team"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "connection_invitation" ADD CONSTRAINT "connection_invitation_qianniuClientId_fkey" FOREIGN KEY ("qianniuClientId") REFERENCES "qianniu_client"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "connection_invitation" ADD CONSTRAINT "connection_invitation_boundUserId_fkey" FOREIGN KEY ("boundUserId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;
