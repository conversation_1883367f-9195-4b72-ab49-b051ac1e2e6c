generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ============ 用户认证模块 ============

model User {
  id            String   @id @default(uuid(7)) @db.Uuid
  name          String
  email         String
  emailVerified Boolean
  image         String?
  role          UserRole @default(CUSTOMER_SERVICE) // 用户角色

  // 团队关系（单一组织内的团队）
  teamId String? @db.Uuid // 用户所属团队

  // 团队管理权限
  managedTeams    String[] @default([]) // 管理的团队ID列表
  permissionLevel Int      @default(1) // 权限级别 1-4
  canManageTeams  Boolean  @default(false) // 是否可以管理团队

  createdAt DateTime
  updatedAt DateTime
  sessions  Session[]
  accounts  Account[]

  // 客服相关关联
  assignedConversations  Conversation[] // 分配的对话
  messages               Message[] // 发送的消息
  recalledMessages       Message[]              @relation("RecalledMessages") // 撤回的消息
  customerServiceTasks   CustomerServiceTask[] // 客服任务
  conversationActivities ConversationActivity[] // 对话操作记录
  // Better Auth 成员关系
  members                Member[] // Better Auth 组织成员关系

  // 团队关系
  team Team? @relation("TeamMembers", fields: [teamId], references: [id], onDelete: SetNull)

  // 邀请关系（作为邀请人）
  sentInvitations Invitation[] @relation("InviterUser")

  // 连接绑定关系
  boundConnectionInvitations ConnectionInvitation[] @relation("BoundConnectionInvitations")

  @@unique([email])
  @@map("user")
}

enum UserRole {
  SYSTEM_ADMIN // 系统管理员 (原ADMIN)
  ORGANIZATION_ADMIN // 组织管理员
  TEAM_MANAGER // 团队管理员 (新增)
  CUSTOMER_SERVICE // 客服人员
}

model Session {
  id        String   @id @default(uuid(7)) @db.Uuid
  expiresAt DateTime
  token     String
  createdAt DateTime
  updatedAt DateTime
  ipAddress String?
  userAgent String?
  userId    String   @db.Uuid
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  activeOrganizationId String?

  @@unique([token])
  @@map("session")
}

model Account {
  id                    String    @id @default(uuid(7)) @db.Uuid
  accountId             String
  providerId            String
  userId                String    @db.Uuid
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime
  updatedAt             DateTime

  @@map("account")
}

model Verification {
  id         String    @id @default(uuid(7)) @db.Uuid
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("verification")
}

// ============ 组织管理模块 ============

model Organization {
  id           String   @id @default(uuid(7)) @db.Uuid
  name         String // 组织名称
  description  String? // 组织描述
  logoUrl      String? // 组织Logo
  website      String? // 官网
  contactEmail String? // 联系邮箱
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // 关联关系
  members              Member[] // Better Auth 成员关系
  brands               Brand[] // 品牌管理
  conversations        Conversation[] // 客服对话
  customerServiceTasks CustomerServiceTask[] // 客服任务
  dailyStats           DailyStats[] // 统计数据

  qianniuClients        QianniuClient[] // 千牛客户端
  connectionInvitations ConnectionInvitation[] // 连接邀请
  settings              OrganizationSetting[] // 组织设置

  slug        String?
  logo        String?
  metadata    String?
  invitations Invitation[]

  teams Team[]

  @@unique([slug])
  @@map("organization")
}

// OrganizationRole 枚举已删除，在单一组织模式下使用 UserRole 代替

// ============ 电商平台管理模块 ============

// 品牌方模型

model Brand {
  id             String   @id @default(uuid(7)) @db.Uuid
  organizationId String   @db.Uuid // 品牌归属组织
  name           String // 品牌名称
  description    String? // 品牌描述
  logo           String? // 品牌Logo
  website        String? // 品牌官网
  contactInfo    Json? // 联系信息
  isActive       Boolean  @default(true)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // 关联关系
  organization    Organization     @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  qianniuAccounts QianniuAccount[] // 品牌的千牛账号 (一对多)

  @@unique([organizationId, name]) // 同一组织下品牌名称唯一
  @@map("brand")
}

// 千牛客户端模型 (属于特定团队)
model QianniuClient {
  id             String @id @default(uuid(7)) @db.Uuid
  organizationId String @db.Uuid
  teamId         String @db.Uuid // 明确属于一个团队

  // 客户端信息
  name        String // "客服团队A-工作站1"
  description String? // "一楼客服区第3台电脑"

  // 连接信息
  connectionId String    @unique // 当前WebSocket连接ID (必须字段，确保在线客户端都有有效连接)
  isOnline     Boolean   @default(false)
  lastOnlineAt DateTime?
  clientInfo   Json? // IP、MAC、版本等信息

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  organization Organization          @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  team         Team                  @relation(fields: [teamId], references: [id], onDelete: Cascade)
  accounts     QianniuAccount[] // 这个客户端的所有账号
  invitation   ConnectionInvitation? // 来源邀请

  @@map("qianniu_client")
}

// 千牛账号模型 (属于特定客户端，服务特定品牌)
model QianniuAccount {
  id       String @id @default(uuid(7)) @db.Uuid
  clientId String @db.Uuid // 所属千牛客户端
  brandId  String @db.Uuid // 服务的品牌 (一对一关系)

  // 账号信息
  accountName  String // 千牛账号名
  accountId    String // 千牛内部ID
  shopName     String? // 店铺名称
  shopId       String? // 店铺ID
  platformType PlatformType // TAOBAO, TMALL, JD等

  // 状态信息
  isActive    Boolean   @default(true)
  isLoggedIn  Boolean   @default(false) // 当前是否登录
  lastLoginAt DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  client        QianniuClient  @relation(fields: [clientId], references: [id], onDelete: Cascade)
  brand         Brand          @relation(fields: [brandId], references: [id], onDelete: Cascade)
  conversations Conversation[] // 这个账号的对话
  customers     Customer[] // 这个账号的客户

  @@unique([clientId, accountId]) // 同一客户端下账号ID唯一
  @@unique([brandId, platformType]) // 同一品牌在同一平台只能有一个主账号
  @@map("qianniu_account")
}

enum PlatformType {
  TAOBAO // 淘宝
  TMALL // 天猫
  PINDUODUO // 拼多多
  JD // 京东
  WECHAT // 微信
  DOUYIN // 抖音
  XIAOHONGSHU // 小红书
  OTHER // 其他
}

// ============ 客户管理模块 ============

model Customer {
  id                 String    @id @default(uuid(7)) @db.Uuid
  qianniuAccountId   String    @db.Uuid // 千牛账号ID (统一架构)
  platformCustomerId String // 平台方的客户ID
  encryptId          String? // 千牛加密ID，用于API调用
  nickname           String // 客户昵称（用于对话显示）
  avatar             String? // 头像（用于对话显示）
  phone              String? // 电话
  email              String? // 邮箱
  realName           String? // 真实姓名
  gender             String? // 性别
  location           String? // 地区
  vipLevel           String? // VIP等级
  tags               String[] // 客户标签
  notes              String? // 备注
  lastActiveAt       DateTime? // 最后活跃时间
  totalOrders        Int? // 总订单数
  totalAmount        Float? // 总消费金额
  createdAt          DateTime  @default(now())
  updatedAt          DateTime  @updatedAt

  // 关联关系
  qianniuAccount QianniuAccount @relation(fields: [qianniuAccountId], references: [id], onDelete: Cascade)
  conversations  Conversation[] // 客服对话

  // 同一千牛账号下客户ID唯一
  @@unique([qianniuAccountId, platformCustomerId])
  @@map("customer")
}

// ============ 对话管理模块 ============

model Conversation {
  id               String             @id @default(uuid(7)) @db.Uuid
  organizationId   String             @db.Uuid
  qianniuAccountId String             @db.Uuid // 千牛账号ID (统一架构)
  customerId       String             @db.Uuid
  assignedUserId   String?            @db.Uuid // 分配的客服人员
  title            String? // 对话标题
  conversationCode String? // 千牛会话代码 (用于API调用)
  status           ConversationStatus @default(PENDING)
  priority         Priority           @default(NORMAL)
  tags             String[] // 对话标签

  // 自动回复控制
  autoReplyEnabled Boolean  @default(true) // 是否启用自动回复（默认开启）
  lastActivity     DateTime @default(now()) // 最后活动时间

  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  closedAt  DateTime? // 关闭时间

  // 关联关系
  organization      Organization           @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  qianniuAccount    QianniuAccount         @relation(fields: [qianniuAccountId], references: [id], onDelete: Cascade)
  customer          Customer               @relation(fields: [customerId], references: [id], onDelete: Cascade)
  assignedUser      User?                  @relation(fields: [assignedUserId], references: [id])
  messages          Message[] // 对话消息
  aiRecommendations AiRecommendation[] // AI推荐回复
  activities        ConversationActivity[] // 对话操作记录

  // 索引
  @@index([organizationId, status])
  @@index([assignedUserId, status])
  @@index([qianniuAccountId, status])
  @@index([autoReplyEnabled, lastActivity])
  @@index([conversationCode]) // 千牛会话代码索引
  @@index([qianniuAccountId, conversationCode]) // 复合索引，提高查找性能
  @@map("conversation")
}

enum ConversationStatus {
  PENDING // 待处理
  IN_PROGRESS // 处理中
  WAITING // 等待客户回复
  RESOLVED // 已解决
  CLOSED // 已关闭
}

enum Priority {
  LOW // 低优先级
  NORMAL // 普通
  HIGH // 高优先级
  URGENT // 紧急
}

// ============ 对话操作记录模块 ============

model ConversationActivity {
  id             String             @id @default(uuid(7)) @db.Uuid
  conversationId String             @db.Uuid
  userId         String?            @db.Uuid // 改为可选，支持匿名记录
  action         ConversationAction
  metadata       Json?
  createdAt      DateTime           @default(now())

  // 关联关系
  conversation Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  user         User?        @relation(fields: [userId], references: [id], onDelete: SetNull) // 改为 SetNull

  @@index([conversationId, createdAt])
  @@map("conversation_activity")
}

enum ConversationAction {
  ENTER // 进入对话
  TYPING // 正在输入
  LEAVE // 离开对话
  HEARTBEAT // 心跳检测
}

// ============ 消息管理模块 ============

model Message {
  id                String      @id @default(uuid(7)) @db.Uuid
  conversationId    String      @db.Uuid
  senderId          String?     @db.Uuid // 发送者ID（仅当senderType为CUSTOMER_SERVICE时有值，客户消息该字段为null）
  senderType        SenderType // 发送者类型
  content           String // 消息内容
  messageType       MessageType @default(TEXT)
  metadata          Json? // 消息元数据（附件信息等）
  platformMessageId String? // 平台方消息ID

  // 消息投递状态
  deliveryStatus MessageDeliveryStatus @default(PENDING)
  deliveredAt    DateTime? // 投递成功时间
  failureReason  String? // 投递失败原因

  // 消息顺序管理
  sequenceNumber Int // 消息序列号，确保顺序

  // 撤回相关字段 - 仅适用于客服发送的消息
  isRecalled   Boolean   @default(false) // 是否已撤回（仅客服消息可撤回）
  recalledAt   DateTime? // 撤回时间
  recalledBy   String?   @db.Uuid // 撤回操作者ID（必须是发送者本人）
  recallReason String? // 撤回原因

  // 回复关系
  parentMessageId String?   @db.Uuid // 回复的消息ID
  parentMessage   Message?  @relation("MessageReplies", fields: [parentMessageId], references: [id])
  replies         Message[] @relation("MessageReplies")

  // AI关联 - 统一处理AI相关信息
  basedOnRecommendationId String?           @unique @db.Uuid // 基于哪个AI推荐发送的（有这个字段就说明是AI相关）
  basedOnRecommendation   AiRecommendation? @relation("RecommendationUsedAsMessage", fields: [basedOnRecommendationId], references: [id])

  // 时间戳
  sentAt    DateTime @default(now()) // 发送时间
  createdAt DateTime @default(now()) // 服务器接收时间
  updatedAt DateTime @updatedAt

  // 关联关系
  conversation      Conversation       @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  sender            User?              @relation(fields: [senderId], references: [id]) // 仅客服消息有发送者
  recalledByUser    User?              @relation("RecalledMessages", fields: [recalledBy], references: [id])
  aiRecommendations AiRecommendation[] @relation("MessageRecommendations") // 针对这条消息生成的AI推荐

  @@unique([conversationId, sequenceNumber])
  // 同一对话中序列号唯一
  @@index([conversationId, sentAt])
  @@map("message")
}

enum SenderType {
  CUSTOMER // 客户（来自外部平台，显示信息从Customer表获取）
  CUSTOMER_SERVICE // 客服（系统内部User，可撤回自己的消息）
  SYSTEM // 系统（系统消息，不可撤回）
  AI // AI助手（可撤回）
}

enum MessageType {
  TEXT // 文本
  IMAGE // 图片
  FILE // 文件
  AUDIO // 语音
  VIDEO // 视频
  SYSTEM // 系统消息
}

enum MessageDeliveryStatus {
  PENDING // 待发送
  SENT // 已发送
  DELIVERED // 已送达
  FAILED // 发送失败
}

// ============ AI推荐回复模块 ============

model AiRecommendation {
  id             String                 @id @default(uuid(7)) @db.Uuid
  messageId      String                 @db.Uuid // 针对哪条消息生成的推荐
  conversationId String                 @db.Uuid // 冗余字段，便于查询
  query          String // 查询关键词（用于检索，比如客户的问题，或者客服修正的查询）
  recommendation String // AI推荐回复内容
  confidence     Float // 置信度 (0-1)
  modelName      String // 使用的模型名称
  modelVersion   String? // 模型版本
  strategyType   RecommendationStrategy // 推荐策略类型
  metadata       Json? // 额外的元数据

  // 使用状态
  isUsed Boolean   @default(false) // 是否被客服使用
  usedAt DateTime? // 使用时间

  // 反馈信息
  feedback     RecommendationFeedback? // 客服反馈
  feedbackNote String? // 反馈备注

  createdAt DateTime @default(now())

  // 关联关系
  triggerMessage Message      @relation("MessageRecommendations", fields: [messageId], references: [id], onDelete: Cascade)
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  // 重要：如果这个推荐被用作消息发送
  usedAsMessage Message? @relation("RecommendationUsedAsMessage")

  @@index([messageId])
  @@index([conversationId, createdAt])
  @@map("ai_recommendation")
}

enum RecommendationStrategy {
  QUICK_REPLY // 快速回复
  DETAILED_ANSWER // 详细解答
  QUESTION_CLARIFICATION // 问题澄清
  PRODUCT_INFO // 产品信息
  ORDER_STATUS // 订单状态
  COMPLAINT_HANDLE // 投诉处理
  SALES_PROMOTION // 销售推广
  POLITE_DECLINE // 礼貌拒绝
}

enum RecommendationFeedback {
  EXCELLENT // 优秀 - 直接使用
  GOOD // 良好 - 稍作修改后使用
  AVERAGE // 一般 - 作为参考
  POOR // 较差 - 没有帮助
  WRONG // 错误 - 完全不合适
}

// ============ 客服任务管理模块 ============

model CustomerServiceTask {
  id             String     @id @default(uuid(7)) @db.Uuid
  organizationId String     @db.Uuid
  assignedUserId String?    @db.Uuid // 改为可选，支持未分配状态
  title          String
  description    String?
  taskType       TaskType
  status         TaskStatus @default(TODO)
  priority       Priority   @default(NORMAL)
  dueDate        DateTime?
  createdAt      DateTime   @default(now())
  updatedAt      DateTime   @updatedAt
  completedAt    DateTime?

  // 关联关系
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  assignedUser User?        @relation(fields: [assignedUserId], references: [id], onDelete: SetNull)

  @@index([assignedUserId, status])
  @@map("customer_service_task")
}

enum TaskType {
  FOLLOW_UP // 跟进客户
  ISSUE_RESOLVE // 解决问题
  ORDER_PROCESS // 订单处理
  COMPLAINT // 投诉处理
  TRAINING // 培训任务
  OTHER // 其他
}

enum TaskStatus {
  TODO // 待办
  IN_PROGRESS // 进行中
  DONE // 已完成
  CANCELLED // 已取消
}

// ============ 统计分析模块 ============

model DailyStats {
  id                    String   @id @default(uuid(7)) @db.Uuid
  organizationId        String   @db.Uuid
  date                  DateTime @db.Date
  totalConversations    Int      @default(0) // 总对话数
  newConversations      Int      @default(0) // 新对话数
  resolvedConversations Int      @default(0) // 已解决对话数
  avgResponseTime       Float? // 平均响应时间（分钟）
  customerSatisfaction  Float? // 客户满意度
  createdAt             DateTime @default(now())

  // 关联关系
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([organizationId, date])
  @@map("daily_stats")
}

// Better Auth 需要 Member 模型来管理组织成员关系
model Member {
  id             String   @id @default(uuid(7)) @db.Uuid
  organizationId String   @db.Uuid
  userId         String   @db.Uuid
  role           String // Better Auth 角色
  createdAt      DateTime @default(now())

  // 关联关系
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([organizationId, userId])
  @@map("member")
}

model Invitation {
  id             String       @id @default(uuid(7)) @db.Uuid
  organizationId String       @db.Uuid
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  email          String
  role           String?
  status         String
  expiresAt      DateTime
  inviterId      String       @db.Uuid
  inviter        User         @relation("InviterUser", fields: [inviterId], references: [id], onDelete: Cascade)

  // Better Auth标准字段
  token      String? @unique // 邀请令牌
  inviteCode String? @unique // 邀请码

  teamId String? @db.Uuid
  team   Team?   @relation("TeamInvitations", fields: [teamId], references: [id], onDelete: SetNull)

  @@map("invitation")
}

// ============ 组织设置模块 ============

model OrganizationSetting {
  id             String   @id @default(uuid(7)) @db.Uuid
  organizationId String   @db.Uuid
  key            String // 设置键名，如 'ragflow_config'
  value          Json? // 设置值，JSON格式
  description    String? // 设置描述
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // 关联关系
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([organizationId, key])
  @@map("organization_setting")
}

model Team {
  id             String       @id @default(uuid(7)) @db.Uuid
  name           String
  organizationId String       @db.Uuid
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime?    @updatedAt

  // 新增：团队管理员字段
  managerId    String? @db.Uuid // 团队管理员的User ID
  maxMembers   Int?
  workingHours Json? // 工作时间配置

  // 关联关系
  members               User[]                 @relation("TeamMembers") // 团队成员
  invitations           Invitation[]           @relation("TeamInvitations")
  qianniuClients        QianniuClient[] // 团队的千牛客户端
  connectionInvitations ConnectionInvitation[] // 连接邀请

  @@map("team")
}

// 连接邀请模型
model ConnectionInvitation {
  id             String @id @default(uuid(7)) @db.Uuid
  organizationId String @db.Uuid
  teamId         String @db.Uuid

  // 连接信息
  name        String // "客服工作站-01"
  description String? // "一楼客服区第3台电脑"
  cdnUrl      String? // CDN脚本链接

  // 状态管理
  status      InvitationStatus @default(PENDING)
  expiresAt   DateTime // 邀请过期时间
  activatedAt DateTime?

  // 激活后的客户端信息
  qianniuClientId String? @unique @db.Uuid

  // 用户绑定信息
  boundUserId String?   @db.Uuid // 绑定的用户ID
  boundAt     DateTime? // 绑定时间

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  organization  Organization   @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  team          Team           @relation(fields: [teamId], references: [id], onDelete: Cascade)
  qianniuClient QianniuClient? @relation(fields: [qianniuClientId], references: [id], onDelete: SetNull)
  boundUser     User?          @relation("BoundConnectionInvitations", fields: [boundUserId], references: [id], onDelete: SetNull)

  @@map("connection_invitation")
}

enum InvitationStatus {
  PENDING // 待激活
  ACTIVATED // 已激活
  EXPIRED // 已过期
  REVOKED // 已撤销
}
