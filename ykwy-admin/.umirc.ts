import { defineConfig } from '@umijs/max';
import routes from './src/router/routes';

export default defineConfig({
  exportStatic: { ignorePreRenderError: false },
  esbuildMinifyIIFE: true,
  hash: true,
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {
    dataField: 'data',
  },
  layout: {
    title: '易康无忧智能客服',
    contentStyle: {
      overflow: 'auto',
    },
  },
  routes,
  npmClient: 'pnpm',
  tailwindcss: {},
  define: {
    'process.env.UMI_APP_API_URL':
      process.env.UMI_APP_API_URL || 'http://localhost:3009',
  },
});
