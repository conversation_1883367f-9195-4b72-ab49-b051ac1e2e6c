export type LogLevel = 'info' | 'warn' | 'error' | 'debug';

export type LogModule = 'request' | 'error' | 'user' | 'page' | 'component';

export interface LogDetails {
  method?: string;
  requestUrl?: string;
  requestData?: Record<string, unknown> | string | null;
  responseStatus?: number;
  responseData?: Record<string, unknown> | string | null;
  duration?: number;
  stack?: string;
  errorType?: string;
}

export interface LogData {
  level: LogLevel;
  message: string;
  timestamp: number;
  url?: string;
  userAgent?: string;
  userId?: string;
  sessionId?: string;
  module: LogModule;
  details?: LogDetails;
}

export interface LogQueue {
  logs: LogData[];
  maxSize: number;
  flushInterval: number;
  sending: boolean;
}
