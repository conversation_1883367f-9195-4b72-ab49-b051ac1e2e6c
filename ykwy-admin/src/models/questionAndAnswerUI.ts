/**
 * 问答知识库 UI 组件相关的类型定义
 * 用于前端组件展示和交互
 */

import type { Answer } from './questionAndAnswer';

// 用于展示的组件类型（扁平化处理）
export interface QuestionGroup {
  id: string;
  title: string; // 对应 questionType
  categoryName?: string; // 分类名称
  categoryCode: string;
  orderStatus: string;
  commonQuestionSamples: string[]; // 常见问法样本
  answers: Answer[];
  isCustom: boolean;
  matchMode: string;
  isPermanent: boolean;
  timeValidityLabel?: string; // 时效标签

  // 商品信息
  productId?: string;
  productName?: string;

  // 统计信息（这些可能需要从其他地方获取）
  weeklyConsultationVolume: number;
  relatedProductsCount: number;
  answersCount: number;
  isExpanded?: boolean;
}

export interface QuestionConfig {
  orderStatus: string;
  timeValidityLabel?: string;
  isPermanent: boolean;
  matchMode: string;
  isCustom: boolean;
  productInfo?: {
    id: string;
    name: string;
  };
}

export interface QuestionKnowledgeListProps {
  data: QuestionGroup[];
  loading?: boolean;
  onEdit?: (groupId: string, answerId: string) => void;
  onDelete?: (groupId: string, answerId: string) => void;
  onCopyCondition?: (groupId: string, answerId: string) => void;
  onAddAnswer?: (groupId: string) => void;
  onEditQuestion?: (groupId: string) => void;
  onDeleteQuestion?: (groupId: string) => void;
  onUpdateContent?: (
    groupId: string,
    answerId: string,
    newContent: string,
  ) => void;
  onConfigChange?: (groupId: string, newConfig: QuestionConfig) => void;
}
