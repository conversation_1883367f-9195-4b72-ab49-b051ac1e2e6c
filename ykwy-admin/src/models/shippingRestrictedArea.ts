// 发货受限地址相关类型定义

export interface ShippingRestrictedArea {
  id: string;
  province: string;
  city: string;
  district: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ShippingRestrictedAreaInput {
  id?: string;
  province: string;
  city: string;
  district: string;
  isActive?: boolean;
}

export interface ShippingRestrictedAreaQueryParams {
  province?: string;
  city?: string;
  district?: string;
  searchText?: string; // 用于全文搜索省/市/县区
  isActive?: boolean;
  current?: number;
  pageSize?: number;
  skip?: number;
  take?: number;
}

export interface ShippingRestrictedAreaListResponse {
  items: ShippingRestrictedArea[];
  total: number;
}

export interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

export interface BulkCreateInput {
  areas: ShippingRestrictedAreaInput[];
}

export interface BulkCreateResult {
  successCount: number;
  failedCount: number;
  errors?: string[];
}

export interface BulkDeleteResult {
  successCount: number;
  failedCount: number;
  errors?: string[];
}
