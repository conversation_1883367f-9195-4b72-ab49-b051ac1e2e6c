/**
 * 尺码表相关类型定义
 */

// 通用API响应类型
export interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
}

// 简单尺码表类型
export interface SizeChartSimple {
  id: string;
  name: string;
  sizeRange: string;
  sizeValue: string;
  createdAt: string;
  updatedAt: string;
  isDeleted: number;
}

export interface CreateSizeChartSimpleInput {
  name: string;
  sizeRange: string;
  sizeValue: string;
}

export interface UpdateSizeChartSimpleInput {
  name?: string;
  sizeRange?: string;
  sizeValue?: string;
}

export interface SizeChartSimpleQueryParams {
  page?: number;
  limit?: number;
  name?: string;
  sizeRange?: string;
  sizeValue?: string;
}

export interface SizeChartSimpleListResponse {
  data: SizeChartSimple[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 复合尺码表类型
export interface CompositeSizeChartSimple {
  id: string;
  name: string;
  type: string;
  sizeRange: string;
  sizeValue: string;
  createdAt: string;
  updatedAt: string;
  isDeleted: number;
}

export interface CreateCompositeSizeChartSimpleInput {
  name: string;
  type: string;
  sizeRange: string;
  sizeValue: string;
}

export interface UpdateCompositeSizeChartSimpleInput {
  name?: string;
  type?: string;
  sizeRange?: string;
  sizeValue?: string;
}

export interface CompositeSizeChartSimpleQueryParams {
  page?: number;
  limit?: number;
  name?: string;
  type?: string;
  sizeRange?: string;
  sizeValue?: string;
}

export interface CompositeSizeChartSimpleListResponse {
  data: CompositeSizeChartSimple[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Name唯一性检查相关类型
export interface CheckNameUniqueParams {
  name: string;
  excludeId?: string;
}

export interface CheckNameUniqueResponse {
  exists: boolean;
  available: boolean;
}
