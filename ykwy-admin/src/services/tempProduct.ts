import type {
  ApiResponse,
  BatchImportResult,
  BulkCreateInput,
  BulkCreateResult,
  BulkDeleteResult,
  TempProduct,
  TempProductInput,
  TempProductListResponse,
  TempProductQueryParams,
} from '@/models/tempProduct';
import { request } from '@umijs/max';
import Cookies from 'js-cookie';

const API_BASE_URL =
  (process.env.UMI_APP_API_URL || 'http://localhost:3009') + '/api/v1';

/**
 * 获取临时商品列表
 */
export async function getTempProductList(
  params?: TempProductQueryParams,
  options?: { [key: string]: any },
) {
  // 转换分页参数
  const queryParams: any = {
    ...params,
  };

  if (queryParams.current && queryParams.pageSize) {
    queryParams.skip = (queryParams.current - 1) * queryParams.pageSize;
    queryParams.take = queryParams.pageSize;
    delete queryParams.current;
    delete queryParams.pageSize;
  }

  return request<ApiResponse<TempProductListResponse>>(
    `${API_BASE_URL}/temp-products`,
    {
      method: 'GET',
      params: queryParams,
      ...(options || {}),
    },
  );
}

/**
 * 获取单个临时商品详情
 */
export async function getTempProductDetail(
  id: string,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<TempProduct>>(
    `${API_BASE_URL}/temp-product/${id}`,
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/**
 * 根据productId获取单个临时商品详情
 */
export async function getTempProductByProductId(
  productId: string,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<TempProduct>>(
    `${API_BASE_URL}/temp-product/by-product-id/${productId}`,
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/**
 * 创建或更新临时商品
 */
export async function upsertTempProduct(
  data: TempProductInput,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<TempProduct>>(`${API_BASE_URL}/temp-product`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
    ...(options || {}),
  });
}

/**
 * 删除临时商品
 */
export async function deleteTempProduct(
  id: string,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<TempProduct>>(
    `${API_BASE_URL}/temp-product/${id}`,
    {
      method: 'DELETE',
      ...(options || {}),
    },
  );
}
/**
 * 恢复已删除的临时商品
 */
export async function restoreTempProduct(
  id: string,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<TempProduct>>(
    `${API_BASE_URL}/temp-product/${id}/restore`,
    {
      method: 'POST',
      ...(options || {}),
    },
  );
}
/**
 * 批量删除临时商品
 */
export async function bulkDeleteTempProducts(
  ids: string[],
  options?: { [key: string]: any },
) {
  return request<ApiResponse<BulkDeleteResult>>(
    `${API_BASE_URL}/temp-products/bulk-delete`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: { ids },
      ...(options || {}),
    },
  );
}

/**
 * 批量创建临时商品
 */
export async function bulkCreateTempProducts(
  data: BulkCreateInput,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<BulkCreateResult>>(
    `${API_BASE_URL}/temp-products/bulk-create`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data,
      ...(options || {}),
    },
  );
}

/**
 * 清空所有临时商品数据
 */
export async function truncateTempProducts(options?: { [key: string]: any }) {
  return request<ApiResponse<BulkDeleteResult>>(
    `${API_BASE_URL}/temp-products/truncate`,
    {
      method: 'DELETE',
      ...(options || {}),
    },
  );
}

/**
 * 根据商品状态查找临时商品
 */
export async function getTempProductsByStatus(
  status: string,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<TempProduct[]>>(
    `${API_BASE_URL}/temp-products/status/${encodeURIComponent(status)}`,
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/**
 * 根据商品名称搜索临时商品
 */
export async function searchTempProductsByName(
  name: string,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<TempProduct[]>>(
    `${API_BASE_URL}/temp-products/search?name=${encodeURIComponent(name)}`,
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/**
 * 批量导入临时商品（Excel文件）
 */
export async function batchImportTempProducts(
  file: File,
  options?: { [key: string]: any },
) {
  const formData = new FormData();
  formData.append('file', file);

  return request<ApiResponse<BatchImportResult>>(
    `${API_BASE_URL}/temp-products/batch-import`,
    {
      method: 'POST',
      data: formData,
      timeout: 120000,
      // 不设置Content-Type，让浏览器自动设置multipart/form-data
      ...(options || {}),
    },
  );
}

/**
 * 下载批量导入模版
 * @param format 文件格式，'excel' 或 'csv'
 */
export async function downloadImportTemplate(
  format: 'excel' | 'csv' = 'excel',
  options?: { [key: string]: any },
): Promise<{ blob: Blob; filename: string }> {
  const url = `${API_BASE_URL}/temp-products/import-template?format=${format}`;
  const token = Cookies.get('access_token');
  const fetchOptions: RequestInit = {
    method: 'GET',
    headers: {
      ...(token ? { Authorization: `Bearer ${token}` } : {}),
      ...(options?.headers || {}),
    },
    ...options,
  };
  const response = await fetch(url, fetchOptions);
  if (!response.ok) {
    throw new Error(`下载模板失败: ${response.status} ${response.statusText}`);
  }
  const blob = await response.blob();
  let filename = format === 'csv' ? '批量导入模板.csv' : '批量导入模板.xlsx';
  const disposition = response.headers.get('Content-Disposition');
  if (disposition) {
    const match = disposition.match(/filename="?([^";]+)"?/);
    if (match) {
      filename = decodeURIComponent(match[1]);
    }
  }
  return { blob, filename };
}

/**
 * 导出商品数据
 */
export async function exportTempProducts(
  exportParams: {
    format: 'excel' | 'csv';
    fields: string[];
    filters?: {
      name?: string;
      status?: string;
      productId?: string;
      includeDeleted?: boolean;
    };
  },
  options?: { [key: string]: any },
) {
  return request<
    ApiResponse<{
      message: string;
      filename: string;
      successCount: number;
      failCount: number;
      filePath: string;
    }>
  >(`${API_BASE_URL}/temp-products/export`, {
    method: 'POST',
    data: exportParams,
    ...(options || {}),
  });
}

/**
 * 下载导出文件
 */
export async function downloadExportFile(
  filename: string,
  options?: { [key: string]: any },
) {
  return request(
    `${API_BASE_URL}/temp-products/download/${encodeURIComponent(filename)}`,
    {
      method: 'GET',
      responseType: 'blob',
      ...(options || {}),
    },
  );
}

export async function syncTempProductsToRagflow(options?: {
  [key: string]: any;
}) {
  return request(`${API_BASE_URL}/temp-products/sync-ragflow`, {
    method: 'POST',
    ...(options || {}),
  });
}
