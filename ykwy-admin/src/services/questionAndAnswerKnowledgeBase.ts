import { request } from '@umijs/max';

const API_BASE_URL =
  (process.env.UMI_APP_API_URL || 'http://localhost:3009') + '/api/v1';

/**
 * 获取知识库问答列表（分页、搜索）
 */
export async function getKnowledgeBaseList(
  params: any,
  options?: { [key: string]: any },
) {
  // ProTable 传入 current/pageSize，需转为 skip/take
  const queryParams: any = { ...params };
  if (params?.current && params?.pageSize) {
    queryParams.skip = (params.current - 1) * params.pageSize;
    queryParams.take = params.pageSize;
    delete queryParams.current;
    delete queryParams.pageSize;
  }
  return request(`${API_BASE_URL}/qa-knowledge-base`, {
    method: 'GET',
    params: queryParams,
    ...(options || {}),
  });
}

/**
 * 获取知识库问答详情
 */
export async function getKnowledgeBaseDetail(
  id: string,
  options?: { [key: string]: any },
) {
  return request(`${API_BASE_URL}/qa-knowledge-base/${id}`, {
    method: 'GET',
    ...(options || {}),
  });
}

/**
 * 创建或更新知识库问答
 */
export async function upsertKnowledgeBase(
  data: any,
  options?: { [key: string]: any },
) {
  return request(`${API_BASE_URL}/qa-knowledge-base`, {
    method: 'POST',
    data,
    ...(options || {}),
  });
}

/**
 * 删除知识库问答
 */
export async function deleteKnowledgeBase(
  id: string,
  options?: { [key: string]: any },
) {
  return request(`${API_BASE_URL}/qa-knowledge-base/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}

/**
 * 批量删除知识库问答
 */
export async function bulkDeleteKnowledgeBase(
  ids: string[],
  options?: { [key: string]: any },
) {
  return request(`${API_BASE_URL}/qa-knowledge-base/bulk-delete`, {
    method: 'POST',
    data: { ids },
    ...(options || {}),
  });
}

/**
 * 获取知识库问答统计
 */
export async function getKnowledgeBaseStats(
  params?: any,
  options?: { [key: string]: any },
) {
  return request(`${API_BASE_URL}/qa-knowledge-base/stats`, {
    method: 'GET',
    params,
    ...(options || {}),
  });
}
