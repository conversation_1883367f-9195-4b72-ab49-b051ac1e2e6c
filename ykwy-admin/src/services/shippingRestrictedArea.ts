import type {
  ApiResponse,
  BulkCreateInput,
  BulkCreateResult,
  BulkDeleteResult,
  ShippingRestrictedArea,
  ShippingRestrictedAreaInput,
  ShippingRestrictedAreaListResponse,
  ShippingRestrictedAreaQueryParams,
} from '@/models/shippingRestrictedArea';
import { request } from '@umijs/max';
import Cookies from 'js-cookie';

const API_BASE_URL =
  (process.env.UMI_APP_API_URL || 'http://localhost:3009') + '/api/v1';

/**
 * 获取发货受限地址列表
 */
export async function getShippingRestrictedAreaList(
  params?: ShippingRestrictedAreaQueryParams,
  options?: { [key: string]: any },
) {
  // 转换分页参数
  const queryParams: any = {
    ...params,
  };

  if (params?.current && params?.pageSize) {
    queryParams.skip = (params.current - 1) * params.pageSize;
    queryParams.take = params.pageSize;
    delete queryParams.current;
    delete queryParams.pageSize;
  }

  return request<ApiResponse<ShippingRestrictedAreaListResponse>>(
    `${API_BASE_URL}/shipping-restricted-areas`,
    {
      method: 'GET',
      params: queryParams,
      ...(options || {}),
    },
  );
}

/**
 * 获取单个发货受限地址详情
 */
export async function getShippingRestrictedAreaById(
  id: string,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<ShippingRestrictedArea>>(
    `${API_BASE_URL}/shipping-restricted-area/${id}`,
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/**
 * 创建或更新发货受限地址
 */
export async function upsertShippingRestrictedArea(
  data: ShippingRestrictedAreaInput,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<ShippingRestrictedArea>>(
    `${API_BASE_URL}/shipping-restricted-area`,
    {
      method: 'POST',
      data,
      ...(options || {}),
    },
  );
}

/**
 * 批量创建发货受限地址
 */
export async function bulkCreateShippingRestrictedAreas(
  data: BulkCreateInput,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<BulkCreateResult>>(
    `${API_BASE_URL}/shipping-restricted-areas/bulk-create`,
    {
      method: 'POST',
      data,
      ...(options || {}),
    },
  );
}

/**
 * 更新发货受限地址状态
 */
export async function updateShippingRestrictedAreaStatus(
  id: string,
  isActive: boolean,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<ShippingRestrictedArea>>(
    `${API_BASE_URL}/shipping-restricted-area/${id}/status`,
    {
      method: 'PUT',
      data: { isActive },
      ...(options || {}),
    },
  );
}

/**
 * 批量删除发货受限地址
 */
export async function bulkDeleteShippingRestrictedAreas(
  ids: string[],
  options?: { [key: string]: any },
) {
  return request<ApiResponse<BulkDeleteResult>>(
    `${API_BASE_URL}/shipping-restricted-areas/bulk-delete`,
    {
      method: 'DELETE',
      data: { ids },
      ...(options || {}),
    },
  );
}

/**
 * 检查地址是否受限
 */
export async function checkAddressRestriction(
  province: string,
  city: string,
  district: string,
  options?: { [key: string]: any },
) {
  return request<ApiResponse<{ isRestricted: boolean }>>(
    `${API_BASE_URL}/shipping-restricted-areas/check`,
    {
      method: 'GET',
      params: { province, city, district },
      ...(options || {}),
    },
  );
}

/**
 * 导入发货受限地址文件
 */
export async function importShippingRestrictedAreaFile(
  file: File,
  options?: { [key: string]: any },
) {
  const formData = new FormData();
  formData.append('file', file);
  return request(`${API_BASE_URL}/shipping-restricted-areas/import`, {
    method: 'POST',
    data: formData,
    requestType: 'form',
    timeout: 120000,
    ...(options || {}),
  });
}

/**
 * 下载发货受限地址导入模板（使用原生fetch实现）
 */
export async function downloadShippingRestrictedAreaTemplate(
  format: 'excel' | 'csv' = 'excel',
  options?: { [key: string]: any },
): Promise<{ blob: Blob; filename: string }> {
  const url = `${API_BASE_URL}/shipping-restricted-areas/template?format=${format}`;
  const token = Cookies.get('access_token');
  const fetchOptions: RequestInit = {
    method: 'GET',
    headers: {
      ...(token ? { Authorization: `Bearer ${token}` } : {}),
      ...(options?.headers || {}),
    },
    ...options,
  };

  const response = await fetch(url, fetchOptions);
  if (!response.ok) {
    throw new Error(`下载模板失败: ${response.status} ${response.statusText}`);
  }

  const blob = await response.blob();

  // 从响应头获取文件名
  let filename =
    format === 'csv' ? '发货受限地址模板.csv' : '发货受限地址模板.xlsx';
  const disposition = response.headers.get('Content-Disposition');
  if (disposition) {
    const match = disposition.match(/filename="?([^";]+)"?/);
    if (match) {
      filename = decodeURIComponent(match[1]);
    }
  }

  return { blob, filename };
}

/**
 * 同步发货受限地址到RagFlow知识库
 */
export async function syncShippingRestrictedAreaToRagflow(options?: {
  [key: string]: any;
}) {
  return request(`${API_BASE_URL}/shipping-restricted-areas/sync-ragflow`, {
    method: 'POST',
    ...(options || {}),
  });
}
