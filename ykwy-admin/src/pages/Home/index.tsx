import CustomerService from '@/components/Assistant/CustomerService';
import useStore from '@/store/userStore';
// 引入 UserStore 类型
import type { UserStore } from '@/store/userStore';
import logger from '@/utils/logger';
import {
  DownOutlined,
  ShopOutlined,
  TaobaoCircleOutlined,
  TikTokOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { useNavigate } from '@umijs/max';
import {
  Button,
  Card,
  Col,
  Dropdown,
  Modal,
  Row,
  Typography,
  message,
} from 'antd';
import Cookies from 'js-cookie';
import React from 'react';

export const layout = false;

const shopCards = [
  { title: 'QINKUNG旗舰店', icon: 'taobao', desc: 'qinkung旗舰店' },
  { title: 'QINKUNG轻功体育官方店', icon: 'taobao', desc: '轻功体育' },
  { title: '轻功跑步官方旗舰店', icon: 'other', desc: 'qinkung' },
  { title: 'QINKUNG轻功体育官方旗舰店', icon: 'douyin', desc: '********' },
  { title: 'QINKUNG轻功体育跑步旗舰店', icon: 'douyin', desc: '*********' },
  { title: '轻功体育服饰配件旗舰店', icon: 'douyin', desc: '*********' },
];

const HomePage: React.FC = () => {
  const userInfo = useStore((s: UserStore) => s.userInfo);
  const clearUser = useStore((s: UserStore) => s.clearUser);
  const navigate = useNavigate();

  // 账户信息弹窗
  const showAccountInfo = () => {
    Modal.info({
      title: '账户信息',
      content: (
        <div>
          <div>邮箱：{userInfo?.email}</div>
          <div>ID：{userInfo?.id}</div>
        </div>
      ),
      okText: '关闭',
    });
  };

  // 退出登录
  const handleLogout = () => {
    logger.clearUserId();

    Cookies.remove('access_token');
    clearUser();
    message.success('已退出登录');
    navigate('/login');
  };

  // 菜单点击事件
  const handleMenuClick = ({ key }: { key: string }) => {
    if (key === 'account') {
      showAccountInfo();
    } else if (key === 'logout') {
      handleLogout();
    }
  };

  return (
    <div
      className="h-screen flex flex-col bg-[#f7f8fa] overflow-hidden"
      data-oid=":p-ub8u"
    >
      {/* 顶部栏 */}
      <CustomerService data-oid="ix_hulr" />
      <div
        className="h-14 bg-white border-b border-[#f0f0f0] flex items-center justify-between px-8 flex-shrink-0"
        data-oid="cw9c0bf"
      >
        <div className="font-bold text-5xl text-[#222]" data-oid="1v4_8-i">
          易康无忧智能客服
        </div>
        <Dropdown
          menu={{
            items: [
              { key: 'account', label: '账户信息' },
              { key: 'logout', label: '退出登录' },
            ],
            onClick: handleMenuClick,
          }}
          placement="bottomRight"
          data-oid="9jivh1y"
        >
          <Button
            icon={<UserOutlined data-oid=":2lulb:" />}
            className="font-medium"
            data-oid="fldbitd"
          >
            {userInfo?.email || '用户'} <DownOutlined data-oid="o4rhm_u" />
          </Button>
        </Dropdown>
      </div>
      {/* 内容区 */}
      <div
        className="flex-1  max-w-[1200px] mx-auto mt-8 bg-transparent"
        data-oid="80g.1us"
      >
        <Typography.Title level={4} className="mb-6" data-oid="zex34pj">
          我的产品
        </Typography.Title>
        <Row gutter={[24, 24]} data-oid="2eyn1ui">
          {shopCards.map((item) => (
            <Col span={8} key={item.title} data-oid="lcvxlm8">
              <Card
                hoverable
                className="rounded-lg min-h-20"
                onClick={() => navigate('/shop/shop-home')}
                data-oid="_oc9iq3"
              >
                <div className="flex items-center" data-oid="a23pbmg">
                  <div
                    className="w-10 h-10 bg-[#f5f5f5] rounded-lg flex items-center justify-center mr-4"
                    data-oid="ulxjbj_"
                  >
                    {item.icon === 'taobao' && (
                      <TaobaoCircleOutlined
                        className="text-7xl text-[#ff5000]"
                        data-oid="n94keqm"
                      />
                    )}
                    {item.icon === 'douyin' && (
                      <TikTokOutlined
                        className="text-7xl text-black"
                        data-oid="-p-.kfo"
                      />
                    )}
                    {item.icon === 'other' && (
                      <ShopOutlined
                        className="text-7xl text-red-600"
                        data-oid="t5463ja"
                      />
                    )}
                  </div>
                  <div data-oid="zwtu5ad">
                    <div className="font-medium text-base" data-oid="u1g_m-3">
                      {item.title}
                    </div>
                    <div className="text-[#888] text-sm" data-oid="rjy-bc1">
                      {item.desc}
                    </div>
                  </div>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    </div>
  );
};

export default HomePage;
