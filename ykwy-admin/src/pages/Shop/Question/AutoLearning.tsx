import { QuestionCircleOutlined, SearchOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Card, Input, Progress, Table, Tabs, Tooltip } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { useState } from 'react';
import AIReview from '../../../components/Question/AIReview';
import VagueQuestions from '../../../components/Question/VagueQuestions';

const { TabPane } = Tabs;

// 模拟数据
interface LearningItem {
  key: string;
  id: string;
  title: string;
  currentScore: number;
  totalScore: number;
  objectives: string[];
  image: string;
}

// 已通过项目数据接口
interface ApprovedItem {
  key: string;
  id: string;
  title: string;
  image: string;
  industryTag: string;
  questionFormat: string;
  reviewer: string;
  reviewTime: string;
}

// 不再学习问题数据接口
interface RejectedItem {
  key: string;
  question: string;
}

const learningItems: LearningItem[] = [
  {
    key: '1',
    id: '100765058017',
    title:
      'QINKUNG【智能问答】3支可可奶昔+A/B杯2支+护肤套装+洗面奶+护手霜【套装】',
    currentScore: 0,
    totalScore: 7,
    objectives: ['1. 掌握产品介绍', '2. 明确提供内容了'],
    image:
      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',
  },
  {
    key: '2',
    id: '101380561647',
    title:
      'QINKUNG设计2022年科技感单肩包+配饰+护肤品+口红+护手霜+洗面奶【套装】',
    currentScore: 0,
    totalScore: 7,
    objectives: ['1. 什么是科技'],
    image:
      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',
  },
  {
    key: '3',
    id: '100765058063',
    title:
      'QINKUNG设计2022年科技感单肩包+配饰+护肤品+口红+护手霜+洗面奶【套装】',
    currentScore: 0,
    totalScore: 7,
    objectives: ['1. 什么是科技'],
    image:
      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',
  },
  {
    key: '4',
    id: '101908930533',
    title:
      'QINKUNG【智能问答】3支可可奶昔+A/B杯2支+护肤套装+洗面奶+护手霜【套装】',
    currentScore: 0,
    totalScore: 2,
    objectives: ['1. 掌握产品介绍'],
    image:
      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',
  },
  {
    key: '5',
    id: '101561590324',
    title:
      'QINKUNG设计2022年科技感单肩包+配饰+护肤品+口红+护肤品+洗面奶【套装】',
    currentScore: 0,
    totalScore: 4,
    objectives: ['1. 什么是科技'],
    image:
      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',
  },
  {
    key: '6',
    id: '101398600730',
    title:
      'QINKUNG设计2022年科技感单肩包+配饰+护肤品+口红+护肤品+洗面奶【套装】',
    currentScore: 1,
    totalScore: 4,
    objectives: ['1. 掌握产品介绍', '2. 掌握产品卖点'],
    image:
      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',
  },
  {
    key: '7',
    id: '101320603165',
    title:
      'QINKUNG设计2022年科技感单肩包+配饰+护肤品+口红+护肤品+洗面奶【套装】',
    currentScore: 0,
    totalScore: 2,
    objectives: ['1. 掌握产品介绍'],
    image:
      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',
  },
  {
    key: '8',
    id: '101398601529',
    title:
      'QINKUNG【智能问答】3支可可奶昔+A/B杯2支+护肤套装+洗面奶+护手霜【套装】',
    currentScore: 0,
    totalScore: 2,
    objectives: ['1. 掌握产品介绍'],
    image:
      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',
  },
  {
    key: '9',
    id: '101320603154',
    title:
      'QINKUNG设计2022年科技感单肩包+配饰+护肤品+口红+护肤品+洗面奶【套装】',
    currentScore: 0,
    totalScore: 2,
    objectives: ['1. 掌握产品介绍'],
    image:
      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',
  },
  {
    key: '10',
    id: '101528100513',
    title:
      'QINKUNG【智能问答】3支可可奶昔+A/B杯2支+护肤套装+洗面奶+护手霜【套装】',
    currentScore: 0,
    totalScore: 2,
    objectives: ['1. 掌握产品介绍'],
    image:
      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',
  },
  {
    key: '11',
    id: '101528100514',
    title:
      'QINKUNG设计2022年科技感单肩包+配饰+护肤品+口红+护肤品+洗面奶【套装】',
    currentScore: 0,
    totalScore: 2,
    objectives: ['1. 掌握产品介绍'],
    image:
      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',
  },
  {
    key: '12',
    id: '101528100515',
    title:
      'QINKUNG【智能问答】3支可可奶昔+A/B杯2支+护肤套装+洗面奶+护手霜【套装】',
    currentScore: 0,
    totalScore: 2,
    objectives: ['1. 掌握产品介绍'],
    image:
      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',
  },
];

// 已通过项目模拟数据
const approvedItems: ApprovedItem[] = [
  {
    key: '1',
    id: '101320622154',
    title:
      'QINKUNG【豪华版货源】蓝白女装道服AIR白芯9小岁事约与朝底苍之当吧编手对练【男款】黑色L',
    image:
      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',
    industryTag: '面料面料整理',
    questionFormat: '1. 155mm我买下了',
    reviewer: 'QINKUNG官方旗舰店 QINKUNG',
    reviewTime: '2024-07-24 15:27:07',
  },
  {
    key: '2',
    id: '101320622154',
    title:
      'QINKUNG【豪华版货源】蓝白女装道服AIR白芯9小岁事约与朝底苍之当吧编手对练【男款】黑色L',
    image:
      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',
    industryTag: '适合有内衬',
    questionFormat: '1. 适合有内衬',
    reviewer: 'QINKUNG官方旗舰店 QINKUNG',
    reviewTime: '2024-07-24 15:26:51',
  },
  {
    key: '3',
    id: '101070565697',
    title:
      'QINKUNG经典为手不页编理与总经QINKLING经典外设面编过最近手了子约的灰色了, 明装来这去',
    image:
      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',
    industryTag: '黄口罩质',
    questionFormat: '1. 有寸生化罩质',
    reviewer: 'QINKUNG官方旗舰店 QINKUNG',
    reviewTime: '2024-07-24 15:26:23',
  },
  {
    key: '4',
    id: '100956830462',
    title:
      'QINKUNG【豪华版货源】蓝白女装道服AIR白芯9小岁事约与朝底苍之当吧编手对练【男款】蓝架M',
    image:
      'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',
    industryTag: '黄口罩质',
    questionFormat: '1. 还有面料是哪的了',
    reviewer: 'QINKUNG官方旗舰店 QINKUNG',
    reviewTime: '2024-05-22 15:42:26',
  },
];

// 不再学习问题模拟数据
const rejectedItems: RejectedItem[] = [
  {
    key: '1',
    question: '有什么区别，哪款比较好',
  },
  {
    key: '2',
    question: '请求推荐商品',
  },
];

const AutoLearning: React.FC = () => {
  const [activeMainTab, setActiveMainTab] = useState('auto-learning');
  const [activeContentTab, setActiveContentTab] = useState('content-search');
  const [searchValue, setSearchValue] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // 待审核表格列定义
  const pendingColumns: ColumnsType<LearningItem> = [
    {
      title: '商品名称',
      dataIndex: 'title',
      key: 'title',
      width: '45%',
      render: (text: string, record: LearningItem) => (
        <div className="flex items-start gap-3" data-oid="w3qcyx3">
          <div
            className="w-10 h-10 bg-gray-200 rounded flex-shrink-0 overflow-hidden"
            data-oid="dyfagpc"
          >
            <img
              src={record.image}
              alt="商品图片"
              className="w-full h-full object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src =
                  'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0xMiAxMkgyOFYyOEgxMlYxMloiIGZpbGw9IiNEOUQ5RDkiLz4KPC9zdmc+';
              }}
              data-oid="rshxvg0"
            />
          </div>
          <div className="flex-1" data-oid="6ggdm:g">
            <div
              className="text-sm text-gray-800 leading-5 mb-1"
              style={{
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
              }}
              data-oid="p43hv79"
            >
              {text}
            </div>
            <div className="text-xs text-gray-500" data-oid="73lsfoh">
              商品ID: {record.id}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: '有效应答 / 咨询总次数',
      dataIndex: 'score',
      key: 'score',
      width: '20%',
      align: 'center',
      render: (_, record: LearningItem) => (
        <div className="text-center" data-oid="7_85.5b">
          <div className="text-sm text-gray-800 mb-2" data-oid="kobaka0">
            {record.currentScore}/{record.totalScore}
          </div>
          <Progress
            percent={
              record.totalScore > 0
                ? Math.round((record.currentScore / record.totalScore) * 100)
                : 0
            }
            size="small"
            strokeColor="#1890ff"
            className="w-16"
            data-oid="je79k0n"
          />
        </div>
      ),
    },
    {
      title: '已学习问题',
      dataIndex: 'objectives',
      key: 'objectives',
      width: '25%',
      render: (objectives: string[]) => (
        <div className="space-y-1" data-oid="d8evvg2">
          {objectives.map((objective, index) => (
            <div
              key={index}
              className="text-sm text-gray-700"
              data-oid="v.er6ne"
            >
              {objective}
            </div>
          ))}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: '10%',
      align: 'center',
      fixed: 'right',
      render: () => (
        <Button
          type="link"
          className="text-blue-500 p-0 text-sm"
          data-oid="_00zzg_"
        >
          立即审核
        </Button>
      ),
    },
  ];

  // 已通过表格列定义
  const approvedColumns: ColumnsType<ApprovedItem> = [
    {
      title: '商品名称/商品分类',
      dataIndex: 'title',
      key: 'title',
      width: '35%',
      render: (text: string, record: ApprovedItem) => (
        <div className="flex items-start gap-3" data-oid="4i7w3_3">
          <div
            className="w-10 h-10 bg-gray-200 rounded flex-shrink-0 overflow-hidden"
            data-oid="_q9a8jz"
          >
            <img
              src={record.image}
              alt="商品图片"
              className="w-full h-full object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src =
                  'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0xMiAxMkgyOFYyOEgxMlYxMloiIGZpbGw9IiNEOUQ5RDkiLz4KPC9zdmc+';
              }}
              data-oid="ddk_kzr"
            />
          </div>
          <div className="flex-1" data-oid="szsr6vk">
            <div
              className="text-sm text-gray-800 leading-5 mb-1"
              style={{
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
              }}
              data-oid="gjyxz3:"
            >
              {text}
            </div>
            <div className="text-xs text-gray-500" data-oid="mjx_xv.">
              商品ID: {record.id}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: '行业标签',
      dataIndex: 'industryTag',
      key: 'industryTag',
      width: '15%',
      render: (text: string) => (
        <span className="text-sm text-gray-600" data-oid="wpsqo2r">
          {text}
        </span>
      ),
    },
    {
      title: '问法',
      dataIndex: 'questionFormat',
      key: 'questionFormat',
      width: '20%',
      render: (text: string) => (
        <span className="text-sm text-gray-600" data-oid="_.ub3hp">
          {text}
        </span>
      ),
    },
    {
      title: '审核负责人',
      dataIndex: 'reviewer',
      key: 'reviewer',
      width: '15%',
      render: (text: string) => (
        <span className="text-sm text-gray-600" data-oid="l75bsig">
          {text}
        </span>
      ),
    },
    {
      title: '审核时间',
      dataIndex: 'reviewTime',
      key: 'reviewTime',
      width: '10%',
      render: (text: string) => (
        <span className="text-sm text-gray-600" data-oid="4ni1ei9">
          {text}
        </span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: '5%',
      align: 'center',
      fixed: 'right',
      render: () => (
        <div className="flex gap-2" data-oid="6kmmyki">
          <Button
            type="link"
            className="text-blue-500 p-0 text-sm"
            data-oid="mpi5rd0"
          >
            会话详情
          </Button>
          <Button
            type="link"
            className="text-blue-500 p-0 text-sm"
            data-oid="mch383:"
          >
            查看答案
          </Button>
        </div>
      ),
    },
  ];

  // 不再学习问题表格列定义
  const rejectedColumns: ColumnsType<RejectedItem> = [
    {
      title: '问题',
      dataIndex: 'question',
      key: 'question',
      width: '90%',
      render: (text: string) => (
        <span className="text-sm text-gray-800" data-oid="17ls55.">
          {text}
        </span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: '10%',
      align: 'center',
      fixed: 'right',
      render: () => (
        <Button
          type="link"
          className="text-blue-500 p-0 text-sm"
          data-oid="vqam976"
        >
          重新关注
        </Button>
      ),
    },
  ];

  // 根据当前内容标签页获取对应的表格配置
  const getTableConfig = () => {
    switch (activeContentTab) {
      case 'content-search':
        return {
          columns: pendingColumns,
          dataSource: learningItems,
          placeholder: '输入关键词搜索问题',
        };
      case 'approved':
        return {
          columns: approvedColumns,
          dataSource: approvedItems,
          placeholder: '输入关键词搜索问题',
        };
      case 'rejected':
        return {
          columns: rejectedColumns,
          dataSource: rejectedItems,
          placeholder: '输入人员问题',
        };
      default:
        return {
          columns: pendingColumns,
          dataSource: learningItems,
          placeholder: '输入关键词搜索问题',
        };
    }
  };

  // 渲染自动学习页面内容
  const renderAutoLearningContent = () => {
    const tableConfig = getTableConfig();

    return (
      <div className=" md:px-6 pb-6" data-oid="31l3rui">
        <Card data-oid="go6gkix">
          {/* 内容标签页和搜索 */}
          <div
            className="flex flex-col md:flex-row md:items-center md:justify-between mb-4 gap-4"
            data-oid="t6lc9tf"
          >
            <Tabs
              activeKey={activeContentTab}
              onChange={setActiveContentTab}
              className="[&_.ant-tabs-tab]:px-4 [&_.ant-tabs-tab]:py-2 [&_.ant-tabs-tab]:text-sm [&_.ant-tabs-tab-active]:text-blue-500 [&_.ant-tabs-ink-bar]:bg-blue-500 [&_.ant-tabs-ink-bar]:h-0.5"
              data-oid="ys3mfg6"
            >
              <TabPane tab="待审核" key="content-search" data-oid="k748i.f" />
              <TabPane tab="已通过" key="approved" data-oid="lgu1yj:" />
              <TabPane tab="不再学习问题" key="rejected" data-oid="7fl7u-m" />
            </Tabs>

            <div className="flex items-center gap-3" data-oid="e-lr-1o">
              <Input
                placeholder={tableConfig.placeholder}
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                prefix={
                  <SearchOutlined
                    className="text-gray-400"
                    data-oid="4m6t-0_"
                  />
                }
                className="w-full md:w-64"
                allowClear
                data-oid="i512ufv"
              />

              {activeContentTab === 'content-search' && (
                <Button
                  type="primary"
                  className="bg-blue-500 whitespace-nowrap"
                  data-oid=":9b81x_"
                >
                  立即审核
                </Button>
              )}
            </div>
          </div>

          {/* 学习项目表格 */}
          <div
            className="overflow-x-auto overflow-y-auto w-full px-2 flex-grow"
            data-oid="gh_a:qt"
          >
            <Table
              columns={tableConfig.columns as any}
              dataSource={tableConfig.dataSource as any}
              pagination={{
                current: currentPage,
                pageSize: pageSize,
                total: tableConfig.dataSource.length,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
                onChange: (page, size) => {
                  setCurrentPage(page);
                  setPageSize(size || 10);
                },
                onShowSizeChange: (current, size) => {
                  setCurrentPage(1);
                  setPageSize(size);
                },
                pageSizeOptions: ['10', '20', '50', '100'],
                responsive: true,
              }}
              className="[&_.ant-table-thead>tr>th]:bg-gray-50 [&_.ant-table-thead>tr>th]:font-medium [&_.ant-table-thead>tr>th]:text-gray-700 [&_.ant-table-tbody>tr:hover>td]:bg-blue-50"
              size="middle"
              bordered={false}
              scroll={{ x: 'max-content', y: 'calc(100vh - 480px)' }}
              data-oid="tsxltj-"
            />
          </div>
        </Card>
      </div>
    );
  };

  // 渲染AI推荐答案审核页面内容
  const renderAIReviewContent = () => <AIReview data-oid="-ko:_ut" />;

  // 渲染问法模糊的问题页面内容
  const renderVagueQuestionsContent = () => (
    <VagueQuestions data-oid="xgyh0l1" />
  );

  // 根据选中的主标签页渲染不同内容
  const renderContentByTab = () => {
    switch (activeMainTab) {
      case 'auto-learning':
        return renderAutoLearningContent();
      case 'ai-review':
        return renderAIReviewContent();
      case 'model-training':
        return renderVagueQuestionsContent();
      default:
        return renderAutoLearningContent();
    }
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5] w-full" data-oid="w1je2wa">
      <div className="w-full h-full" data-oid=":njwlbt">
        {/* 主要标签页 */}
        <div className="mb-6 px-4 md:px-6 pt-4" data-oid="bxf8:_0">
          <Tabs
            activeKey={activeMainTab}
            onChange={setActiveMainTab}
            className="[&_.ant-tabs-tab]:px-6 [&_.ant-tabs-tab]:py-3 [&_.ant-tabs-tab]:text-base [&_.ant-tabs-tab]:font-medium [&_.ant-tabs-tab-active]:text-blue-500 [&_.ant-tabs-ink-bar]:bg-blue-500 [&_.ant-tabs-ink-bar]:h-0.5"
            data-oid="1-5azgi"
          >
            <TabPane tab="自动学习" key="auto-learning" data-oid="22clfnl" />
            <TabPane
              tab={
                <span data-oid="lhevczn">
                  AI推荐答案审核
                  <span
                    className="ml-1 bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full"
                    data-oid="rtw.4jt"
                  >
                    HOT
                  </span>
                </span>
              }
              key="ai-review"
              data-oid=".3rakgo"
            />

            <TabPane
              tab="问法模糊的问题"
              key="model-training"
              data-oid="46z0h1h"
            />
          </Tabs>
        </div>

        {/* 统计卡片区域 - 只在自动学习页面显示 */}
        {activeMainTab === 'auto-learning' && (
          <div
            className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 mb-6 px-4 md:px-6"
            data-oid="7s2pnoo"
          >
            {/* 机器人自动学习卡片 */}
            <Card className="col-span-1" data-oid=".k58025">
              <div className="flex items-center" data-oid="9_edg93">
                <div
                  className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mr-4"
                  data-oid="gd5nlum"
                >
                  <span
                    className="text-white text-xl font-bold"
                    data-oid="mindhog"
                  >
                    AI
                  </span>
                </div>
                <div data-oid="l.4kxei">
                  <div
                    className="text-lg font-semibold text-gray-800 mb-1"
                    data-oid="hh3bpe5"
                  >
                    机器人自动学习
                  </div>
                  <div className="text-sm text-gray-600" data-oid="k3:qzxe">
                    机器人自动学习客服应答，并通过分析
                    咨询频次，为您推荐最值得审核的内容
                  </div>
                </div>
              </div>
            </Card>

            {/* 累计自动学习卡片 */}
            <Card className="col-span-1" data-oid="a4u:_-g">
              <div className="text-center" data-oid="_uu72so">
                <div
                  className="text-3xl font-bold text-gray-800 mb-2"
                  data-oid="62iplvv"
                >
                  4.00
                </div>
                <div
                  className="text-sm text-gray-600 flex items-center justify-center gap-1"
                  data-oid="s4lsz0-"
                >
                  累计添加答案
                  <Tooltip title="累计添加答案" data-oid="p1cqlj8">
                    <QuestionCircleOutlined
                      className="text-gray-400 text-xs cursor-help"
                      data-oid=":xkp8xt"
                    />
                  </Tooltip>
                </div>
              </div>
            </Card>

            {/* 累计平均学习时长卡片 */}
            <Card className="col-span-1" data-oid="p144w5j">
              <div className="text-center" data-oid="_0e7spm">
                <div
                  className="text-3xl font-bold text-gray-800 mb-2"
                  data-oid="w97_96-"
                >
                  2.0
                </div>
                <div
                  className="text-sm text-gray-600 flex items-center justify-center gap-1"
                  data-oid="b67itaa"
                >
                  累计节约配置时间（分钟）
                  <Tooltip title="按照每条答案大约30秒" data-oid="h-tljy0">
                    <QuestionCircleOutlined
                      className="text-gray-400 text-xs cursor-help"
                      data-oid="ho7cjg1"
                    />
                  </Tooltip>
                </div>
              </div>
            </Card>
          </div>
        )}

        {/* 内容区域 - 根据选中的主标签页显示不同内容 */}
        {renderContentByTab()}
      </div>
    </div>
  );
};

export default AutoLearning;
