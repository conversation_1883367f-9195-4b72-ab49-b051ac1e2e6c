import { QuestionKnowledgeList, SearchHeader } from '@/components/Question/Q&A';
import type { Answer, KnowledgeCategory } from '@/models/questionAndAnswer';
import type {
  QuestionConfig,
  QuestionGroup,
} from '@/models/questionAndAnswerUI';
import { getKnowledgeCategoryTree } from '@/services/knowledgeCategory';
import { upsertKnowledgeBase } from '@/services/questionAndAnswerKnowledgeBase';
import { PageContainer } from '@ant-design/pro-components';
import { Card, message } from 'antd';
import React, { useEffect, useState } from 'react';

const Knowledge: React.FC = () => {
  const [categoryTree, setCategoryTree] = useState<KnowledgeCategory[]>([]);
  const [activeTab, setActiveTab] = useState('list');
  const [questionGroups, setQuestionGroups] = useState<QuestionGroup[]>([]);
  const [loading, setLoading] = useState(false);

  // 模拟数据 - 基于实际数据库模型
  const mockQuestionGroups: QuestionGroup[] = [
    {
      id: '1',
      title: '指定发货物流',
      categoryName: '物流配送',
      categoryCode: '1.1',
      orderStatus: '1100', // 售前、发货前激活
      commonQuestionSamples: ['发什么快递', '用什么物流', '快递公司是哪家'],
      answers: [
        {
          id: 'a1',
          content:
            'QK为了给友友们更好更快的体验，大部分地区发顺丰快递，其他部分地区发其他快递，部分偏远地区运费或其他特殊情况会从顺丰转投其他快递站点，请完善收货地址后才能在后台看到对应运费金额；满足顺丰条件的会优先选择顺丰。',
          replyOrder: 1,
          questionAndAnswerId: '1',
        },
      ],
      isCustom: false,
      matchMode: 'keyword',
      isPermanent: true,
      timeValidityLabel: '永久有效',
      weeklyConsultationVolume: 120,
      relatedProductsCount: 12,
      answersCount: 1,
      isExpanded: true,
    },
    {
      id: '2',
      title: '发什么快递',
      categoryName: '物流配送',
      categoryCode: '1.1',
      orderStatus: '1111', // 全部状态激活
      commonQuestionSamples: ['快递公司', '什么快递', '用的哪家快递'],
      answers: [
        {
          id: 'a2',
          content: '您好，稍等一下哦，这边为您查看哦~',
          replyOrder: 1,
          questionAndAnswerId: '2',
        },
      ],
      isCustom: false,
      matchMode: 'full',
      isPermanent: true,
      timeValidityLabel: '永久有效',
      weeklyConsultationVolume: 75,
      relatedProductsCount: 5,
      answersCount: 1,
    },
    {
      id: '3',
      title: '电子发票如何查看',
      categoryName: '发票服务',
      categoryCode: '2.1',
      orderStatus: '0001', // 只有售后激活
      commonQuestionSamples: ['发票在哪', '电子发票怎么看', '发票查看'],
      answers: [
        {
          id: 'a3',
          content:
            '亲友，目前支持开具：增值税普通发票选择，提供收货用户查看，提供您的发票查看券，发票尊号尝试个收件，或提高其发票查看券以及发票查看的技术内容：正常在客服数据记录6-7-15个工作日开具。',
          replyOrder: 1,
          questionAndAnswerId: '3',
        },
      ],
      isCustom: false,
      matchMode: 'keyword',
      isPermanent: true,
      timeValidityLabel: '永久有效',
      weeklyConsultationVolume: 98,
      relatedProductsCount: 7,
      answersCount: 1,
    },
    {
      id: '4',
      title: 'QINKUN产品咨询',
      categoryName: '商品咨询',
      categoryCode: '3.1',
      orderStatus: '1110', // 售前、发货前、发货后激活
      commonQuestionSamples: ['QINKUN怎么样', '这个产品好用吗', '效果如何'],
      productId: 'product-001',
      productName: 'QINKUN美白精华液',
      answers: [
        {
          id: 'a4',
          content:
            '您好！感谢您对QINKUN产品的关注。这款美白精华液采用先进配方，温和有效，适合各种肌肤使用。建议您先了解自己的肌肤状况，我们会为您推荐最合适的使用方法。',
          replyOrder: 1,
          questionAndAnswerId: '4',
        },
        {
          id: 'a5',
          content:
            '如需了解更多产品详情，请咨询我们的专业客服，我们会根据您的具体需求提供个性化建议。',
          replyOrder: 2,
          questionAndAnswerId: '4',
        },
      ],
      isCustom: true,
      matchMode: 'keyword',
      isPermanent: false,
      timeValidityLabel: '工作日有效',
      weeklyConsultationVolume: 156,
      relatedProductsCount: 1,
      answersCount: 2,
    },
    // 添加更多模拟数据以测试分页
    ...Array.from({ length: 15 }, (_, index) => ({
      id: `mock-${index + 5}`,
      title: `模拟问题 ${index + 5}`,
      categoryName: '测试分类',
      categoryCode: '4.1',
      orderStatus: '1000',
      commonQuestionSamples: [`测试问法 ${index + 1}`, `测试问法 ${index + 2}`],
      answers: [
        {
          id: `answer-${index + 5}`,
          content: `这是第 ${
            index + 5
          } 个模拟问题的回答内容，用于测试分页功能。`,
          replyOrder: 1,
          questionAndAnswerId: `mock-${index + 5}`,
        },
      ],
      isCustom: index % 2 === 0,
      matchMode: 'keyword',
      isPermanent: true,
      timeValidityLabel: '永久有效',
      weeklyConsultationVolume: Math.floor(Math.random() * 200) + 50,
      relatedProductsCount: Math.floor(Math.random() * 10) + 1,
      answersCount: 1,
    })),
  ];

  useEffect(() => {
    getKnowledgeCategoryTree().then((res) => {
      if (res.code === 200 && Array.isArray(res.data)) {
        setCategoryTree(res.data);
      }
    });

    // 设置模拟数据
    setQuestionGroups(mockQuestionGroups);
  }, []);

  const handleEdit = (groupId: string, answerId: string) => {
    message.info(`编辑回答: ${answerId} 在问题组: ${groupId}`);
    // 这里可以打开编辑模态框或跳转到编辑页面
  };

  const handleDelete = (groupId: string, answerId: string) => {
    message.success(`删除回答: ${answerId}`);
    setQuestionGroups((prev) =>
      prev.map((group) =>
        group.id === groupId
          ? {
              ...group,
              answers: group.answers.filter((answer) => answer.id !== answerId),
            }
          : group,
      ),
    );
  };

  const handleCopyCondition = (groupId: string, answerId: string) => {
    message.info(`复制条件: ${answerId}`);
    // 这里可以实现复制条件的逻辑
  };

  const handleAddAnswer = async (groupId: string) => {
    try {
      const group = questionGroups.find((g) => g.id === groupId);
      if (!group) {
        message.error('未找到对应的问题组');
        return;
      }

      const maxReplyOrder = Math.max(
        ...(group?.answers.map((a) => a.replyOrder) || [0]),
      );

      const newAnswer: Answer = {
        id: `new-${Date.now()}`,
        content: '请输入新的回答内容...',
        replyOrder: maxReplyOrder + 1,
        questionAndAnswerId: groupId,
      };

      // 使用 upsertKnowledgeBase 添加新回答
      await upsertKnowledgeBase({
        id: groupId,
        questionType: group.title,
        orderStatus: group.orderStatus,
        categoryCode: group.categoryCode,
        commonQuestionSamples: group.commonQuestionSamples,
        answers: [
          ...group.answers.map((answer) => ({
            content: answer.content,
            replyOrder: answer.replyOrder,
          })),
          {
            content: newAnswer.content,
            replyOrder: newAnswer.replyOrder,
          },
        ],
        isCustom: group.isCustom,
        matchMode: group.matchMode,
        isPermanent: group.isPermanent,
        productId: group.productId,
      });

      // 更新本地状态
      setQuestionGroups((prev) =>
        prev.map((group) =>
          group.id === groupId
            ? { ...group, answers: [...group.answers, newAnswer] }
            : group,
        ),
      );
      message.success('添加新回答成功');
    } catch (error) {
      console.error('添加新回答失败:', error);
      message.error('添加新回答失败');
    }
  };

  const handleEditQuestion = (groupId: string) => {
    message.info(`编辑问题: ${groupId}`);
    // 这里可以打开问题编辑模态框
  };

  const handleDeleteQuestion = (groupId: string) => {
    message.success(`删除问题: ${groupId}`);
    setQuestionGroups((prev) => prev.filter((group) => group.id !== groupId));
  };

  const handleUpdateContent = async (
    groupId: string,
    answerId: string,
    newContent: string,
  ) => {
    try {
      // 找到当前问题组
      const currentGroup = questionGroups.find((group) => group.id === groupId);
      if (!currentGroup) {
        message.error('未找到对应的问题组');
        return;
      }

      // 更新回答内容
      const updatedAnswers = currentGroup.answers.map((answer) =>
        answer.id === answerId ? { ...answer, content: newContent } : answer,
      );

      // 使用 upsertKnowledgeBase 更新回答
      await upsertKnowledgeBase({
        id: groupId,
        questionType: currentGroup.title,
        orderStatus: currentGroup.orderStatus,
        categoryCode: currentGroup.categoryCode,
        commonQuestionSamples: currentGroup.commonQuestionSamples,
        answers: updatedAnswers.map((answer) => ({
          content: answer.content,
          replyOrder: answer.replyOrder,
        })),
        isCustom: currentGroup.isCustom,
        matchMode: currentGroup.matchMode,
        isPermanent: currentGroup.isPermanent,
        productId: currentGroup.productId,
      });

      // 更新本地状态
      setQuestionGroups((prev) =>
        prev.map((group) =>
          group.id === groupId
            ? {
                ...group,
                answers: updatedAnswers,
              }
            : group,
        ),
      );
      message.success('回答内容已更新');
    } catch (error) {
      console.error('更新回答内容失败:', error);
      message.error('更新回答内容失败');
    }
  };

  const handleConfigChange = async (
    groupId: string,
    newConfig: QuestionConfig,
  ) => {
    try {
      // 找到当前问题组
      const currentGroup = questionGroups.find((group) => group.id === groupId);
      if (!currentGroup) {
        message.error('未找到对应的问题组');
        return;
      }

      // 使用 upsertKnowledgeBase 更新订单状态
      await upsertKnowledgeBase({
        id: groupId,
        questionType: currentGroup.title,
        orderStatus: newConfig.orderStatus,
        categoryCode: currentGroup.categoryCode,
        commonQuestionSamples: currentGroup.commonQuestionSamples,
        answers: currentGroup.answers.map((answer) => ({
          content: answer.content,
          replyOrder: answer.replyOrder,
        })),
        isCustom: currentGroup.isCustom,
        matchMode: currentGroup.matchMode,
        isPermanent: currentGroup.isPermanent,
        productId: currentGroup.productId,
      });

      // 更新本地状态
      setQuestionGroups((prev) =>
        prev.map((group) =>
          group.id === groupId
            ? {
                ...group,
                orderStatus: newConfig.orderStatus,
              }
            : group,
        ),
      );
      message.success('配置已更新');
    } catch (error) {
      console.error('更新配置失败:', error);
      message.error('更新配置失败');
    }
  };

  const handleSearch = (values: Record<string, unknown>) => {
    setLoading(true);
    console.log('搜索参数:', values);
    // 模拟搜索
    setTimeout(() => {
      setLoading(false);
      message.success('搜索完成');
    }, 1000);
  };

  return (
    <div style={{ minHeight: '100%', overflow: 'auto' }}>
      <PageContainer
        header={{
          title: '问答知识库',
        }}
        tabList={[
          {
            tab: '行业场景',
            key: 'list',
          },
          {
            tab: '自定义问题',
            key: 'custom',
          },
        ]}
        breadcrumbRender={false}
        tabActiveKey={activeTab}
        onTabChange={setActiveTab}
      >
        {activeTab === 'list' && (
          <div className="space-y-4">
            {/* 搜索区域 - 跟随滚动 */}
            <SearchHeader
              categoryTree={categoryTree}
              loading={loading}
              onSearch={handleSearch}
            />

            {/* 问答列表 */}
            <QuestionKnowledgeList
              data={questionGroups}
              loading={loading}
              onEdit={handleEdit}
              onDelete={handleDelete}
              onCopyCondition={handleCopyCondition}
              onAddAnswer={handleAddAnswer}
              onEditQuestion={handleEditQuestion}
              onDeleteQuestion={handleDeleteQuestion}
              onUpdateContent={handleUpdateContent}
              onConfigChange={handleConfigChange}
            />
          </div>
        )}

        {activeTab === 'custom' && (
          <Card>
            <div className="text-center py-8 text-gray-500">
              自定义问题功能开发中...
            </div>
          </Card>
        )}
      </PageContainer>
    </div>
  );
};

export default Knowledge;
