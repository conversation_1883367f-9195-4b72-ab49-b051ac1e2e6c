import {
  QuestionDetailDrawer,
  QuestionEditModal,
  QuestionStatsDrawer,
} from '@/components/Question';
import ImportQAKnowledgeModal from '@/components/Question/ImportQAKnowledgeModal';
import type {
  KnowledgeCategorySimple,
  QuestionAndAnswer,
  QuestionAndAnswerInput,
  QuestionAndAnswerQueryParams,
  QuestionAndAnswerStats,
} from '@/models/questionAndAnswer';
import { getOrderStatusLabel } from '@/models/questionAndAnswer';
import {
  getAvailableCategories,
  getCategoryCodeNameMap,
} from '@/services/knowledgeCategory';
import {
  bulkDeleteQuestionAndAnswers,
  deleteQuestionAndAnswer,
  getQuestionAndAnswerList,
  getQuestionAndAnswerStats,
  syncToRagflow,
  upsertQuestionAndAnswer,
} from '@/services/questionAndAnswer';
import {
  BarChartOutlined,
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  InboxOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import {
  ActionType,
  ProColumns,
  ProFormSelect,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Input, message, Popconfirm, Tag, Tooltip } from 'antd';
import React, { useCallback, useEffect, useRef, useState } from 'react';

const KnowledgeSimple: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [detailDrawerVisible, setDetailDrawerVisible] = useState(false);
  const [statsDrawerVisible, setStatsDrawerVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<QuestionAndAnswer | null>(
    null,
  );
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [categories, setCategories] = useState<KnowledgeCategorySimple[]>([]);
  const [categoryMap, setCategoryMap] = useState<Record<string, string>>({});
  const [stats, setStats] = useState<QuestionAndAnswerStats | null>(null);
  const [importModalVisible, setImportModalVisible] = useState(false);

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      const [categoriesRes, categoryMapRes] = await Promise.all([
        getAvailableCategories(),
        getCategoryCodeNameMap(),
      ]);

      if (categoriesRes.code === 200) {
        setCategories(categoriesRes.data || []);
      }

      if (categoryMapRes.code === 200) {
        setCategoryMap(categoryMapRes.data || {});
      }
    } catch (error) {
      console.error('获取分类数据失败:', error);
    }
  };

  // 获取统计信息
  const fetchStats = async () => {
    try {
      const response = await getQuestionAndAnswerStats();
      if (response.code === 200 && response.data) {
        setStats(response.data);
      }
    } catch (error) {
      console.error('获取统计信息失败:', error);
    }
  };

  // 查看详情
  const handleView = useCallback((record: QuestionAndAnswer) => {
    setCurrentRecord(record);
    setDetailDrawerVisible(true);
  }, []);

  // 编辑问答
  const handleEdit = useCallback((record: QuestionAndAnswer) => {
    setCurrentRecord(record);
    setEditModalVisible(true);
  }, []);

  // 删除问答
  const handleDelete = useCallback(async (record: QuestionAndAnswer) => {
    try {
      const response = await deleteQuestionAndAnswer(record.id);
      if (response.code === 200) {
        message.success('删除成功');
        actionRef.current?.reload();
      } else {
        message.error(response.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  }, []);

  useEffect(() => {
    fetchCategories();
  }, []);

  // 表格列定义 - 重点展示5个核心字段
  const columns: ProColumns<QuestionAndAnswer>[] = [
    {
      title: '问题类型',
      dataIndex: 'questionType',
      key: 'questionType',
      width: 160,
      ellipsis: true,
      copyable: true,
      renderFormItem: () => (
        <Input placeholder="请输入问题类型关键词搜索" data-oid="dr4tr7x" />
      ),
      render: (dom: any, record: QuestionAndAnswer) => (
        <Tooltip title={record.questionType}>
          <span style={{ fontWeight: 500 }}>{record.questionType}</span>
        </Tooltip>
      ),
    },
    {
      title: '订单状态',
      dataIndex: 'orderStatus',
      key: 'orderStatus',
      width: 120,
      renderFormItem: () => (
        <ProFormSelect
          placeholder="请选择订单状态"
          options={[
            '1000',
            '0100',
            '0010',
            '0001',
            '1100',
            '1010',
            '1001',
            '0110',
            '0101',
            '0011',
            '1110',
            '1101',
            '1011',
            '0111',
            '1111',
            '0000',
          ].map((val) => ({ label: getOrderStatusLabel(val), value: val }))}
          data-oid="order-status-select"
        />
      ),
      render: (dom: any, record: QuestionAndAnswer) => {
        const statusArr = record.orderStatus?.split('') || [];
        const labels = ['售前', '发货前', '发货后', '售后'];
        return (
          <span>
            {labels.map((label, idx) => (
              <Tag
                key={label}
                color={statusArr[idx] === '1' ? '#52c41a' : '#d9d9d9'}
                style={{ marginRight: 4, marginBottom: 2 }}
              >
                {label}
              </Tag>
            ))}
          </span>
        );
      },
    },
    {
      title: '分类',
      dataIndex: 'categoryCode',
      key: 'categoryCode',
      width: 0,
      hideInTable: true,
      renderFormItem: () => (
        <ProFormSelect
          placeholder="请选择分类"
          options={categories.map((cat) => ({
            label: cat.name,
            value: cat.code,
          }))}
          showSearch
          data-oid="category-select"
        />
      ),
    },
    {
      title: '常见问法样本',
      dataIndex: 'commonQuestionSamples',
      key: 'commonQuestionSamples',
      width: 250,
      ellipsis: true,
      search: false,
      render: (dom: any, record: QuestionAndAnswer) => {
        let samples: any = record.commonQuestionSamples || [];
        if (typeof samples === 'string') {
          try {
            if (samples.trim().startsWith('[')) {
              samples = JSON.parse(samples);
            } else {
              samples = samples
                .split(',')
                .map((s: string) => s.trim())
                .filter(Boolean);
            }
          } catch {
            samples = [samples];
          }
        }
        if (!Array.isArray(samples) || samples.length === 0) {
          return <span style={{ color: '#999' }}>暂无样本</span>;
        }
        return (
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: 4 }}>
            {samples.map((sample, idx) => (
              <Tooltip key={idx} title={sample} placement="top">
                <Tag
                  style={{
                    maxWidth: 120,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }}
                >
                  {sample}
                </Tag>
              </Tooltip>
            ))}
          </div>
        );
      },
    },
    {
      title: '回答内容',
      dataIndex: 'answers',
      key: 'answers',
      width: 300,
      ellipsis: true,
      search: false,
      render: (dom: any, record: QuestionAndAnswer) => {
        let answers: any = record.answers || [];
        if (typeof answers === 'string') {
          try {
            if (answers.trim().startsWith('[')) {
              answers = JSON.parse(answers);
            } else {
              answers = answers
                .split(',')
                .map((s: string) => s.trim())
                .filter(Boolean);
            }
          } catch {
            answers = [answers];
          }
        }
        if (!Array.isArray(answers) || answers.length === 0) {
          return <span style={{ color: '#999' }}>暂无回答</span>;
        }
        return (
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'flex-start',
              maxWidth: 300,
            }}
          >
            {answers.map((answer, idx) => (
              <Tooltip key={idx} title={answer} placement="top">
                <Tag
                  style={{
                    maxWidth: 280,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    marginBottom: 6,
                  }}
                >
                  {answer}
                </Tag>
              </Tooltip>
            ))}
          </div>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      valueType: 'dateTime',
      sorter: true,
      search: false,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 100,
      fixed: 'right',
      render: (text, record) => [
        <Tooltip key="view" title="查看详情">
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          />
        </Tooltip>,
        <Tooltip key="edit" title="编辑">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          />
        </Tooltip>,
        <Popconfirm
          key="delete"
          title="确定要删除这条问答吗？"
          onConfirm={() => handleDelete(record)}
          okText="确定"
          cancelText="取消"
        >
          <Tooltip title="删除">
            <Button type="link" danger icon={<DeleteOutlined />} />
          </Tooltip>
        </Popconfirm>,
      ],
    },
  ];

  const handleRequest = async (params: QuestionAndAnswerQueryParams) => {
    try {
      const response = await getQuestionAndAnswerList({
        current: params.current || 1,
        pageSize: params.pageSize || 10,
        questionType: params.questionType,
        orderStatus: params.orderStatus,
        categoryCode: params.categoryCode,
        includeDeleted: false,
      });

      if (response.code === 200) {
        return {
          data: response.data.items || [],
          success: true,
          total: response.data.total || 0,
        };
      } else {
        message.error(response.msg || '查询失败');
        return {
          data: [],
          success: false,
          total: 0,
        };
      }
    } catch (error) {
      console.error('查询问答列表失败:', error);
      message.error('查询失败');
      return {
        data: [],
        success: false,
        total: 0,
      };
    }
  };

  // 新增问答
  const handleAdd = () => {
    setCurrentRecord(null);
    setEditModalVisible(true);
  };

  // 保存问答
  const handleSave = async (data: QuestionAndAnswerInput) => {
    try {
      const response = await upsertQuestionAndAnswer(data);
      if (response.code === 200) {
        message.success(currentRecord ? '更新成功' : '创建成功');
        setEditModalVisible(false);
        actionRef.current?.reload();
      } else {
        message.error(response.msg || '保存失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    }
  };

  // 批量删除
  const handleBulkDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的项目');
      return;
    }

    try {
      const response = await bulkDeleteQuestionAndAnswers(
        selectedRowKeys as string[],
      );
      if (response.code === 200) {
        const { deletedCount, failedIds } = response.data;
        if (failedIds.length === 0) {
          message.success(`成功删除 ${deletedCount} 条记录`);
        } else {
          message.warning(
            `成功删除 ${deletedCount} 条记录，${failedIds.length} 条记录删除失败`,
          );
        }
        setSelectedRowKeys([]);
        actionRef.current?.reload();
      } else {
        message.error(response.msg || '批量删除失败');
      }
    } catch (error) {
      message.error('批量删除失败');
    }
  };

  // 查看统计
  const handleViewStats = async () => {
    await fetchStats();
    setStatsDrawerVisible(true);
  };

  // 同步到知识库
  const handleSyncToRagflow = async () => {
    const hide = message.loading('正在同步问答数据到知识库...', 0);
    try {
      const res = await syncToRagflow();
      hide();
      if (res.code === 0) {
        message.success('问答数据同步成功');
      } else {
        message.error(res.msg || '问答数据同步失败');
      }
    } catch (error) {
      message.error('问答数据同步失败，请稍后重试');
      hide();
    }
  };

  return (
    <div style={{ padding: 20 }}>
      <div className=" w-full p-5 flex-grow-1">
        <ProTable<QuestionAndAnswer>
          columns={columns}
          actionRef={actionRef}
          request={handleRequest}
          rowKey="id"
          search={{
            labelWidth: 'auto',
            defaultCollapsed: false,
          }}
          scroll={{ x: 1180, y: 'calc(100vh - 370px)' }}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
          dateFormatter="string"
          headerTitle="问答知识库"
          toolBarRender={() => [
            <Button
              key="import"
              icon={<InboxOutlined />}
              onClick={() => setImportModalVisible(true)}
            >
              导入
            </Button>,
            <Button
              key="stats"
              icon={<BarChartOutlined />}
              onClick={handleViewStats}
            >
              统计
            </Button>,

            <Popconfirm
              key="sync"
              title="确定要同步问答数据到知识库吗？"
              description="此操作将把当前问答数据同步到知识库系统中，请确认是否继续。"
              onConfirm={handleSyncToRagflow}
              okText="确定"
              cancelText="取消"
            >
              <Button>同步到知识库</Button>
            </Popconfirm>,
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              新增问答
            </Button>,
            selectedRowKeys.length > 0 && (
              <Popconfirm
                key="batchDelete"
                title={`确定要删除选中的 ${selectedRowKeys.length} 条记录吗？`}
                onConfirm={handleBulkDelete}
                okText="确定"
                cancelText="取消"
              >
                <Button danger icon={<DeleteOutlined />}>
                  批量删除
                </Button>
              </Popconfirm>
            ),
          ]}
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
          }}
        />
      </div>

      {/* 编辑弹窗 */}
      <QuestionEditModal
        visible={editModalVisible}
        currentRecord={currentRecord}
        categories={categories}
        onCancel={() => setEditModalVisible(false)}
        onSave={handleSave}
      />

      {/* 详情抽屉 */}
      <QuestionDetailDrawer
        visible={detailDrawerVisible}
        currentRecord={currentRecord}
        categoryMap={categoryMap}
        onClose={() => setDetailDrawerVisible(false)}
      />

      {/* 统计抽屉 */}
      <QuestionStatsDrawer
        visible={statsDrawerVisible}
        stats={stats}
        categoryMap={categoryMap}
        onClose={() => setStatsDrawerVisible(false)}
      />

      <ImportQAKnowledgeModal
        visible={importModalVisible}
        onCancel={() => setImportModalVisible(false)}
        onSuccess={() => {
          setImportModalVisible(false);
          actionRef.current?.reload();
        }}
      />
    </div>
  );
};

export default KnowledgeSimple;
