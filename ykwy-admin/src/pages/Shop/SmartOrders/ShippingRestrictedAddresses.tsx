import ImportShippingRestrictedAreaModal from '@/components/ShippingRestrictedArea/ImportShippingRestrictedAreaModal';
import type {
  ShippingRestrictedArea,
  ShippingRestrictedAreaQueryParams,
} from '@/models/shippingRestrictedArea';
import {
  getShippingRestrictedAreaList,
  syncShippingRestrictedAreaToRagflow,
  updateShippingRestrictedAreaStatus,
} from '@/services/shippingRestrictedArea';
import { InboxOutlined, SearchOutlined } from '@ant-design/icons';
import {
  Button,
  Input,
  message,
  Popconfirm,
  Select,
  Table,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';

const { Title, Text } = Typography;

const ShippingRestrictedAddresses: React.FC = () => {
  const [searchText, setSearchText] = useState('');
  const [restrictionStatus, setRestrictionStatus] = useState<
    boolean | undefined
  >(undefined);
  const [dataSource, setDataSource] = useState<ShippingRestrictedArea[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchParams, setSearchParams] =
    useState<ShippingRestrictedAreaQueryParams>({});
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [importModalVisible, setImportModalVisible] = useState(false);

  // 更新状态回调
  const handleStatusChange = async (id: string, isActive: boolean) => {
    try {
      const response = await updateShippingRestrictedAreaStatus(id, isActive);
      if (response.code === 200) {
        message.success('状态更新成功');
        // 更新本地数据，避免重新加载整个列表
        setDataSource((prev) =>
          prev.map((item) => (item.id === id ? { ...item, isActive } : item)),
        );
      } else {
        message.error(response.msg || '状态更新失败');
      }
    } catch (error) {
      console.error('更新状态失败:', error);
      message.error('状态更新失败，请稍后重试');
    }
  };

  // 同步到知识库
  const handleSyncToRagflow = async () => {
    const hide = message.loading('正在同步发货受限地址数据到知识库...', 0);
    try {
      const res = await syncShippingRestrictedAreaToRagflow();
      hide();
      if (res.code === 0) {
        message.success('发货受限地址数据同步成功');
      } else {
        message.error(res.msg || '发货受限地址数据同步失败');
      }
    } catch (error) {
      message.error('发货受限地址数据同步失败，请稍后重试');
      hide();
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '省市/直辖市',
      dataIndex: 'province',
      key: 'province',
      width: 150,
      render: (text: string) => (
        <span className="font-medium" data-oid="-o02ypy">
          {text}
        </span>
      ),
    },
    {
      title: '城市',
      dataIndex: 'city',
      key: 'city',
      width: 200,
    },
    {
      title: '县/区',
      dataIndex: 'district',
      key: 'district',
      width: 180,
    },
    {
      title: '发货受限程度',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 150,
      render: (isActive: boolean, record: ShippingRestrictedArea) => (
        <Select
          value={isActive}
          onChange={(value) => handleStatusChange(record.id, value)}
          size="small"
          bordered={false}
          style={{ width: 'auto', minWidth: '70px' }}
          options={[
            {
              label: <span className="text-green-500">不受限</span>,
              value: false,
            },
            {
              label: <span className="text-red-500">受限</span>,
              value: true,
            },
          ]}
        />
      ),
    },
  ];

  // 加载数据
  const loadData = async (params?: ShippingRestrictedAreaQueryParams) => {
    setLoading(true);
    try {
      const queryParams = {
        ...searchParams,
        ...params,
        current: params?.current || pagination.current,
        pageSize: params?.pageSize || pagination.pageSize,
      };

      const response = await getShippingRestrictedAreaList(queryParams);

      if (response.code === 200) {
        setDataSource(response.data.items);
        setPagination((prev) => ({
          ...prev,
          current: queryParams.current,
          pageSize: queryParams.pageSize,
          total: response.data.total,
        }));
      } else {
        message.error(response.msg || '获取数据失败');
      }
    } catch (error) {
      console.error('获取发货受限地址列表失败:', error);
      message.error('获取数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载数据
  useEffect(() => {
    loadData();
  }, []);

  // 搜索功能
  const handleSearch = () => {
    const newParams: ShippingRestrictedAreaQueryParams = {
      ...searchParams,
      searchText: searchText.trim() || undefined, // 使用searchText字段进行全文搜索
      isActive: restrictionStatus,
      current: 1,
    };
    setSearchParams(newParams);
    loadData(newParams);
  };

  // 重置搜索
  const handleReset = () => {
    setSearchText('');
    setRestrictionStatus(undefined);
    setSearchParams({});
    loadData({ current: 1, pageSize: pagination.pageSize });
  };

  // 打开导入模态框
  const handleImport = () => {
    setImportModalVisible(true);
  };

  // 导入成功回调
  const handleImportSuccess = () => {
    setImportModalVisible(false);
    loadData(); // 刷新数据
  };

  // 表格分页变化处理
  const handleTableChange = (page: number, pageSize: number) => {
    const newParams = { ...searchParams, current: page, pageSize };
    loadData(newParams);
  };

  return (
    <div className="p-6 bg-gray-100 min-h-screen" data-oid="hwy2f.6">
      {/* 主要内容区域 */}
      <div className="bg-white rounded-lg p-6 shadow-lg" data-oid="h0-atrd">
        {/* 标题区域 */}
        <div className="mb-6" data-oid="irtr-a0">
          <Title
            level={4}
            className="mb-2 text-gray-800 text-lg font-semibold"
            data-oid="2q8erx5"
          >
            交通管制、自然灾害等原因，部分地区网点收派件受限？
          </Title>

          <div className="text-gray-600 text-sm mb-5" data-oid="v_x0c5_">
            <Text type="secondary" data-oid="nh87m-7">
              &quot;地址管理&quot; 帮您搞定
            </Text>
            <Text className="text-blue-600" data-oid="k6mzljh">
              跟单场景
            </Text>
          </div>
        </div>

        {/* 搜索和操作区域 */}
        <div
          className="mb-5 flex justify-between items-center p-4 bg-gray-50 rounded-md border border-gray-200"
          data-oid=":itjc:e"
        >
          <div className="flex items-center gap-3" data-oid="p0g0of.">
            <Input
              placeholder="全国地址查询（省份/城市/县区）"
              prefix={<SearchOutlined data-oid="0w59_yl" />}
              className="w-70"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              onPressEnter={handleSearch}
              data-oid="j5cd9vg"
            />
            <Select
              placeholder="选择受限状态"
              className="w-40"
              value={restrictionStatus}
              onChange={setRestrictionStatus}
              allowClear
              options={[
                { label: '受限', value: true },
                { label: '不受限', value: false },
              ]}
            />
          </div>

          <div className="flex gap-3" data-oid="hn:o:ho">
            <Button
              onClick={handleReset}
              className="border-gray-300 text-gray-700"
            >
              重置
            </Button>
            <Button
              icon={<InboxOutlined />}
              onClick={handleImport}
              className="border-gray-300 text-gray-700"
              data-oid="8157n-3"
            >
              导入
            </Button>
            <Popconfirm
              title="确定要同步发货受限地址数据到知识库吗？"
              description="此操作将把当前发货受限地址数据同步到知识库系统中，请确认是否继续。"
              onConfirm={handleSyncToRagflow}
              okText="确定"
              cancelText="取消"
            >
              <Button className="border-gray-300 text-gray-700">
                同步到知识库
              </Button>
            </Popconfirm>
            <Button type="primary" onClick={handleSearch} data-oid="26iruln">
              查询
            </Button>
          </div>
        </div>

        {/* 表格区域 */}
        <div data-oid="46.awvk">
          <Table
            columns={columns}
            dataSource={dataSource}
            loading={loading}
            rowKey="id"
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              showTotal: (total) => `共 ${total} 条记录`,
              showSizeChanger: true,
              showQuickJumper: true,
              pageSizeOptions: ['10', '20', '50', '100'],
              onChange: handleTableChange,
              onShowSizeChange: handleTableChange,
            }}
            scroll={{ y: 'calc(100vh - 380px)' }}
            size="middle"
            bordered={false}
            className="bg-white"
            data-oid="e:cet52"
          />
        </div>
      </div>

      {/* 导入模态框 */}
      <ImportShippingRestrictedAreaModal
        visible={importModalVisible}
        onCancel={() => setImportModalVisible(false)}
        onSuccess={handleImportSuccess}
      />
    </div>
  );
};

export default ShippingRestrictedAddresses;
