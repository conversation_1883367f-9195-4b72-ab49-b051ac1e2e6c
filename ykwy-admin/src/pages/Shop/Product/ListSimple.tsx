import ExportProductModal from '@/components/Product/ExportProductModal';
import TaskCenter from '@/components/TaskCenter';
import type {
  TempProduct,
  TempProductInput,
  TempProductQueryParams,
} from '@/models/tempProduct';
import { getProductStatusLabel } from '@/models/tempProduct';
import {
  batchImportTempProducts,
  deleteTempProduct,
  downloadImportTemplate,
  getTempProductByProductId,
  getTempProductList,
  syncTempProductsToRagflow,
  upsertTempProduct,
} from '@/services/tempProduct';
import {
  ControlOutlined,
  DeleteOutlined,
  DownloadOutlined,
  EditOutlined,
  EyeOutlined,
  InboxOutlined,
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import {
  Alert,
  Button,
  Card,
  Col,
  Drawer,
  Form,
  Image,
  Input,
  Modal,
  Popconfirm,
  Row,
  Select,
  Space,
  Tag,
  Tooltip,
  Typography,
  Upload,
  message,
} from 'antd';
import type { TablePaginationConfig } from 'antd/es/table';
import dayjs from 'dayjs';
import React, { useCallback, useEffect, useState } from 'react';

const { Search } = Input;
const { Option } = Select;
const { Dragger } = Upload;

const FALLBACK_IMAGE_SRC =
  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==';

const ListSimple: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<TempProduct[]>([]);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
  });
  const [searchParams, setSearchParams] = useState<TempProductQueryParams>({});

  // 批量导入相关状态
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // 任务中心相关状态
  const [taskCenterVisible, setTaskCenterVisible] = useState(false);
  const [taskCenterRefreshTrigger, setTaskCenterRefreshTrigger] = useState(0);
  //同步商品知识库
  const handleSyncToKnowledgeBase = async () => {
    const hide = message.loading('正在同步商品数据到知识库...', 0);
    try {
      const res = await syncTempProductsToRagflow();
      hide();
      if (res.code === 0) {
        message.success('商品数据同步成功');
      } else {
        message.error(res.msg || '商品数据同步失败');
      }
    } catch (error) {
      message.error('商品数据同步失败，请稍后重试');
      hide();
    }
  };
  // 导出弹窗状态
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [currentProduct, setCurrentProduct] = useState<TempProduct | null>(
    null,
  );
  const [detailDrawerVisible, setDetailDrawerVisible] = useState(false);
  const [currentDetailProduct, setCurrentDetailProduct] =
    useState<TempProduct | null>(null);

  // 加载数据
  const loadData = async (params?: TempProductQueryParams) => {
    setLoading(true);
    try {
      const queryParams = {
        ...searchParams,
        ...params,
        current: params?.current || pagination.current,
        pageSize: params?.pageSize || pagination.pageSize,
      };

      const response = await getTempProductList(queryParams);

      if (response.code === 200) {
        setDataSource(response.data.items);
        setPagination((prev) => ({
          ...prev,
          current: queryParams.current,
          pageSize: queryParams.pageSize,
          total: response.data.total,
        }));
      } else {
        message.error(response.msg || '获取数据失败');
      }
    } catch (error) {
      console.error('获取商品列表失败:', error);
      message.error('获取数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载数据
  useEffect(() => {
    loadData();
  }, []);

  // 搜索处理
  const handleSearch = (value: string) => {
    const newParams = { ...searchParams, name: value, current: 1 };
    setSearchParams(newParams);
    loadData(newParams);
  };

  // 状态筛选处理
  const handleStatusFilter = (value: string) => {
    const newParams = {
      ...searchParams,
      status: value || undefined,
      current: 1,
    };
    setSearchParams(newParams);
    loadData(newParams);
  };

  // 重置搜索
  const handleReset = () => {
    setSearchParams({});
    loadData({ current: 1, pageSize: pagination.pageSize });
  };

  // 刷新数据
  const handleRefresh = () => {
    loadData({ ...searchParams });
  };

  // 下载模版文件
  const handleDownloadTemplate = async (format: 'excel' | 'csv' = 'excel') => {
    try {
      const { blob, filename } = await downloadImportTemplate(format);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success(`${format.toUpperCase()}模版文件下载成功`);
    } catch (error) {
      console.error('下载模版失败:', error);
      message.error('下载模版失败，请稍后重试');
    }
  };

  // 打开批量导入弹窗
  const handleOpenImportModal = () => {
    setImportModalVisible(true);
    setSelectedFile(null);
  };

  // 关闭批量导入弹窗
  const handleCloseImportModal = () => {
    setImportModalVisible(false);
    setSelectedFile(null);
  };

  // 处理文件选择（不立即上传）
  const handleFileSelect = (file: File) => {
    // 验证文件类型
    const isValidFile =
      file.type ===
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.type === 'application/vnd.ms-excel' ||
      file.type === 'text/csv' ||
      file.type === 'application/csv' ||
      file.name.toLowerCase().endsWith('.xlsx') ||
      file.name.toLowerCase().endsWith('.xls') ||
      file.name.toLowerCase().endsWith('.csv');

    if (!isValidFile) {
      message.error('只能上传Excel或CSV文件！');
      return false;
    }

    // 验证文件大小
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('文件大小不能超过10MB！');
      return false;
    }

    setSelectedFile(file);
    return false; // 阻止默认上传行为
  };

  // 处理文件上传
  const handleFileUpload = async (file: File) => {
    try {
      // 显示开始导入的消息
      message.loading('正在导入数据，请在任务中心查看进度...', 2);

      const response = await batchImportTempProducts(file);

      if (response.code === 200) {
        // 任务已创建成功，结果将在任务中心显示
        message.success('导入任务已结束，请在任务中心查看详细结果');

        // 触发任务中心刷新
        setTaskCenterRefreshTrigger((prev) => prev + 1);

        // 如果有成功导入的数据，延迟刷新列表
        if (response.data.successCount > 0) {
          setTimeout(() => {
            loadData({ ...searchParams });
          }, 3000); // 延长到3秒，给任务处理更多时间
        }
      } else {
        throw new Error(response.msg || '导入失败');
      }
    } catch (error) {
      console.error('批量导入失败:', error);
      message.error('批量导入失败，请检查文件格式并重试');
    }
  };

  // 确认上传文件
  const handleConfirmUpload = () => {
    if (selectedFile) {
      // 关闭导入弹窗
      handleCloseImportModal();
      // 开始上传
      handleFileUpload(selectedFile);
      // 稍微延迟打开任务中心，确保任务已创建
      setTimeout(() => {
        setTaskCenterVisible(true);
      }, 500);
    }
  };

  // 处理导出
  // const handleOpenExport = () => {
  //   setExportModalVisible(true);
  // };

  // 打开新建/编辑模态框
  const handleOpenModal = (record?: TempProduct) => {
    setCurrentProduct(record || null);
    setModalVisible(true);
  };

  // 关闭模态框
  const handleCloseModal = () => {
    setModalVisible(false);
    setCurrentProduct(null);
  };

  // 保存商品信息
  const handleSaveProduct = async (values: TempProductInput) => {
    try {
      if (currentProduct) {
        // 更新商品
        await upsertTempProduct({
          id: currentProduct.id,
          ...values,
        });

        message.success('商品更新成功');
      } else {
        // 创建新商品
        await upsertTempProduct(values);

        message.success('商品创建成功');
      }
      handleCloseModal();
      loadData();
    } catch {}
  };

  // 查看商品详情
  const handleView = useCallback(async (record: TempProduct) => {
    try {
      const response = await getTempProductByProductId(record.productId);
      if (response.code === 200) {
        setCurrentDetailProduct(response.data);
        setDetailDrawerVisible(true);
      } else {
        message.error(response.msg || '获取商品详情失败');
      }
    } catch (error) {
      console.error('获取商品详情失败:', error);
      message.error('获取商品详情失败');
    }
  }, []);

  // 删除商品
  const handleDeleteProduct = async (id: string) => {
    try {
      await deleteTempProduct(id);
      message.success('商品删除成功');
      loadData();
    } catch (error) {
      message.error('删除失败，请稍后重试');
    }
  };

  // 导出成功回调
  const handleExportSuccess = () => {
    // 刷新任务中心（如果开着）
    setTaskCenterRefreshTrigger((prev) => prev + 1);
    // 可以选择自动打开任务中心查看进度
    message.info('可在任务中心查看导出进度和下载文件');
  };

  // 取消选择文件
  const handleCancelSelect = () => {
    setSelectedFile(null);
  };

  // 表格列定义
  const columns: ProColumns<TempProduct>[] = [
    {
      title: '操作',
      key: 'action',
      width: 80,
      fixed: 'right',
      render: (_, record: TempProduct) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => handleView(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleOpenModal(record)}
              style={{ color: '#1890ff' }}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除该商品吗？"
            onConfirm={() => handleDeleteProduct(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button type="text" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
    {
      title: '商品图片',
      dataIndex: 'imageUrl',
      key: 'imageUrl',
      width: 100,
      fixed: 'left',
      render: (_, record: TempProduct) => (
        <Image
          src={record.imageUrl || ''}
          alt={record.name}
          width={60}
          height={60}
          className="object-cover rounded"
          fallback={FALLBACK_IMAGE_SRC}
          preview={{
            mask: <EyeOutlined />,
          }}
        />
      ),
    },
    {
      title: '商品名称',
      dataIndex: 'name',
      key: 'name',
      width: 300,
      ellipsis: {
        showTitle: false,
      },
      render: (_, record: TempProduct) => (
        <div style={{ display: 'flex', alignItems: 'center', minWidth: 0 }}>
          <Tooltip title={record.name} placement="topLeft">
            <Typography.Text className="flex-1 min-w-0 mr-2" ellipsis>
              <a
                href={record.linkOrId}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-500 no-underline"
              >
                {record.name}
              </a>
            </Typography.Text>
          </Tooltip>
          <Typography.Text
            copyable={{
              text: record.name,
              tooltips: ['复制商品名称', '已复制'],
            }}
            style={{ flexShrink: 0 }}
          />
        </div>
      ),
    },
    {
      title: '商品ID',
      dataIndex: 'productId',
      key: 'productId',
      width: 120,
      ellipsis: true,
      copyable: true,
    },
    {
      title: '货号/款号',
      dataIndex: 'styleNumber',
      key: 'styleNumber',
      width: 120,
      ellipsis: true,
      render: (_, record: TempProduct) => (
        <Typography.Text
          copyable={record.styleNumber ? { text: record.styleNumber } : false}
        >
          {record.styleNumber || '-'}
        </Typography.Text>
      ),
    },
    {
      title: '商品状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      align: 'center',
      render: (_, record: TempProduct) => {
        const statusConfig = getProductStatusLabel(record.status);
        return (
          <Tag color={statusConfig.color} style={{ margin: 0 }}>
            {statusConfig.text}
          </Tag>
        );
      },
      filters: [
        { text: '已上架', value: '已上架' },
        { text: '已下架', value: '已下架' },
      ],
      onFilter: (value: any, record: TempProduct) => record.status === value,
    },

    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      sorter: true,
      render: (_, record: TempProduct) => (
        <span style={{ fontSize: '12px', color: '#666' }}>
          {dayjs(record.createdAt).format('YYYY-MM-DD HH:mm')}
        </span>
      ),
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 150,
      sorter: true,
      render: (_, record: TempProduct) => (
        <span style={{ fontSize: '12px', color: '#666' }}>
          {dayjs(record.updatedAt).format('YYYY-MM-DD HH:mm')}
        </span>
      ),
    },
  ];

  // 表格变化处理
  const handleTableChange = (
    newPagination: TablePaginationConfig,
    filters: any,
    sorter: any,
  ) => {
    const params: TempProductQueryParams = {
      ...searchParams,
      current: newPagination.current,
      pageSize: newPagination.pageSize,
    };

    // 处理排序
    if (sorter && sorter.field && sorter.order) {
      params.sortBy = sorter.field;
      params.sortOrder = sorter.order === 'ascend' ? 'asc' : 'desc';
    }

    loadData(params);
  };

  return (
    <div style={{ padding: '24px', background: '#f0f2f5', minHeight: '100vh' }}>
      <Card
        title="商品列表"
        extra={
          <Button
            type="text"
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={loading}
          >
            刷新
          </Button>
        }
      >
        {/* 操作区域 */}
        <div style={{ marginBottom: 16 }}>
          <div className="flex justify-between items-center">
            {/* 搜索区域 */}
            <Space size="middle">
              <Search
                placeholder="搜索商品名称"
                allowClear
                style={{ width: 300 }}
                onSearch={handleSearch}
                enterButton={<SearchOutlined />}
              />
              <Select
                placeholder="选择状态"
                allowClear
                style={{ width: 120 }}
                onChange={handleStatusFilter}
                value={searchParams.status}
              >
                <Option value="已上架">已上架</Option>
                <Option value="已下架">已下架</Option>
                <Option value="待上架">待上架</Option>
                <Option value="草稿">草稿</Option>
              </Select>
              <Button onClick={handleReset}>重置</Button>
            </Space>

            {/* 批量操作区域 */}
            <Space size="middle">
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setModalVisible(true)}
              >
                新建商品
              </Button>
              <Popconfirm
                key="sync"
                title="确定要同步商品数据到知识库吗？"
                description="此操作将把当前商品数据同步到知识库系统中，请确认是否继续。"
                onConfirm={handleSyncToKnowledgeBase}
                okText="确定"
                cancelText="取消"
              >
                <Button>同步到知识库</Button>
              </Popconfirm>
              <Button
                icon={<ControlOutlined />}
                onClick={() => setTaskCenterVisible(true)}
              >
                任务中心
              </Button>

              <Button
                type="primary"
                icon={<UploadOutlined />}
                onClick={handleOpenImportModal}
              >
                批量导入
              </Button>
              {/*<Button icon={<ExportOutlined />} onClick={handleOpenExport}>*/}
              {/*  导出数据*/}
              {/*</Button>*/}
            </Space>
          </div>
        </div>

        {/* 数据表格 */}
        <ProTable
          columns={columns}
          dataSource={dataSource}
          rowKey="id"
          loading={loading}
          pagination={pagination}
          onChange={handleTableChange}
          scroll={{ x: 'max-content', y: 'calc(100vh - 300px)' }}
          size="middle"
          bordered
          search={false}
          toolBarRender={false}
          style={{
            background: '#fff',
            borderRadius: '6px',
          }}
        />
      </Card>

      {/* 批量导入弹窗 */}
      <Modal
        title="批量导入商品"
        open={importModalVisible}
        onCancel={handleCloseImportModal}
        footer={null}
        width={600}
        destroyOnHidden
      >
        <div style={{ padding: '20px 0' }}>
          <div style={{ marginBottom: 16, textAlign: 'right' }}>
            <Space size="middle">
              <Button
                icon={<DownloadOutlined />}
                onClick={() => handleDownloadTemplate('excel')}
              >
                下载Excel模版
              </Button>
              <Button
                icon={<DownloadOutlined />}
                onClick={() => handleDownloadTemplate('csv')}
              >
                下载CSV模版
              </Button>
            </Space>
          </div>
          {!selectedFile && (
            <>
              <Alert
                message="导入说明"
                description={
                  <div>
                    <p>1. 请先下载模版文件，按照模版格式填写数据</p>
                    <p>2. 支持上传 .xlsx、.xls 或 .csv 格式的文件</p>
                    <p>
                      3.
                      商品名称、商品链接或ID、商品ID、商品状态为必填字段，货号/款号为可选字段
                    </p>
                    <p>4. 缺少必填字段的数据将被跳过，不影响其他数据导入</p>
                  </div>
                }
                type="info"
                showIcon
                style={{ marginBottom: 20 }}
              />

              <Dragger
                name="file"
                multiple={false}
                accept=".xlsx,.xls,.csv"
                beforeUpload={handleFileSelect}
                showUploadList={false}
              >
                <p className="ant-upload-drag-icon">
                  <InboxOutlined />
                </p>
                <p className="ant-upload-text">点击或拖拽文件到此区域选择</p>
                <p className="ant-upload-hint">
                  支持单个文件选择，文件格式：.xlsx、.xls、.csv，大小限制：10MB
                </p>
              </Dragger>
            </>
          )}

          {selectedFile && (
            <>
              <Alert
                message="文件选择确认"
                description={
                  <div>
                    <p>您已选择文件，请确认是否开始导入：</p>
                  </div>
                }
                type="warning"
                showIcon
                style={{ marginBottom: 20 }}
              />

              <div
                style={{
                  border: '1px solid #d9d9d9',
                  borderRadius: '6px',
                  padding: '16px',
                  marginBottom: '20px',
                  backgroundColor: '#fafafa',
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    marginBottom: '8px',
                  }}
                >
                  <InboxOutlined
                    style={{
                      fontSize: '16px',
                      color: '#1890ff',
                      marginRight: '8px',
                    }}
                  />
                  <span style={{ fontWeight: 'bold' }}>选中的文件：</span>
                </div>
                <div style={{ marginLeft: '24px' }}>
                  <p style={{ margin: 0, color: '#1890ff' }}>
                    📄 {selectedFile.name}
                  </p>
                  <p
                    style={{
                      margin: '4px 0 0 0',
                      color: '#999',
                      fontSize: '12px',
                    }}
                  >
                    文件大小：{(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
              </div>

              <div style={{ textAlign: 'center' }}>
                <Space size="middle">
                  <Button onClick={handleCancelSelect}>重新选择</Button>
                  <Button type="primary" onClick={handleConfirmUpload}>
                    确认导入
                  </Button>
                </Space>
              </div>
            </>
          )}
        </div>
      </Modal>

      {/* 导出弹窗 */}
      <ExportProductModal
        visible={exportModalVisible}
        onClose={() => setExportModalVisible(false)}
        onExportSuccess={handleExportSuccess}
      />

      {/* 新建/编辑商品弹窗 */}
      <Modal
        title={currentProduct ? '编辑商品' : '新建商品'}
        open={modalVisible}
        onCancel={handleCloseModal}
        footer={null}
        width={800}
        destroyOnHidden
      >
        <Form
          layout="vertical"
          initialValues={
            currentProduct || {
              name: '',
              linkOrId: '',
              productId: '',
              status: '已上架',
              styleNumber: '',
              imageUrl: '',
            }
          }
          onFinish={handleSaveProduct}
          onFinishFailed={() => message.error('请检查表单填写是否正确')}
        >
          <Row gutter={24}>
            <Col span={24}>
              <Form.Item
                name="name"
                label="商品名称"
                rules={[{ required: true, message: '请输入商品名称' }]}
              >
                <Input placeholder="请输入商品名称" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                name="productId"
                label="商品ID"
                rules={[
                  { required: true, message: '请输入商品ID' },
                  { pattern: /^\d{12}$/, message: '商品ID必须为12位数字' },
                ]}
              >
                <Input placeholder="请输入12位数字商品ID" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                name="status"
                label="商品状态"
                rules={[{ required: true, message: '请选择商品状态' }]}
              >
                <Select placeholder="请选择商品状态">
                  <Option value="已上架">已上架</Option>
                  <Option value="已下架">已下架</Option>
                  <Option value="待上架">待上架</Option>
                  <Option value="草稿">草稿</Option>
                </Select>
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item
                name="linkOrId"
                label="商品链接或ID"
                rules={[{ required: true, message: '请输入商品链接或ID' }]}
              >
                <Input placeholder="请输入商品链接或ID" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item name="styleNumber" label="货号/款号">
                <Input placeholder="请输入货号/款号" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                name="imageUrl"
                label="商品图片链接"
                rules={[
                  {
                    type: 'url',
                    message: '请输入有效的图片URL地址',
                  },
                ]}
              >
                <Input placeholder="请输入商品图片URL" />
              </Form.Item>
            </Col>

            {currentProduct?.imageUrl && (
              <Col span={24}>
                <Form.Item label="当前商品图片">
                  <Image
                    src={currentProduct.imageUrl}
                    width={120}
                    height={120}
                    style={{ objectFit: 'cover' }}
                    fallback={FALLBACK_IMAGE_SRC}
                  />
                </Form.Item>
              </Col>
            )}
          </Row>

          <Form.Item style={{ marginTop: 24, textAlign: 'right' }}>
            <Space>
              <Button onClick={handleCloseModal}>取消</Button>
              <Button type="primary" htmlType="submit">
                {currentProduct ? '更新商品' : '创建商品'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 商品详情抽屉 */}
      <Drawer
        title="商品详情"
        placement="right"
        open={detailDrawerVisible}
        onClose={() => setDetailDrawerVisible(false)}
        width={600}
      >
        {currentDetailProduct && (
          <div className="p-5">
            <div className="mb-4 text-center">
              <Image
                src={currentDetailProduct.imageUrl || ''}
                alt={currentDetailProduct.name}
                width={400}
                height={400}
                className="object-cover rounded"
                fallback={FALLBACK_IMAGE_SRC}
              />
            </div>
            <div className="mb-3">
              <h3 className="mb-2 text-base font-medium">商品信息</h3>
              <p>
                <strong>商品名称：</strong>
                {currentDetailProduct.name}
              </p>
              <p>
                <strong>商品ID：</strong>
                {currentDetailProduct.productId}
              </p>
              <p>
                <strong>货号/款号：</strong>
                {currentDetailProduct.styleNumber || '-'}
              </p>
              <p>
                <strong>商品状态：</strong>
                <Tag
                  color={
                    getProductStatusLabel(currentDetailProduct.status).color
                  }
                >
                  {getProductStatusLabel(currentDetailProduct.status).text}
                </Tag>
              </p>
              <p>
                <strong>商品链接：</strong>
                <a
                  href={currentDetailProduct.linkOrId}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {currentDetailProduct.linkOrId}
                </a>
              </p>
            </div>
            <div>
              <h3 className="mb-2 text-base font-medium">时间信息</h3>
              <p>
                <strong>创建时间：</strong>
                {dayjs(currentDetailProduct.createdAt).format(
                  'YYYY-MM-DD HH:mm:ss',
                )}
              </p>
              <p>
                <strong>更新时间：</strong>
                {dayjs(currentDetailProduct.updatedAt).format(
                  'YYYY-MM-DD HH:mm:ss',
                )}
              </p>
            </div>
          </div>
        )}
      </Drawer>

      {/* 任务中心 */}
      <TaskCenter
        visible={taskCenterVisible}
        onClose={() => setTaskCenterVisible(false)}
        defaultActiveTab="import"
        refreshTrigger={taskCenterRefreshTrigger}
      />
    </div>
  );
};

export default ListSimple;
