import { ArrowLeftOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import React from 'react';

const RuleSetting: React.FC<{ onBack: () => void }> = ({ onBack }) => (
  <div className="bg-[#f7f8fa] min-h-screen p-6" data-oid="_:y5.7w">
    <div className="bg-white rounded-lg p-6" data-oid="zllmiqd">
      <div className="flex items-center mb-4" data-oid="9_1oa4i">
        <ArrowLeftOutlined
          className="text-xl mr-2 cursor-pointer"
          onClick={onBack}
          data-oid="gjofhft"
        />

        <span className="text-lg font-semibold" data-oid="6ave6vq">
          设置商品尺码表关联规则
        </span>
      </div>
      <div
        className="flex items-start bg-[#fff7e6] rounded p-4 mb-4"
        data-oid="sa0-jyx"
      >
        <img
          src="http://cdn.pdd.myjjing.com/static/sizeRule/size_rule_tip.png"
          alt="icon"
          className="w-12 h-12 mr-4"
          data-oid="hupeq4e"
        />

        <div className="text-sm text-[#222] leading-6" data-oid="jgnj1be">
          <div data-oid="uvhvw7f">
            1. 快速绑定仅&quot;未绑定&quot;尺码表的商品生效
          </div>
          <div data-oid="u_7s47y">
            2.
            拖拽规则可调整优先级，当同一商品命中多条规则时，以优先级高的规则生效，最多支持100条规则
          </div>
          <div data-oid="wni-dua">
            3.
            每次批量上新或修改表规则后，需要手动点击&quot;立即应用&quot;开启生效顺序执行规则
          </div>
        </div>
      </div>
      <div
        className="bg-[#f7f8fa] rounded p-4 max-h-[calc(100vh-300px)] overflow-y-auto"
        data-oid="aoma0x:"
      >
        <div className="flex gap-2 mb-4" data-oid="rr6wow5">
          <Button type="primary" data-oid="_tr5y1.">
            新建规则
          </Button>
          <Button data-oid="16ldkc_">立即应用</Button>
        </div>
        <table className="w-full bg-white rounded" data-oid="v:uaf8n">
          <thead data-oid="qsxin8:">
            <tr className="text-left" data-oid="3_-zvmo">
              <th className="py-2 px-3" data-oid="attgjpb">
                序号
              </th>
              <th className="py-2 px-3" data-oid="v-i8nln">
                规则名称
              </th>
              <th className="py-2 px-3" data-oid="erw7ttm">
                满足条件
              </th>
              <th className="py-2 px-3" data-oid="8b46n0a">
                执行内容
              </th>
              <th className="py-2 px-3" data-oid="abgfjzz">
                启用状态
              </th>
              <th className="py-2 px-3" data-oid="jjdf:q.">
                运行状态
              </th>
              <th className="py-2 px-3" data-oid="26:djr7">
                操作
              </th>
            </tr>
          </thead>
          <tbody data-oid="fu8f8q5">
            <tr data-oid="nw-hhgu">
              <td colSpan={7} data-oid="0m_m92x">
                <div
                  className="flex flex-col items-center py-8"
                  data-oid=":0z34r8"
                >
                  <img
                    src="https://img.alicdn.com/imgextra/i2/O1CN01Qn1ytF1Ck8Q4lYt6w_!!6000000000422-2-tps-120-96.png"
                    alt="暂无数据"
                    className="w-20 mb-2"
                    data-oid="r0q-ghm"
                  />

                  <div className="text-[#888] text-sm" data-oid="wkzlflk">
                    暂无数据
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
);

export default RuleSetting;
