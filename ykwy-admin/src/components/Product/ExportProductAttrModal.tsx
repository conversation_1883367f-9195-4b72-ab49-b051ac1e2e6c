import { Button, Checkbox, Divider, Input, Modal } from 'antd';
import React, { useState } from 'react';

const productAttrs = [
  { label: '商品ID', value: 'id', disabled: true },
  { label: '商品名称', value: 'name', disabled: true },
  { label: '商品链接', value: 'link', disabled: true },
  { label: '商品标签', value: 'tag' },
  { label: '商品状态', value: 'status' },
  { label: '尺码表', value: 'size' },
  { label: '商品分类', value: 'category' },
  { label: '型号', value: 'model' },
];

const platformAttrs = [
  { label: '100146', value: '100146' },
  { label: '162887', value: '162887' },
  { label: '180243', value: '180243' },
  { label: '上市时间', value: '上市时间' },
  { label: '国家', value: '国家' },
  { label: '款式', value: '款式' },
  { label: '穿着方式', value: '穿着方式' },
  { label: '肩带', value: '肩带' },
  { label: '袖长', value: '袖长' },
  { label: '适用人群', value: '适用人群' },
  { label: '适用运动', value: '适用运动' },
  { label: '领型', value: '领型' },
  { label: '功能', value: '功能' },
  { label: '插件', value: '插件' },
  { label: '流行元素', value: '流行元素' },
  { label: '类别', value: '类别' },
  { label: '衣长', value: '衣长' },
  { label: '裤长', value: '裤长' },
  { label: '适用季节', value: '适用季节' },
  { label: '里料材质', value: '里料材质' },
  { label: '厚度', value: '厚度' },
  { label: '材质', value: '材质' },
  { label: '版型', value: '版型' },
  { label: '罩杯款式', value: '罩杯款式' },
  { label: '衣门襟', value: '衣门襟' },
  { label: '裤门襟', value: '裤门襟' },
  { label: '适用性别', value: '适用性别' },
  { label: '面料', value: '面料' },
];

interface ExportProductAttrModalProps {
  visible: boolean;
  onCancel: () => void;
  onExport: (productAttrs: string[], platformAttrs: string[]) => void;
}

const ExportProductAttrModal: React.FC<ExportProductAttrModalProps> = ({
  visible,
  onCancel,
  onExport,
}) => {
  const [checkedProductAttrs, setCheckedProductAttrs] = useState<string[]>([
    'id',
    'name',
    'link',
  ]);
  const [checkedPlatformAttrs, setCheckedPlatformAttrs] = useState<string[]>(
    [],
  );
  const [search, setSearch] = useState('');

  const filter = (list: any[]) =>
    list.filter((item) => item.label.includes(search));

  const onProductAllChange = (e: any) => {
    const newChecked = e.target.checked
      ? productAttrs.map((i) => i.value)
      : productAttrs.filter((i) => i.disabled).map((i) => i.value);
    setCheckedProductAttrs(newChecked);
  };

  const onProductCheckboxChange = (value: string, checked: boolean) => {
    if (checked) {
      setCheckedProductAttrs([...checkedProductAttrs, value]);
    } else {
      setCheckedProductAttrs(
        checkedProductAttrs.filter((item) => item !== value),
      );
    }
  };

  const onPlatformAllChange = (e: any) => {
    setCheckedPlatformAttrs(
      e.target.checked ? platformAttrs.map((i) => i.value) : [],
    );
  };

  const onPlatformCheckboxChange = (list: any[]) => {
    setCheckedPlatformAttrs(list as string[]);
  };

  return (
    <Modal
      title="批量导出商品属性"
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={700}
      maskClosable={false}
      destroyOnHidden
      data-oid="qe9qjp4"
    >
      <Input
        placeholder="请输入关键字"
        value={search}
        onChange={(e) => setSearch(e.target.value)}
        style={{ width: 300, marginBottom: 16 }}
        data-oid="mclo8s1"
      />

      <div style={{ display: 'flex', gap: 24 }} data-oid="udzfva6">
        <div style={{ flex: 1 }} data-oid="bjbr1go">
          <Checkbox
            indeterminate={
              checkedProductAttrs.length > 0 &&
              checkedProductAttrs.length < productAttrs.length
            }
            checked={checkedProductAttrs.length === productAttrs.length}
            onChange={onProductAllChange}
            style={{ marginBottom: 8, fontWeight: 600 }}
            data-oid="fezsvja"
          >
            商品属性
          </Checkbox>
          <div
            style={{ display: 'flex', flexDirection: 'column', gap: 4 }}
            data-oid="hu9bey_"
          >
            {filter(productAttrs).map((attr) => (
              <Checkbox
                key={attr.value}
                value={attr.value}
                disabled={attr.disabled}
                checked={checkedProductAttrs.includes(attr.value)}
                onChange={(e) =>
                  onProductCheckboxChange(attr.value, e.target.checked)
                }
                data-oid="91imudq"
              >
                {attr.label}
              </Checkbox>
            ))}
          </div>
        </div>

        <div style={{ flex: 2 }} data-oid="8h40_v.">
          <Checkbox
            indeterminate={
              checkedPlatformAttrs.length > 0 &&
              checkedPlatformAttrs.length < platformAttrs.length
            }
            checked={checkedPlatformAttrs.length === platformAttrs.length}
            onChange={onPlatformAllChange}
            style={{ marginBottom: 8, fontWeight: 600 }}
            data-oid="ykfhfew"
          >
            平台属性
          </Checkbox>
          <Checkbox.Group
            value={checkedPlatformAttrs}
            onChange={onPlatformCheckboxChange}
            style={{ display: 'flex', flexWrap: 'wrap', gap: 16 }}
            data-oid="g3qy_4q"
          >
            {filter(platformAttrs).map((attr) => (
              <Checkbox key={attr.value} value={attr.value} data-oid="jexhzgp">
                {attr.label}
              </Checkbox>
            ))}
          </Checkbox.Group>
        </div>
      </div>
      <Divider style={{ margin: '24px 0' }} data-oid="wv.w2-f" />
      <div style={{ textAlign: 'right' }} data-oid="ysi77ua">
        <Button
          onClick={onCancel}
          style={{ marginRight: 8 }}
          data-oid="53nzylb"
        >
          取消
        </Button>
        <Button
          type="primary"
          onClick={() => onExport(checkedProductAttrs, checkedPlatformAttrs)}
          data-oid="1.e9-81"
        >
          导出
        </Button>
      </div>
    </Modal>
  );
};

export default ExportProductAttrModal;
