import { Button, Card, Form, Input, Modal, Select } from 'antd';
import React from 'react';

interface ProductKnowledgeModalProps {
  open: boolean;
  onCancel: () => void;
  onOk: (values: any) => void;
  product: any; // 当前商品对象
}

const ProductKnowledgeModal: React.FC<ProductKnowledgeModalProps> = ({
  open,
  onCancel,
  onOk,
  product,
}) => {
  const [form] = Form.useForm();

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      if (!values.detail || values.detail === '<p><br></p>') {
        form.setFields([{ name: 'detail', errors: ['知识详情不能为空'] }]);
        return;
      }

      onOk(values);
    } catch (err) {}
  };

  return (
    <Modal
      title="商品知识"
      open={open}
      onCancel={onCancel}
      onOk={handleOk}
      width={700}
      footer={[
        <Button key="cancel" onClick={onCancel} data-oid="d_x9i0p">
          取消
        </Button>,
        <Button key="ok" type="primary" onClick={handleOk} data-oid="ytaym9o">
          确定
        </Button>,
      ]}
      data-oid="mbp-sjv"
    >
      <div className="mb-6" data-oid="0cq3mfy">
        <Card variant="outlined" className="bg-[#fafbfc]" data-oid="w8w9zlb">
          <div className="flex items-center p-4" data-oid="vyxld3t">
            <img
              src={product?.imgUrl}
              alt={product?.name}
              className="w-[60px] h-[60px] mr-4 object-cover rounded border border-gray-200"
              data-oid="_1ecpkv"
            />

            <div data-oid="5k8gfxs">
              <div
                className="font-semibold text-base text-[#1677ff] mb-0.5"
                data-oid="i5h4pp5"
              >
                {product?.name}
              </div>
              <div className="text-gray-500 text-sm" data-oid="_wv:cpd">
                {product?.desc}
              </div>
            </div>
          </div>
        </Card>
      </div>
      <Form form={form} layout="vertical" data-oid="c:ivak_">
        <Form.Item
          label="知识标题"
          name="title"
          rules={[{ required: true, message: '知识标题不能为空' }]}
          data-oid="zt4th1q"
        >
          <Input placeholder="请输入" data-oid="ox7epz7" />
        </Form.Item>
        <Form.Item
          label="知识分类"
          name="category"
          initialValue="默认分类"
          data-oid="lp4r54b"
        >
          <Select data-oid="4xdyez5">
            <Select.Option value="默认分类" data-oid="i84mav1">
              默认分类
            </Select.Option>
          </Select>
        </Form.Item>
        <Form.Item
          label="知识详情"
          name="detail"
          required
          rules={[{ required: true, message: '知识详情不能为空' }]}
          data-oid="m:nz8dr"
        >
          <Input.TextArea
            placeholder="请输入"
            autoSize={{ minRows: 5, maxRows: 8 }}
            showCount
            maxLength={500}
            data-oid="92-s.zs"
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ProductKnowledgeModal;
