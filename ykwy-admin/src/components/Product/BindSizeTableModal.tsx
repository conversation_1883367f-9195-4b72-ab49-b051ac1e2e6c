import { AppstoreOutlined } from '@ant-design/icons';
import { Button, Input, Modal, Pagination, Tabs } from 'antd';
import React, { useState } from 'react';

interface SizeTableType {
  id: string;
  name: string;
  bindCount: number;
  type?: 'normal' | 'complex';
}

interface BindSizeTableModalProps {
  open: boolean;
  onOk: (selected: SizeTableType | null) => void;
  onCancel: () => void;
  sizeTables: SizeTableType[];
  selectedId?: string;
}

const BindSizeTableModal: React.FC<BindSizeTableModalProps> = ({
  open,
  onOk,
  onCancel,
  sizeTables,
  selectedId,
}) => {
  const [search, setSearch] = useState('');
  const [selected, setSelected] = useState<string | undefined>(selectedId);
  const [page, setPage] = useState(1);
  const pageSize = 6;

  // 过滤和分页
  const filtered = sizeTables.filter((t) => t.name.includes(search));
  const paged = filtered.slice((page - 1) * pageSize, page * pageSize);

  // 三列布局
  const getRows = (arr: SizeTableType[]) => {
    const rows = [];
    for (let i = 0; i < arr.length; i += 3) {
      rows.push(arr.slice(i, i + 3));
    }
    return rows;
  };

  // 图标渲染
  const renderIcon = (type?: string) => {
    if (type === 'complex') {
      return (
        <span className="flex items-center gap-1" data-oid="favkpse">
          <AppstoreOutlined
            style={{ color: '#4e83fd', fontSize: 22 }}
            data-oid="0puh51q"
          />

          <span
            className="text-[#4e83fd] text-[13px] font-medium ml-1"
            data-oid="1w8zi4s"
          >
            复合
          </span>
        </span>
      );
    }
    return (
      <svg
        width="22"
        height="22"
        viewBox="0 0 24 24"
        fill="none"
        data-oid="7jgy5:n"
      >
        <rect
          x="4"
          y="6"
          width="16"
          height="12"
          rx="2"
          fill="#b3b3b3"
          data-oid="ki1aoyi"
        />
      </svg>
    );
  };

  return (
    <Modal
      open={open}
      title={
        <span className="font-medium" data-oid="7-ptaeu">
          绑定尺码表
        </span>
      }
      onCancel={onCancel}
      width={720}
      footer={[
        <Button
          key="cancel"
          onClick={onCancel}
          className="min-w-[72px]"
          data-oid="9_p7clh"
        >
          取消
        </Button>,
        <Button
          key="ok"
          type="primary"
          disabled={!selected}
          className="min-w-[72px]"
          onClick={() =>
            onOk(sizeTables.find((t) => t.id === selected) || null)
          }
          data-oid="f1.hz8-"
        >
          绑定
        </Button>,
      ]}
      style={{ top: 40 }}
      bodyStyle={{ padding: 0, minHeight: 380 }}
      data-oid="6tf8758"
    >
      <div className="px-6 pt-6 pb-0" data-oid="yo:2fut">
        <div className="flex items-center mb-4" data-oid="0da5oek">
          <Tabs
            defaultActiveKey="all"
            items={[{ key: 'all', label: '全部' }]}
            className="flex-1"
            data-oid="-viyh9-"
          />

          <Input.Search
            placeholder="请输入关键字"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="w-[220px] ml-4"
            allowClear
            data-oid="4yoj:.e"
          />
        </div>
        <div className="min-h-[200px]" data-oid="autc-dq">
          {getRows(paged).map((row, rowIdx) => (
            <div key={rowIdx} className="flex gap-4 mb-4" data-oid="__w0p8p">
              {row.map((table) => {
                const isSelected = selected === table.id;
                return (
                  <div
                    key={table.id}
                    onClick={() => setSelected(table.id)}
                    className={`flex flex-1 min-w-0 bg-white rounded-lg h-20 items-center box-border cursor-pointer px-[18px] relative transition-all border ${
                      isSelected
                        ? 'border-[#1677ff] border-2 bg-[#f0f7ff] shadow-[0_0_8px_0_#e6f0ff]'
                        : 'border-[#e5e6eb] border'
                    } hover:shadow-[0_2px_8px_0_#e5e6eb]`}
                    tabIndex={0}
                    data-oid="un2u4sm"
                  >
                    <div
                      className="min-w-[38px] mr-3 flex items-center justify-start"
                      data-oid="s812pbx"
                    >
                      {renderIcon(table.type)}
                    </div>
                    <div
                      className="flex flex-col flex-1 min-w-0 items-start justify-center"
                      data-oid="awm225b"
                    >
                      <div
                        className="font-medium text-[#222] text-[15px] mb-0.5 truncate w-full"
                        data-oid="-po:wxr"
                      >
                        {table.name}
                      </div>
                      <div
                        className="text-[#888] text-[13px] truncate w-full"
                        data-oid="lg4n_yg"
                      >
                        已绑定{table.bindCount}款商品
                      </div>
                    </div>
                  </div>
                );
              })}
              {/* 补齐空白卡片保证三列对齐 */}
              {row.length < 3 &&
                Array.from({ length: 3 - row.length }).map((_, i) => (
                  <div
                    key={i}
                    className="flex-1 min-w-0 bg-transparent border-none"
                    data-oid="c7iucp4"
                  />
                ))}
            </div>
          ))}
          {paged.length === 0 && (
            <div className="text-[#bbb] py-8 text-center" data-oid="t2mfvia">
              暂无数据
            </div>
          )}
        </div>
        <div
          className="flex justify-center items-center mt-6"
          data-oid="1kc9q24"
        >
          <Pagination
            current={page}
            pageSize={pageSize}
            total={filtered.length}
            onChange={setPage}
            showSizeChanger={false}
            style={{ userSelect: 'none' }}
            data-oid="cmdw1jx"
          />
        </div>
      </div>
    </Modal>
  );
};

export default BindSizeTableModal;
