import { exportTempProducts } from '@/services/tempProduct';
import {
  Button,
  Checkbox,
  Divider,
  Form,
  Input,
  message,
  Modal,
  Radio,
} from 'antd';
import React, { useState } from 'react';

interface ExportProductModalProps {
  visible: boolean;
  onClose: () => void;
  onExportSuccess?: () => void; // 导出成功后的回调
}

const ExportProductModal: React.FC<ExportProductModalProps> = ({
  visible,
  onClose,
  onExportSuccess,
}) => {
  const [exportForm] = Form.useForm();
  const [exportLoading, setExportLoading] = useState(false);

  // 可选的导出字段
  const fieldOptions = [
    { label: '商品名称', value: 'name' },
    { label: '商品链接或ID', value: 'linkOrId' },
    { label: '商品ID', value: 'productId' },
    { label: '商品状态', value: 'status' },
    { label: '货号/款号', value: 'styleNumber' },
    { label: '商品图片链接', value: 'imageUrl' },
    { label: '创建时间', value: 'createdAt' },
    { label: '更新时间', value: 'updatedAt' },
  ];

  // 关闭弹窗
  const handleClose = () => {
    exportForm.resetFields();
    onClose();
  };

  // 执行导出
  const handleExport = async () => {
    try {
      const values = await exportForm.validateFields();
      setExportLoading(true);

      // 构建过滤条件
      const filters: any = {};
      if (values.name) filters.name = values.name;
      if (values.status) filters.status = values.status;
      if (values.productId) filters.productId = values.productId;
      filters.includeDeleted = values.includeDeleted || false;

      const response = await exportTempProducts({
        format: values.format,
        fields: values.fields,
        filters: Object.keys(filters).length > 0 ? filters : undefined,
      });

      if (response.code === 200) {
        message.success('导出任务已结束，请在任务中心查看进度和下载文件');
        handleClose();
        // 调用成功回调
        if (onExportSuccess) {
          onExportSuccess();
        }
      } else {
        message.error(response.msg || '导出任务创建失败');
      }
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败，请稍后重试');
    } finally {
      setExportLoading(false);
    }
  };

  return (
    <Modal
      title="导出商品数据"
      open={visible}
      onCancel={handleClose}
      footer={[
        <Button key="cancel" onClick={handleClose}>
          取消
        </Button>,
        <Button
          key="export"
          type="primary"
          loading={exportLoading}
          onClick={handleExport}
        >
          开始导出
        </Button>,
      ]}
      width={600}
      destroyOnHidden
    >
      <Form
        form={exportForm}
        layout="vertical"
        initialValues={{
          format: 'excel',
          fields: ['name', 'linkOrId', 'productId', 'status'],
        }}
      >
        <Form.Item
          label="导出格式"
          name="format"
          rules={[{ required: true, message: '请选择导出格式' }]}
        >
          <Radio.Group>
            <Radio value="excel">Excel (.xlsx)</Radio>
            <Radio value="csv">CSV (.csv)</Radio>
          </Radio.Group>
        </Form.Item>

        <Form.Item
          label="导出字段"
          name="fields"
          rules={[{ required: true, message: '请至少选择一个字段' }]}
        >
          <Checkbox.Group
            options={fieldOptions}
            style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}
          />
        </Form.Item>

        <Divider orientation="left" plain>
          过滤条件（可选）
        </Divider>

        <Form.Item label="商品名称" name="name">
          <Input placeholder="按商品名称过滤" />
        </Form.Item>

        <Form.Item label="商品状态" name="status">
          <Input placeholder="按商品状态过滤" />
        </Form.Item>

        <Form.Item label="商品ID" name="productId">
          <Input placeholder="按商品ID过滤" />
        </Form.Item>

        <Form.Item name="includeDeleted" valuePropName="checked">
          <Checkbox>包含已删除的商品</Checkbox>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ExportProductModal;
