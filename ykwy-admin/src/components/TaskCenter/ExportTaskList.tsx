import {
  getImportExportTaskList,
  type ImportExportTask,
  type ImportExportTaskQueryParams,
} from '@/services/importExportTask';
import { downloadExportFile } from '@/services/tempProduct';
import { DownloadOutlined, ReloadOutlined } from '@ant-design/icons';
import { Button, Empty, message, Table, Tag, Tooltip } from 'antd';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';

interface ExportTaskListProps {
  refreshTrigger?: number;
  visible?: boolean;
}

const ExportTaskList: React.FC<ExportTaskListProps> = ({
  refreshTrigger,
  visible,
}) => {
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<ImportExportTask[]>([]);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
  });

  const refreshInterval = useRef<NodeJS.Timeout | null>(null);

  // 加载数据
  const loadData = async (params?: ImportExportTaskQueryParams) => {
    setLoading(true);
    try {
      // 计算30天前的时间
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const queryParams = {
        taskType: 'export' as const,
        current: params?.current || pagination.current,
        pageSize: params?.pageSize || pagination.pageSize,
        sortBy: 'createdAt',
        sortOrder: 'desc' as const,
        startTime: thirtyDaysAgo.toISOString(),
        ...params,
      };

      const response = await getImportExportTaskList(queryParams);

      if (response.code === 200) {
        // 确保items是数组，避免undefined
        setDataSource(response.data?.items || []);
        setPagination((prev) => ({
          ...prev,
          current: queryParams.current,
          pageSize: queryParams.pageSize,
          total: response.data?.total || 0,
        }));
      } else {
        message.error(response.msg || '获取任务列表失败');
        // 错误时也确保dataSource是数组
        setDataSource([]);
      }
    } catch (error) {
      console.error('获取任务列表失败:', error);
      message.error('获取任务列表失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 自动刷新
  const startAutoRefresh = () => {
    if (refreshInterval.current) {
      clearInterval(refreshInterval.current);
    }

    refreshInterval.current = setInterval(() => {
      const hasPendingTasks = dataSource.some(
        (task) => task.status === 'pending',
      );
      if (hasPendingTasks) {
        loadData();
      }
    }, 3000);
  };

  const stopAutoRefresh = () => {
    if (refreshInterval.current) {
      clearInterval(refreshInterval.current);
      refreshInterval.current = null;
    }
  };

  // 初始化加载数据
  useEffect(() => {
    loadData();
    return () => {
      stopAutoRefresh();
    };
  }, []);

  // 响应refreshTrigger变化
  useEffect(() => {
    if (refreshTrigger && visible) {
      loadData();
    }
  }, [refreshTrigger, visible]);

  // 监听数据变化，决定是否启动自动刷新
  useEffect(() => {
    const hasPendingTasks = dataSource.some(
      (task) => task.status === 'pending',
    );
    if (hasPendingTasks) {
      startAutoRefresh();
    } else {
      stopAutoRefresh();
    }
  }, [dataSource]);

  // 手动刷新
  const handleRefresh = () => {
    loadData();
  };

  // 表格变化处理
  const handleTableChange = (newPagination: TablePaginationConfig) => {
    loadData({
      current: newPagination.current,
      pageSize: newPagination.pageSize,
    });
  };

  // 渲染状态标签
  const renderStatus = (status: string) => {
    const statusConfig = {
      pending: { color: 'processing', text: '处理中' },
      success: { color: 'success', text: '成功' },
      failed: { color: 'error', text: '失败' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      color: 'default',
      text: status,
    };

    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 渲染结果列
  const renderResult = (record: ImportExportTask) => {
    if (record.status === 'pending') {
      return <span style={{ color: '#1890ff' }}>处理中...</span>;
    }

    if (record.status === 'success') {
      return (
        <div style={{ fontSize: '12px', color: '#52c41a' }}>
          {record.result || '导出成功'}
        </div>
      );
    }

    if (record.status === 'failed') {
      return (
        <div style={{ color: '#ff4d4f', fontSize: '12px' }}>
          <Tooltip title={record.result || '导出失败'}>导出失败</Tooltip>
        </div>
      );
    }

    return '-';
  };

  // 下载文件
  const handleDownload = async (record: ImportExportTask) => {
    if (!record.fileName) {
      message.error('文件名不存在');
      return;
    }

    try {
      message.loading('正在下载文件...', 0);

      // 使用统一的request方法下载文件
      const response = await downloadExportFile(record.fileName);

      // 确定文件类型
      const isExcel = record.fileName.toLowerCase().endsWith('.xlsx');
      const contentType = isExcel
        ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        : 'text/csv';

      // 创建Blob对象
      const blob = new Blob([response], { type: contentType });

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = record.fileName;
      link.style.display = 'none';

      // 触发下载
      document.body.appendChild(link);
      link.click();

      // 清理
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      message.destroy();
      message.success('文件下载成功');
    } catch (error) {
      message.destroy();
      console.error('下载失败:', error);
      message.error(
        `下载失败: ${error instanceof Error ? error.message : '未知错误'}`,
      );
    }
  };

  // 渲染操作列
  const renderActions = (record: ImportExportTask) => {
    if (record.status === 'success' && record.filePath) {
      return (
        <Button
          type="link"
          size="small"
          icon={<DownloadOutlined />}
          onClick={() => handleDownload(record)}
        >
          下载
        </Button>
      );
    }
    return null;
  };

  const columns: ColumnsType<ImportExportTask> = [
    {
      title: '任务名称',
      dataIndex: 'taskName',
      key: 'taskName',
      width: 180,
      ellipsis: true,
    },
    {
      title: '时间',
      dataIndex: 'taskTime',
      key: 'taskTime',
      width: 130,
      render: (text: string) => (
        <span style={{ fontSize: '12px' }}>
          {dayjs(text).format('MM-DD HH:mm')}
        </span>
      ),
    },
    {
      title: '文件名',
      dataIndex: 'fileName',
      key: 'fileName',
      width: 160,
      ellipsis: true,
      render: (text: string) => text || '-',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (_, record) => renderStatus(record.status),
    },
    {
      title: '结果',
      dataIndex: 'result',
      key: 'result',
      width: 120,
      render: (_, record) => renderResult(record),
    },
    {
      title: '操作',
      key: 'actions',
      width: 70,
      render: (_, record) => renderActions(record),
      fixed: 'right',
    },
  ];

  return (
    <div style={{ padding: '16px 0' }}>
      <div
        style={{
          marginBottom: 16,
          display: 'flex',
          justifyContent: 'flex-end',
        }}
      >
        <Button
          type="text"
          icon={<ReloadOutlined />}
          onClick={handleRefresh}
          loading={loading}
          size="small"
        >
          刷新
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={dataSource}
        rowKey="id"
        loading={loading}
        pagination={pagination}
        onChange={handleTableChange}
        size="small"
        scroll={{ x: 'max-content', y: 'calc(100vh - 240px)' }}
        locale={{
          emptyText: (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="暂无导出任务"
            />
          ),
        }}
      />
    </div>
  );
};

export default ExportTaskList;
