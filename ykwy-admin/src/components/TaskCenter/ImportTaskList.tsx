import {
  cancelImportTask,
  getImportExportTaskList,
  type ImportExportTask,
  type ImportExportTaskQueryParams,
} from '@/services/importExportTask';
import {
  ExclamationCircleOutlined,
  ReloadOutlined,
  StopOutlined,
} from '@ant-design/icons';
import { Button, Empty, message, Progress, Table, Tooltip } from 'antd';
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table';
import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';

interface ImportTaskListProps {
  refreshTrigger?: number;
  visible?: boolean;
}
export interface TaskResultDto {
  successCount?: number;
  newCount?: number;
  updatedCount?: number;
  failCount?: number;
  errors?: string[];
  details?: Record<string, string>;
}

const ImportTaskList: React.FC<ImportTaskListProps> = ({
  refreshTrigger,
  visible,
}) => {
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<ImportExportTask[]>([]);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
  });

  const refreshInterval = useRef<NodeJS.Timeout | null>(null);

  // 加载数据
  const loadData = async (params?: ImportExportTaskQueryParams) => {
    setLoading(true);
    try {
      // 计算30天前的时间
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const queryParams = {
        taskType: 'import' as const,
        current: params?.current || pagination.current,
        pageSize: params?.pageSize || pagination.pageSize,
        sortBy: 'createdAt',
        sortOrder: 'desc' as const,
        startTime: thirtyDaysAgo.toISOString(), // 只查询最近30天的数据
        ...params,
      };

      const response = await getImportExportTaskList(queryParams);

      if (response.code === 200) {
        // 确保items是数组，避免undefined
        setDataSource(response.data?.items || []);
        setPagination((prev) => ({
          ...prev,
          current: queryParams.current,
          pageSize: queryParams.pageSize,
          total: response.data?.total || 0,
        }));
      } else {
        message.error(response.msg || '获取任务列表失败');
        // 错误时也确保dataSource是数组
        setDataSource([]);
      }
    } catch (error) {
      console.error('获取任务列表失败:', error);
      message.error('获取任务列表失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 自动刷新
  const startAutoRefresh = () => {
    if (refreshInterval.current) {
      clearInterval(refreshInterval.current);
    }

    refreshInterval.current = setInterval(() => {
      // 检查是否有pending状态的任务
      const hasPendingTasks = dataSource.some(
        (task) => task.status === 'pending',
      );
      if (hasPendingTasks) {
        loadData();
      }
    }, 3000); // 每3秒刷新一次
  };

  // 停止自动刷新
  const stopAutoRefresh = () => {
    if (refreshInterval.current) {
      clearInterval(refreshInterval.current);
      refreshInterval.current = null;
    }
  };

  // 初始化加载数据
  useEffect(() => {
    loadData();
    return () => {
      stopAutoRefresh();
    };
  }, []);

  // 响应refreshTrigger变化
  useEffect(() => {
    if (refreshTrigger && visible) {
      loadData();
    }
  }, [refreshTrigger, visible]);

  // 监听数据变化，决定是否启动自动刷新
  useEffect(() => {
    const hasPendingTasks = dataSource.some(
      (task) => task.status === 'pending',
    );
    if (hasPendingTasks) {
      startAutoRefresh();
    } else {
      stopAutoRefresh();
    }
  }, [dataSource]);

  // 取消任务
  const handleCancelTask = async (taskId: string) => {
    try {
      const response = await cancelImportTask(taskId);
      if (response.code === 200) {
        message.success('任务已取消');
        loadData();
      } else {
        message.error(response.msg || '取消任务失败');
      }
    } catch (error: any) {
      console.error('取消任务失败:', error);
      // 检查是否是接口不存在的错误
      if (error?.status === 404 || error?.message?.includes('404')) {
        message.warning('取消功能暂未实现，请等待任务自动完成');
      } else {
        message.error('取消任务失败，请稍后重试');
      }
    }
  };

  // 手动刷新
  const handleRefresh = () => {
    loadData();
  };

  // 表格变化处理
  const handleTableChange = (newPagination: TablePaginationConfig) => {
    loadData({
      current: newPagination.current,
      pageSize: newPagination.pageSize,
    });
  };

  // 渲染结果列
  const renderResult = (record: ImportExportTask) => {
    const { status, result } = record;

    if (status === 'pending') {
      return (
        <div style={{ width: 180 }}>
          <Progress
            percent={85}
            status="active"
            size="small"
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
            format={() => '处理中...'}
          />
        </div>
      );
    }

    let parsedResult: TaskResultDto | null = null;
    if (result) {
      try {
        parsedResult = typeof result === 'string' ? JSON.parse(result) : result;
      } catch {
        // 解析失败时，保持 null
        parsedResult = null;
      }
    }

    if (status === 'success') {
      if (!parsedResult) {
        return <span style={{ color: '#52c41a' }}>导入成功</span>;
      }

      return (
        <div style={{ fontSize: '12px' }}>
          <div className="text-green-600">
            成功：{parsedResult.successCount ?? 0} 条
          </div>
          <div className="text-green-600">
            新增：{parsedResult.newCount ?? 0} 条
          </div>
          <div className="text-green-600">
            更新：{parsedResult.updatedCount ?? 0} 条
          </div>
          {(parsedResult.failCount ?? 0) > 0 && (
            <div style={{ color: '#ff4d4f' }}>
              失败：{parsedResult.failCount} 条
            </div>
          )}
        </div>
      );
    }

    if (status === 'failed') {
      let tooltipContent = '导入失败';

      if (parsedResult) {
        if (
          Array.isArray(parsedResult.errors) &&
          parsedResult.errors.length > 0
        ) {
          tooltipContent = parsedResult.errors.join('\n');
        } else if (
          parsedResult.details &&
          typeof parsedResult.details === 'object' &&
          !Array.isArray(parsedResult.details)
        ) {
          tooltipContent = Object.entries(parsedResult.details)
            .map(([key, value]) => `${key}: ${value}`)
            .join('\n');
        } else {
          tooltipContent = JSON.stringify(parsedResult, null, 2);
        }
      } else if (typeof result === 'string') {
        tooltipContent = result;
      }

      return (
        <div style={{ color: '#ff4d4f', fontSize: '12px' }}>
          <Tooltip
            title={
              <pre style={{ whiteSpace: 'pre-wrap', margin: 0 }}>
                {tooltipContent}
              </pre>
            }
          >
            <ExclamationCircleOutlined /> 导入失败
          </Tooltip>
        </div>
      );
    }

    return '-';
  };

  // 渲染操作列
  const renderActions = (record: ImportExportTask) => {
    if (record.status === 'pending') {
      return (
        <Button
          type="link"
          size="small"
          icon={<StopOutlined />}
          onClick={() => handleCancelTask(record.id)}
          danger
        >
          取消
        </Button>
      );
    }
    return null;
  };

  const columns: ColumnsType<ImportExportTask> = [
    {
      title: '任务名称',
      dataIndex: 'taskName',
      key: 'taskName',
      width: 180,
      ellipsis: true,
    },
    {
      title: '时间',
      dataIndex: 'taskTime',
      key: 'taskTime',
      width: 130,
      render: (text: string) => (
        <span style={{ fontSize: '12px' }}>
          {dayjs(text).format('MM-DD HH:mm')}
        </span>
      ),
    },
    {
      title: '文件名',
      dataIndex: 'fileName',
      key: 'fileName',
      width: 160,
      ellipsis: true,
      render: (text: string) => text || '-',
    },
    {
      title: '结果',
      dataIndex: 'result',
      key: 'result',
      width: 100,
      render: (_, record) => renderResult(record),
    },
    {
      title: '操作',
      key: 'actions',
      width: 70,
      render: (_, record) => renderActions(record),
      fixed: 'right',
    },
  ];

  return (
    <div style={{ padding: '16px 0' }}>
      <div style={{ marginBottom: 16, textAlign: 'right' }}>
        <Button
          type="text"
          icon={<ReloadOutlined />}
          onClick={handleRefresh}
          loading={loading}
          size="small"
        >
          刷新
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={dataSource}
        rowKey="id"
        loading={loading}
        pagination={pagination}
        onChange={handleTableChange}
        size="small"
        scroll={{ x: 'max-content', y: 'calc(100vh - 260px)' }}
        locale={{
          emptyText: (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="暂无导入任务"
            />
          ),
        }}
      />
    </div>
  );
};

export default ImportTaskList;
