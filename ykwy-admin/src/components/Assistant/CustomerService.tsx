import { testProductDto } from '@/constants/product';
import React, { useState } from 'react';
import './CustomerService.css';
// import {GoodsService} from "../services/goods.ts";

interface Message {
  id: number;
  question: string;
  answer: string;
  timestamp: Date;
  semantic: string;
}

// const goodsService = new GoodsService()

const CustomerService: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('smart-assistant');
  const [searchQuery, setSearchQuery] = useState('');
  const [messages] = useState<Message[]>([
    {
      id: 1,
      question: '你好，请问这件衣服有货吗？',
      answer: '您好，这款衣服目前有货，可以直接下单。',
      timestamp: new Date(),
      semantic: '商品库存',
    },
    {
      id: 2,
      question: '你好，请问这件衣服有货吗？',
      answer: '您好，这款衣服目前有货，可以直接下单。',
      timestamp: new Date(),
      semantic: '商品库存',
    },
  ]);

  const [products] = useState<testProductDto[]>([
    {
      id: '040e73ad-c544-48dc-a1ae-f96a5b33b3ca',
      title: '白色 T恤',
      imageUrl: 'https://picsum.photos/seed/zz9Mj/2030/106',
    },
    {
      id: '040e73ad-c544-48dc-a1ae-f96a5b33b3ca',
      title: '黑色 T恤',
      imageUrl: 'https://picsum.photos/seed/zz9Mj/2030/106',
    },
  ]);

  // const fetchProducts = () => {
  //   goodsService.queryGoods()
  //     .then((res) => {
  //       setProducts([res])
  //       console.log('获取到的数据:', res)
  //       console.log('返回数据的类型', typeof res)
  //       console.log('products的数据',products)
  //     })
  //     .catch(error => {
  //       console.error('获取商品数据失败:', error);
  //     });
  // };

  // 监听products状态变化
  // useEffect(() => {
  //   if (products.length > 0) {
  //     console.log(products.length)
  //     console.log('products已更新:', products);
  //   }
  // }, [products]);
  //
  // useEffect(() => {
  //   fetchProducts();
  // }, []);

  const toggleChat = () => {
    setIsOpen(!isOpen);
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  const handleSearch = () => {
    console.log('搜索:', searchQuery);
    // 实际搜索逻辑
  };

  // 上报消息
  const handleReport = (messageId: number) => {
    console.log(`上报消息，消息ID：${messageId}`);
    // 可在此补充实际上报逻辑，比如调用接口等
  };

  return (
    <div
      className={`customer-service-container ${isOpen ? 'open' : ''}`}
      data-oid="nmtw84b"
    >
      {isOpen ? (
        <div className="chat-box" data-oid="01tdibm">
          {/* 新增标题容器，放在 chat-header 上一级 */}
          <div className="assistant-main-header" data-oid="ao:p8rc">
            <h2 className="assistant-title" data-oid="z_v9anm">
              客服助手
            </h2>
            <button
              type={'button'}
              className="close-btn"
              onClick={toggleChat}
              data-oid="wtaekrr"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                data-oid="i075zlu"
              >
                <path
                  d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
                  data-oid="u5qsqi."
                />
              </svg>
            </button>
          </div>
          <div className="chat-header" data-oid="mgekuj3">
            <div className="tabs" data-oid="u_2z9xo">
              <button
                type={'button'}
                className={`tab ${
                  activeTab === 'smart-assistant' ? 'active' : ''
                }`}
                onClick={() => handleTabChange('smart-assistant')}
                data-oid="ahhsv2f"
              >
                智能辅助
              </button>
            </div>
          </div>

          <div className="content-area" data-oid="y5k8e3c">
            {/* 买家设置板块 */}
            <div className="panel-section" data-oid="w2kd2iq">
              <h3 className="section-title" data-oid="y4qjorw">
                买家设置
              </h3>
              <div className="settings-content" data-oid="nvis-qn">
                <div className="setting-item" data-oid="ocuxv9.">
                  <label className="setting-label" data-oid="6mskm2e">
                    <input type="checkbox" defaultChecked data-oid="zribjjh" />
                    禁用自动回复
                  </label>
                  <div className="setting-value" data-oid="4cr2qh6">
                    <select className="setting-select" data-oid="8uqm3hu">
                      <option value="1" selected data-oid="gp7g:8j">
                        24小时禁用
                      </option>
                      <option value="2" data-oid="o1:wexz">
                        24小时启用
                      </option>
                    </select>
                  </div>
                </div>
                <div className="setting-item" data-oid="kvhklxr">
                  <label className="setting-label" data-oid="m8rulym">
                    <input type="checkbox" data-oid="27vivah" />
                    接收促销通知
                  </label>
                </div>
              </div>
            </div>

            {/* 商品知识板块 */}
            <div className="panel-section" data-oid=":6:xa.a">
              <h3 className="section-title" data-oid="_vikty0">
                商品知识
              </h3>
              <div className="search-container" data-oid="4is0x40">
                <input
                  type="text"
                  placeholder="搜索商品..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  data-oid="esxn28i"
                />

                <button
                  type={'button'}
                  className="search-btn"
                  onClick={handleSearch}
                  data-oid="09x.:uk"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor" // 继承父元素文字颜色
                    strokeWidth="2" // 适当加粗线条
                    data-oid="3:501yp"
                  >
                    <path
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                      data-oid="w5g23z2"
                    />
                  </svg>
                </button>
              </div>

              <div className="products-grid" data-oid="431fa05">
                <h4 className="subsection-title" data-oid="-:v5-vt">
                  焦点商品
                </h4>
                <div className="product-list" data-oid="z7:ju0e">
                  {products && products.length > 0 ? (
                    products.map((product) => (
                      <div
                        key={product.id}
                        className="product-item"
                        data-oid="qle9fr0"
                      >
                        <img
                          src={product.imageUrl}
                          alt={product.title}
                          data-oid="9ufjptg"
                        />

                        <div className="product-info" data-oid="j5h4bnv">
                          <div className="product-name" data-oid="vks0i5i">
                            {product.title}
                          </div>
                          {/*<div className="product-price">¥{product.price}</div>*/}
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="empty-message" data-oid="mwuavdb">
                      暂无焦点商品
                    </div>
                  )}
                </div>

                <h4 className="subsection-title" data-oid="lo5tj_9">
                  历史商品
                </h4>
                <div className="product-list" data-oid="6yo6j_m">
                  {products.length === 0 ? (
                    <div className="empty-message" data-oid="pjsjwp8">
                      暂无焦点商品
                    </div>
                  ) : (
                    products &&
                    products.map((product) => (
                      <div
                        key={product.id}
                        className="product-item"
                        data-oid="-yn:haj"
                      >
                        <img
                          src={product.imageUrl}
                          alt={product.title}
                          data-oid="poq0jk6"
                        />

                        <div className="product-info" data-oid="rlk:ca.">
                          <div className="product-name" data-oid="7dvilmp">
                            {product.title}
                          </div>
                          {/*<div className="product-price">¥{product.price}</div>*/}
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>

            {/* 智能辅助板块 */}
            <div className="panel-section" data-oid="i79tboo">
              <h3 className="section-title" data-oid="jj_5bmr">
                智能辅助
              </h3>
              {/* 查找用户和AI的首条消息 */}
              {messages.length === 0 ? (
                <p data-oid="g7qly.e">暂无对话记录</p>
              ) : (
                messages.map((message) => (
                  <div
                    key={message.id}
                    className="conversation-item"
                    data-oid="x:14jq7"
                  >
                    {/* 对话记录 */}
                    {/*<div className="user-msg">*/}
                    <div className="msg-content" data-oid="4965o7l">
                      <span className="msg-tag" data-oid="1vad_gl">
                        问
                      </span>
                      <span data-oid="fa-wm5b">{message.question}</span>
                    </div>
                    <div className="msg-content" data-oid="_g945hu">
                      <span className="msg-tag" data-oid="4okj81c">
                        答
                      </span>
                      <span data-oid="t.q8a_l">{message.answer}</span>
                    </div>
                    <div className="msg-meta" data-oid="zmjcr0_">
                      <span data-oid="_xc1jns">
                        {message.timestamp.toLocaleTimeString()}
                      </span>
                      <span className="semantic-tag" data-oid="3e66n29">
                        语义：{message.semantic}
                      </span>
                      <button
                        type={'button'}
                        className="report-btn"
                        onClick={() => handleReport(message.id)}
                        data-oid="kla7pao"
                      >
                        上报
                      </button>
                    </div>
                    {/*</div>*/}
                  </div>
                ))
              )}
              {/*</div>*/}
            </div>
          </div>
        </div>
      ) : (
        <button
          type={'button'}
          className="floating-btn"
          onClick={toggleChat}
          data-oid="8xq249j"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            data-oid="r.kx8:b"
          >
            <path
              d="M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-7 12h-2v-2h2v2zm0-4h-2V6h2v4z"
              data-oid="plellpw"
            />
          </svg>
        </button>
      )}
    </div>
  );
};

export default CustomerService;
