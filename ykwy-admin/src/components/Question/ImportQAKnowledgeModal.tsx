import {
  downloadQAKnowledgeTemplate,
  importQAKnowledgeFile,
} from '@/services/questionAndAnswer';
import { DownloadOutlined, InboxOutlined } from '@ant-design/icons';
import { Alert, Button, Modal, Progress, Upload, message } from 'antd';
import type { UploadFile } from 'antd/es/upload/interface';
import React, { useState } from 'react';

interface ImportQAKnowledgeModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
}

const ImportQAKnowledgeModal: React.FC<ImportQAKnowledgeModalProps> = ({
  visible,
  onCancel,
  onSuccess,
}) => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [step, setStep] = useState<'select' | 'confirm' | 'uploading'>(
    'select',
  );

  // 模板下载
  const handleDownloadTemplate = async (format: 'excel' | 'csv' = 'excel') => {
    try {
      const { blob, filename } = await downloadQAKnowledgeTemplate(format);
      if (!blob) {
        message.error('下载模版失败，请稍后重试');
        return;
      }
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      message.success(`${format.toUpperCase()}模版文件下载成功`);
    } catch (error) {
      console.error('下载模版失败:', error);
      message.error('下载模版失败，请稍后重试');
    }
  };

  // 文件选择
  const beforeUpload = (file: UploadFile) => {
    const fileType = file.type || '';
    const isExcelOrCsv =
      fileType.includes('sheet') ||
      fileType.includes('csv') ||
      file.name.endsWith('.csv');
    if (!isExcelOrCsv) {
      message.error('只支持Excel或CSV文件');
      return Upload.LIST_IGNORE;
    }
    setFileList([file]);
    setStep('confirm');
    return false;
  };

  // 关闭弹窗重置
  const handleClose = () => {
    setFileList([]);
    setProgress(0);
    setStep('select');
    onCancel();
  };

  // 用户确认上传
  const handleConfirm = async () => {
    setStep('uploading');
    setUploading(true);
    setProgress(10);
    // 模拟进度条
    const timer = setInterval(() => {
      setProgress((p) => (p < 90 ? p + 10 : p));
    }, 300);
    // 实际上传
    try {
      const file = fileList[0] as any;
      const resp = await importQAKnowledgeFile(file);
      clearInterval(timer);
      setProgress(100);
      console.log('导入响应:', resp);

      // 延迟一秒显示完成状态，然后关闭弹窗
      setTimeout(() => {
        setUploading(false);
        message.success('导入完成！');
        handleClose();
        onSuccess(); // 触发父组件刷新数据
      }, 1000);
    } catch (e: any) {
      clearInterval(timer);
      setUploading(false);
      console.error('导入失败:', e);
      // 尝试从错误响应中提取具体信息
      let errorMsg = '上传失败';
      if (e?.response?.data?.msg) {
        errorMsg = e.response.data.msg;
      } else if (e?.message) {
        errorMsg = e.message;
      }
      message.error(errorMsg);
      setStep('select'); // 导入失败时回到选择文件状态
    }
  };

  return (
    <Modal
      open={visible}
      title="导入问答知识库"
      onCancel={handleClose}
      footer={null}
      width={500}
      destroyOnHidden
    >
      <div style={{ marginBottom: 16 }}>
        <Button
          icon={<DownloadOutlined />}
          onClick={() => handleDownloadTemplate('excel')}
        >
          下载Excel模板文件
        </Button>
        <Button
          icon={<DownloadOutlined />}
          onClick={() => handleDownloadTemplate('csv')}
          style={{ marginLeft: 8 }}
        >
          下载CSV模板文件
        </Button>
      </div>
      {step === 'select' && (
        <>
          <Alert
            message="导入说明"
            description={
              <div>
                <div>请上传符合模板格式的Excel或CSV文件。</div>
                <div style={{ color: 'red', marginTop: 4 }}>
                  <b>以下字段为必填：</b> 问题类型、分类、回答、订单状态
                </div>
                <div style={{ marginTop: 8 }}>
                  <b>订单状态</b>
                  ：必须为4位二进制字符串，1代表激活，0代表未激活，从左到右依次为{' '}
                  <b>售前、发货前、发货后、售后</b>
                  ，如&ldquo;1100&rdquo;表示售前和发货前激活。
                </div>
                <div style={{ marginTop: 8 }}>
                  <b>分类</b>：只能填写以下14种之一：
                  <br />
                  <span style={{ color: '#1677ff' }}>
                    开始语、图片识别、无法识别、聊天互动、商品问题、订单相关、活动优惠、下单付款、物流问题、售后问题、发票问题、安装问题、活动问题、其他
                  </span>
                </div>
              </div>
            }
            type="info"
            showIcon
            style={{ marginBottom: 12 }}
          />
          <Upload.Dragger
            name="file"
            multiple={false}
            beforeUpload={beforeUpload}
            fileList={fileList}
            accept=".xlsx,.xls,.csv"
            showUploadList={true}
            maxCount={1}
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">点击或拖拽文件到此处上传</p>
            <p className="ant-upload-hint">
              仅支持Excel或CSV文件，且需符合模板格式
            </p>
          </Upload.Dragger>
        </>
      )}
      {step === 'confirm' && (
        <div style={{ textAlign: 'center', padding: 32 }}>
          <Alert
            message={`已选择文件：${fileList[0]?.name}`}
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
          <div style={{ marginTop: 16 }}>
            <Button onClick={handleClose} style={{ marginRight: 8 }}>
              取消
            </Button>
            <Button type="primary" onClick={handleConfirm} loading={uploading}>
              确认上传
            </Button>
          </div>
        </div>
      )}
      {step === 'uploading' && (
        <div style={{ textAlign: 'center', padding: 32 }}>
          <Progress
            percent={progress}
            status={progress < 100 ? 'active' : 'success'}
          />
          <div style={{ marginTop: 16 }}>正在导入，请稍候...</div>
        </div>
      )}
    </Modal>
  );
};

export default ImportQAKnowledgeModal;
