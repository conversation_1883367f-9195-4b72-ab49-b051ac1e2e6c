import type {
  QuestionConfig,
  QuestionGroup as QuestionGroupType,
} from '@/models/questionAndAnswerUI';
import { PlusOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { Button, Collapse, Tag } from 'antd';
import React from 'react';
import AnswerItem from './AnswerItem';
import ConfigSection from './ConfigSection';

interface QuestionGroupProps {
  group: QuestionGroupType;
  onEdit?: (groupId: string, answerId: string) => void;
  onDelete?: (groupId: string, answerId: string) => void;
  onCopyCondition?: (groupId: string, answerId: string) => void;
  onAddAnswer?: (groupId: string) => void;
  onEditQuestion?: (groupId: string) => void;
  onDeleteQuestion?: (groupId: string) => void;
  onUpdateContent?: (
    groupId: string,
    answerId: string,
    newContent: string,
  ) => void;
  onConfigChange?: (groupId: string, newConfig: QuestionConfig) => void;
}

const QuestionGroup: React.FC<QuestionGroupProps> = ({
  group,
  onEdit,
  onDelete,
  onCopyCondition,
  onAddAnswer,
  // onEditQuestion,
  // onDeleteQuestion,
  onUpdateContent,
  onConfigChange,
}) => {
  const config = {
    orderStatus: group.orderStatus,
    timeValidityLabel: group.timeValidityLabel,
    isPermanent: group.isPermanent,
    matchMode: group.matchMode,
    isCustom: group.isCustom,
    productInfo: group.productId
      ? {
          id: group.productId,
          name: group.productName || '未知商品',
        }
      : undefined,
  };

  const headerContent = (
    <div className="flex items-center justify-between w-full">
      <div className="flex items-center gap-3">
        <span className="text-base font-medium text-blue-600">
          {group.title}
        </span>
        {group.categoryName && (
          <Tag color="cyan" className="text-xs">
            {group.categoryName}
          </Tag>
        )}
      </div>
      {/*<div className="flex items-center gap-4 text-xs text-gray-500">*/}
      {/*  <span>7日咨询量：{group.weeklyConsultationVolume}</span>*/}
      {/*  <span>关联商品：{group.relatedProductsCount}</span>*/}
      {/*  <span>答案数量：{group.answersCount}</span>*/}
      {/*</div>*/}
    </div>
  );

  return (
    <div className="mb-4">
      <Collapse
        defaultActiveKey={group.isExpanded ? ['1'] : []}
        className="border border-gray-200 rounded-lg"
      >
        <Collapse.Panel
          header={headerContent}
          key="1"
          className="bg-blue-50"
          // extra={
          //   <Space>
          //     <Button
          //       type="text"
          //       size="small"
          //       onClick={(e) => {
          //         e.stopPropagation();
          //         onEditQuestion?.(group.id);
          //       }}
          //       className="text-blue-600 hover:text-blue-800"
          //     >
          //       编辑问题
          //     </Button>
          //     <Button
          //       type="text"
          //       size="small"
          //       danger
          //       onClick={(e) => {
          //         e.stopPropagation();
          //         onDeleteQuestion?.(group.id);
          //       }}
          //       className="hover:text-red-600"
          //     >
          //       删除
          //     </Button>
          //   </Space>
          // }
        >
          <div className="p-4">
            <ConfigSection
              config={config}
              onConfigChange={(newConfig) =>
                onConfigChange?.(group.id, newConfig)
              }
            />

            {/* 常见问法样本 */}
            {group.commonQuestionSamples &&
              group.commonQuestionSamples.length > 0 && (
                <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <QuestionCircleOutlined className="text-blue-500" />
                    <span className="text-sm font-medium text-gray-700">
                      常见问法样本：
                    </span>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {group.commonQuestionSamples.map((sample, index) => (
                      <Tag key={index} color="processing" className="text-xs">
                        {sample}
                      </Tag>
                    ))}
                  </div>
                </div>
              )}

            <div className="space-y-3">
              {group.answers.map((answer) => (
                <AnswerItem
                  key={answer.id}
                  answer={answer}
                  groupId={group.id}
                  onEdit={onEdit}
                  onDelete={onDelete}
                  onCopyCondition={onCopyCondition}
                  onUpdateContent={onUpdateContent}
                />
              ))}
            </div>

            <Button
              type="dashed"
              icon={<PlusOutlined />}
              className="w-full mt-4 border-blue-300 text-blue-600 hover:border-blue-400 hover:text-blue-700"
              onClick={() => onAddAnswer?.(group.id)}
            >
              添加回复
            </Button>
          </div>
        </Collapse.Panel>
      </Collapse>
    </div>
  );
};

export default QuestionGroup;
