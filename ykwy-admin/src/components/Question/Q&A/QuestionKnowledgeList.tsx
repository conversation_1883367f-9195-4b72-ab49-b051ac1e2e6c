import type { QuestionKnowledgeListProps } from '@/models/questionAndAnswerUI';
import { Pagination, Spin } from 'antd';
import React, { useState } from 'react';
import QuestionGroup from './QuestionGroup';

const QuestionKnowledgeList: React.FC<QuestionKnowledgeListProps> = ({
  data,
  loading = false,
  onEdit,
  onDelete,
  onCopyCondition,
  onAddAnswer,
  onEditQuestion,
  onDeleteQuestion,
  onUpdateContent,
  onConfigChange,
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10; // 每页显示10条记录

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Spin size="large" />
      </div>
    );
  }

  if (!data || data.length === 0) {
    return <div className="text-center py-8 text-gray-500">暂无数据</div>;
  }

  // 计算分页数据
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentData = data.slice(startIndex, endIndex);
  const total = data.length;

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // 滚动到页面顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="space-y-4">
      {/* 问题列表 */}
      <div className="space-y-4">
        {currentData.map((group) => (
          <QuestionGroup
            key={group.id}
            group={group}
            onEdit={onEdit}
            onDelete={onDelete}
            onCopyCondition={onCopyCondition}
            onAddAnswer={onAddAnswer}
            onEditQuestion={onEditQuestion}
            onDeleteQuestion={onDeleteQuestion}
            onUpdateContent={onUpdateContent}
            onConfigChange={onConfigChange}
          />
        ))}
      </div>

      {/* 分页组件 */}
      {total > pageSize && (
        <div className="flex justify-center py-6">
          <Pagination
            current={currentPage}
            total={total}
            pageSize={pageSize}
            showSizeChanger={false}
            showQuickJumper
            showTotal={(total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
            }
            onChange={handlePageChange}
            className="bg-white px-4 py-2 rounded-lg shadow-sm"
          />
        </div>
      )}
    </div>
  );
};

export default QuestionKnowledgeList;
