import type { KnowledgeCategory } from '@/models/questionAndAnswer';
import { DownOutlined } from '@ant-design/icons';
import { Popover, Space } from 'antd';
import React, { useEffect, useState } from 'react';

interface CategorySelectorProps {
  categoryTree: KnowledgeCategory[];
  value?: string;
  onChange?: (categoryId: string | undefined, categoryCode?: string) => void;
  placeholder?: string;
  style?: React.CSSProperties;
}

function getChildrenByParent(
  tree: KnowledgeCategory[],
  parentId?: string,
): KnowledgeCategory[] {
  if (!parentId) return tree.filter((item) => !item.parentId);
  const findChildren = (
    categories: KnowledgeCategory[],
  ): KnowledgeCategory[] => {
    let result: KnowledgeCategory[] = [];
    categories.forEach((item) => {
      if (item.id === parentId && item.children) {
        result = item.children;
      } else if (item.children) {
        result = result.concat(findChildren(item.children));
      }
    });
    return result;
  };
  return findChildren(tree);
}

const getCategoryById = (
  id: string | undefined,
  tree: KnowledgeCategory[],
): KnowledgeCategory | undefined => {
  if (!id) return undefined;
  for (const item of tree) {
    if (item.id === id) return item;
    if (item.children) {
      const result = getCategoryById(id, item.children);
      if (result) return result;
    }
  }
  return undefined;
};

const CategorySelector: React.FC<CategorySelectorProps> = ({
  categoryTree,
  value,
  onChange,
  style,
}) => {
  const [level1Categories, setLevel1Categories] = useState<KnowledgeCategory[]>(
    [],
  );
  const [level2Categories, setLevel2Categories] = useState<KnowledgeCategory[]>(
    [],
  );
  const [selectedLevel1, setSelectedLevel1] = useState<string | undefined>();
  const [expandedLevel2Id, setExpandedLevel2Id] = useState<string | null>(null);
  const [selectedLevel2, setSelectedLevel2] = useState<string | undefined>();
  const [selectedLevel3Map, setSelectedLevel3Map] = useState<
    Record<string, string | undefined>
  >({});
  // const [selectedCategoryId, setSelectedCategoryId] = useState<string | undefined>();

  useEffect(() => {
    if (categoryTree.length > 0) {
      setLevel1Categories(getChildrenByParent(categoryTree));
    }
  }, [categoryTree]);

  useEffect(() => {
    // setSelectedCategoryId(value);
    // 根据value反推选中状态
    if (value) {
      const category = getCategoryById(value, categoryTree);
      if (category) {
        // 找到父级分类
        const findParents = (
          categories: KnowledgeCategory[],
          targetId: string,
        ): { level1?: string; level2?: string } => {
          for (const item of categories) {
            if (item.id === targetId) {
              return {};
            }
            if (item.children) {
              for (const child of item.children) {
                if (child.id === targetId) {
                  return { level1: item.id };
                }
                if (child.children) {
                  for (const grandChild of child.children) {
                    if (grandChild.id === targetId) {
                      return { level1: item.id, level2: child.id };
                    }
                  }
                }
              }
            }
          }
          return {};
        };

        const parents = findParents(categoryTree, value);
        if (parents.level1) {
          setSelectedLevel1(parents.level1);
          setLevel2Categories(
            getChildrenByParent(categoryTree, parents.level1),
          );
        }
        if (parents.level2) {
          setSelectedLevel2(parents.level2);
          setSelectedLevel3Map({ [parents.level2]: value });
        }
      }
    } else {
      // 清空所有选中状态
      setSelectedLevel1(undefined);
      setSelectedLevel2(undefined);
      setSelectedLevel3Map({});
      setLevel2Categories([]);
    }
  }, [value, categoryTree]);

  const handleLevel1Change = (categoryId: string | undefined) => {
    setSelectedLevel1(categoryId);
    setExpandedLevel2Id(null);
    setSelectedLevel2(undefined);
    setSelectedLevel3Map({});
    setLevel2Categories(
      categoryId ? getChildrenByParent(categoryTree, categoryId) : [],
    );

    const newCategoryId = categoryId;
    // setSelectedCategoryId(newCategoryId);

    const category = getCategoryById(newCategoryId, categoryTree);
    onChange?.(newCategoryId, category?.code);
  };

  const getLevel3CategoriesByLevel2 = (
    level2Id: string,
  ): KnowledgeCategory[] => {
    return getChildrenByParent(categoryTree, level2Id);
  };

  const hasChildren = (categoryId: string): boolean => {
    const findCategory = (categories: KnowledgeCategory[]): boolean => {
      for (const category of categories) {
        if (category.id === categoryId) return !!category.children?.length;
        if (category.children) {
          const found = findCategory(category.children);
          if (found) return true;
        }
      }
      return false;
    };
    return findCategory(categoryTree);
  };

  const renderLevel2Item = (item: KnowledgeCategory) => {
    const isOpen = expandedLevel2Id === item.id;
    const isSelected = selectedLevel2 === item.id;
    const level3Categories = getLevel3CategoriesByLevel2(item.id);
    const triggerPopover = level3Categories.length > 0;

    const popoverContent = (
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          gap: 4,
          maxHeight: 240,
          overflowY: 'auto',
          minWidth: 160,
        }}
      >
        <div
          onClick={() => {
            setSelectedLevel3Map((prev) => ({ ...prev, [item.id]: undefined }));
            const newCategoryId = item.id;
            // setSelectedCategoryId(newCategoryId);
            setExpandedLevel2Id(null);
            setSelectedLevel2(item.id);

            const category = getCategoryById(newCategoryId, categoryTree);
            onChange?.(newCategoryId, category?.code);
          }}
          style={{
            cursor: 'pointer',
            padding: '4px 12px',
            background: !selectedLevel3Map[item.id] ? '#e6f7ff' : undefined,
            borderRadius: 4,
          }}
        >
          全部
        </div>
        {level3Categories.map((sub) => (
          <div
            key={sub.id}
            onClick={() => {
              setSelectedLevel3Map((prev) => ({ ...prev, [item.id]: sub.id }));
              const newCategoryId = sub.id;
              // setSelectedCategoryId(newCategoryId);
              setExpandedLevel2Id(null);
              setSelectedLevel2(item.id);

              const category = getCategoryById(newCategoryId, categoryTree);
              onChange?.(newCategoryId, category?.code);
            }}
            style={{
              cursor: 'pointer',
              padding: '4px 12px',
              background:
                selectedLevel3Map[item.id] === sub.id ? '#bae7ff' : undefined,
              borderRadius: 4,
            }}
          >
            {sub.name}
          </div>
        ))}
      </div>
    );

    return (
      <Popover
        key={item.id}
        content={popoverContent}
        trigger="click"
        open={isOpen}
        onOpenChange={(visible) => {
          setExpandedLevel2Id(visible ? item.id : null);
          if (visible) {
            setSelectedLevel2(item.id);
          }
        }}
        placement="bottomLeft"
        overlayStyle={{ zIndex: 1000 }}
      >
        <span
          onClick={(e) => e.preventDefault()}
          style={{
            cursor: triggerPopover ? 'pointer' : 'default',
            color: isSelected ? '#1890ff' : '#333',
            fontSize: '14px',
            fontWeight: isSelected ? 600 : 'normal',
            display: 'inline-flex',
            alignItems: 'center',
            gap: '4px',
          }}
        >
          {item.name}
          {triggerPopover && (
            <DownOutlined style={{ fontSize: '10px', opacity: 0.6 }} />
          )}
        </span>
      </Popover>
    );
  };

  return (
    <div style={style}>
      <div
        style={{
          marginBottom: '12px',
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          flexWrap: 'wrap',
        }}
      >
        {/*<div style={{ fontSize: '14px', fontWeight: 500, color: '#666', flexShrink: 0 }}>问题分类：</div>*/}
        <Space wrap size={[16, 8]}>
          <span
            onClick={() => handleLevel1Change(undefined)}
            style={{
              cursor: 'pointer',
              color: !selectedLevel1 ? '#1890ff' : '#333',
              fontSize: '14px',
              fontWeight: !selectedLevel1 ? 600 : 'normal',
            }}
          >
            全部
          </span>
          {level1Categories.map((item) => (
            <span
              key={item.id}
              onClick={() => handleLevel1Change(item.id)}
              style={{
                cursor: 'pointer',
                color: selectedLevel1 === item.id ? '#1890ff' : '#333',
                fontSize: '14px',
                fontWeight: selectedLevel1 === item.id ? 600 : 'normal',
                display: 'inline-flex',
                alignItems: 'center',
                gap: '4px',
              }}
            >
              {item.name}
              {hasChildren(item.id) && (
                <DownOutlined style={{ fontSize: '10px', opacity: 0.6 }} />
              )}
            </span>
          ))}
        </Space>
      </div>

      {level2Categories.length > 0 && (
        <div
          style={{
            marginBottom: '12px',
            backgroundColor: '#f5f5f5',
            padding: '12px',
            borderRadius: '4px',
          }}
        >
          <Space wrap size={[16, 8]}>
            <span
              onClick={() => {
                setExpandedLevel2Id(null);
                setSelectedLevel3Map({});
                setSelectedLevel2(undefined);
                const newCategoryId = selectedLevel1;
                // setSelectedCategoryId(newCategoryId);

                const category = getCategoryById(newCategoryId, categoryTree);
                onChange?.(newCategoryId, category?.code);
              }}
              style={{
                cursor: 'pointer',
                color: !selectedLevel2 ? '#1890ff' : '#333',
                fontSize: '14px',
                fontWeight: !selectedLevel2 ? 600 : 'normal',
              }}
            >
              全部
            </span>
            {level2Categories.map((item) => renderLevel2Item(item))}
          </Space>
        </div>
      )}
    </div>
  );
};

export default CategorySelector;
