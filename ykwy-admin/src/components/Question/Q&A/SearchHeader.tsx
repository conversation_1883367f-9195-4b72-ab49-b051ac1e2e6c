import CategorySelector from '@/components/Question/Q&A/CategoryCascader';
import type { KnowledgeCategory } from '@/models/questionAndAnswer';
import { SearchOutlined } from '@ant-design/icons';
import { Button, Card, Checkbox, Form, Input, Space } from 'antd';
import React from 'react';

interface SearchHeaderProps {
  categoryTree: KnowledgeCategory[];
  loading?: boolean;
  onSearch?: (values: Record<string, unknown>) => void;
}

const SearchHeader: React.FC<SearchHeaderProps> = ({
  categoryTree,
  loading = false,
  onSearch,
}) => {
  const [form] = Form.useForm();

  const handleSearch = () => {
    form.validateFields().then((values) => {
      onSearch?.(values);
    });
  };

  const handleReset = () => {
    form.resetFields();
    onSearch?.({});
  };

  const handleOrderStatusChange = (checkedValues: string[]) => {
    // 将选中的状态转换为四位二进制字符串
    const statusMap = ['售前', '发货前', '发货后', '售后'];
    let binaryString = '0000';

    checkedValues.forEach((status) => {
      const index = statusMap.indexOf(status);
      if (index !== -1) {
        binaryString =
          binaryString.substring(0, index) +
          '1' +
          binaryString.substring(index + 1);
      }
    });

    form.setFieldsValue({ orderStatus: binaryString });
  };

  return (
    <Card className="mb-4">
      <Form form={form} layout="inline" className="w-full">
        {/* 问题分类单独一行 */}
        <div className="w-full mb-4">
          <div className="flex items-center">
            <span className="text-sm font-medium text-gray-600 mr-2 w-20">
              问题分类:
            </span>
            <div className="flex-1">
              <CategorySelector
                style={{ width: '100%', paddingTop: '12px' }}
                categoryTree={categoryTree}
                onChange={(id, code) => {
                  form.setFieldsValue({ categoryId: code });
                }}
                placeholder="请选择问题分类"
              />
            </div>
          </div>
        </div>

        {/* 其他搜索条件在第二行 */}
        <div className="w-full flex items-center gap-4">
          <div className="flex items-center">
            <span className="text-sm font-medium text-gray-600 mr-2 w-20">
              问题类型:
            </span>
            <Input
              placeholder="搜索问题"
              className="w-48"
              onChange={(e) =>
                form.setFieldsValue({ questionType: e.target.value })
              }
            />
          </div>

          <div className="flex items-center">
            <span className="text-sm font-medium text-gray-600 mr-2 w-20">
              订单状态:
            </span>
            <Checkbox.Group
              options={[
                { label: '售前', value: '售前' },
                { label: '发货前', value: '发货前' },
                { label: '发货后', value: '发货后' },
                { label: '售后', value: '售后' },
              ]}
              onChange={handleOrderStatusChange}
              className="flex gap-2"
            />
          </div>

          <div className="flex items-center ml-auto">
            <Space>
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={handleSearch}
                loading={loading}
              >
                搜索
              </Button>
              <Button onClick={handleReset}>重置</Button>
            </Space>
          </div>
        </div>
      </Form>
    </Card>
  );
};

export default SearchHeader;
