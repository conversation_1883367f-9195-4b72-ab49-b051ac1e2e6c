import type { QuestionConfig } from '@/models/questionAndAnswerUI';
import { ShopOutlined } from '@ant-design/icons';
import { Button, Tag } from 'antd';
import React, { useEffect, useState } from 'react';

interface ConfigSectionProps {
  config: QuestionConfig;
  onConfigChange?: (newConfig: QuestionConfig) => void;
}

const ConfigSection: React.FC<ConfigSectionProps> = ({
  config,
  onConfigChange,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [tempOrderStatus, setTempOrderStatus] = useState(config.orderStatus);
  const [originalOrderStatus, setOriginalOrderStatus] = useState(
    config.orderStatus,
  );

  const statusMap = ['售前', '发货前', '发货后', '售后'];

  // 当 config 变化时更新状态
  useEffect(() => {
    setTempOrderStatus(config.orderStatus);
    setOriginalOrderStatus(config.orderStatus);
  }, [config.orderStatus]);

  // 处理订单状态点击
  const handleStatusClick = (index: number) => {
    if (!isEditing) {
      setIsEditing(true);
    }

    const newOrderStatus = tempOrderStatus.split('');
    newOrderStatus[index] = newOrderStatus[index] === '1' ? '0' : '1';
    setTempOrderStatus(newOrderStatus.join(''));
  };

  // 保存配置
  const handleSave = () => {
    if (onConfigChange) {
      onConfigChange({
        ...config,
        orderStatus: tempOrderStatus,
      });
    }
    setIsEditing(false);
    setOriginalOrderStatus(tempOrderStatus);
  };

  // 取消编辑
  const handleCancel = () => {
    setTempOrderStatus(originalOrderStatus);
    setIsEditing(false);
  };

  // 获取当前显示的状态（编辑时显示临时状态，否则显示原始状态）
  const currentOrderStatus = isEditing ? tempOrderStatus : config.orderStatus;

  return (
    <div className="mb-4 p-4 bg-gray-50 rounded-lg">
      <div className="space-y-4">
        <div className="flex flex-wrap items-center gap-6">
          {/* 订单状态 */}
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-600">
              订单状态：
            </span>
            <div className="flex gap-1">
              {statusMap.map((status, index) => {
                const isActive = currentOrderStatus[index] === '1';
                return (
                  <Tag
                    key={status}
                    color={isActive ? 'blue' : 'default'}
                    className={`text-xs cursor-pointer transition-colors ${
                      isActive ? 'hover:bg-blue-100' : 'hover:bg-gray-100'
                    }`}
                    onClick={() => handleStatusClick(index)}
                  >
                    {status}
                  </Tag>
                );
              })}
            </div>
          </div>

          {/* 问答时效 */}
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-600">
              问答时效：
            </span>
            <Tag
              color={config.isPermanent ? 'green' : 'orange'}
              className="text-xs"
            >
              {config.isPermanent
                ? '永久有效'
                : config.timeValidityLabel || '有时效限制'}
            </Tag>
          </div>

          {/* 关联商品（如果有） */}
          {config.productInfo && (
            <div className="flex items-center gap-2">
              <ShopOutlined className="text-blue-500" />
              <span className="text-sm font-medium text-gray-600">
                关联商品：
              </span>
              <Tag color="cyan" className="text-xs">
                {config.productInfo.name}
              </Tag>
            </div>
          )}
        </div>

        {/* 保存和取消按钮 */}
        {isEditing && (
          <div className="flex gap-2 pt-2 border-t border-gray-200">
            <Button
              type="primary"
              size="small"
              onClick={handleSave}
              className="bg-blue-500 hover:bg-blue-600"
            >
              保存
            </Button>
            <Button size="small" onClick={handleCancel}>
              取消
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ConfigSection;
