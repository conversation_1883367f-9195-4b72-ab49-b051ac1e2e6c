import type { Answer } from '@/models/questionAndAnswerUI';
import { Button, Card, Input, message, Space, Tag } from 'antd';
import React, { useState } from 'react';

const { TextArea } = Input;

interface AnswerItemProps {
  answer: Answer;
  groupId: string;
  onEdit?: (groupId: string, answerId: string) => void;
  onDelete?: (groupId: string, answerId: string) => void;
  onCopyCondition?: (groupId: string, answerId: string) => void;
  onUpdateContent?: (
    groupId: string,
    answerId: string,
    newContent: string,
  ) => void;
}

const AnswerItem: React.FC<AnswerItemProps> = ({
  answer,
  groupId,
  onEdit,
  onDelete,
  onCopyCondition,
  onUpdateContent,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(answer.content);

  const handleStartEdit = () => {
    setIsEditing(true);
    setEditContent(answer.content);
  };

  const handleSaveEdit = () => {
    if (editContent.trim() === '') {
      message.error('回答内容不能为空');
      return;
    }

    if (editContent !== answer.content) {
      onUpdateContent?.(groupId, answer.id, editContent);
      message.success('回答内容已更新');
    }
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditContent(answer.content);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      handleCancelEdit();
    }
  };

  return (
    <Card size="small" className="mb-3 border border-gray-200">
      <div className="flex justify-between items-start">
        <div className="flex-1 pr-4">
          <div className="flex items-center gap-2 mb-2">
            <Tag color="blue" className="text-xs">
              第{answer.replyOrder}次回复
            </Tag>
          </div>

          {isEditing ? (
            <div className="mb-3">
              <TextArea
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                onKeyDown={handleKeyPress}
                autoSize={{ minRows: 3, maxRows: 8 }}
                className="text-sm"
                autoFocus
              />
              <div className="flex gap-2 mt-2">
                <Button type="primary" size="small" onClick={handleSaveEdit}>
                  保存
                </Button>
                <Button size="small" onClick={handleCancelEdit}>
                  取消
                </Button>
              </div>
            </div>
          ) : (
            <div
              className="text-sm text-gray-800 leading-relaxed mb-3 cursor-pointer hover:bg-gray-50 p-2 rounded border border-transparent hover:border-gray-300 transition-colors"
              onClick={handleStartEdit}
              title="点击编辑回答内容"
            >
              {answer.content}
            </div>
          )}
        </div>

        {!isEditing && (
          <Space direction="vertical" size="small">
            <Button
              type="link"
              size="small"
              className="p-0 h-auto text-blue-600 hover:text-blue-800"
              onClick={() => onEdit?.(groupId, answer.id)}
            >
              编辑
            </Button>
            <Button
              type="link"
              size="small"
              className="p-0 h-auto text-blue-600 hover:text-blue-800"
              onClick={() => onCopyCondition?.(groupId, answer.id)}
            >
              复制条件
            </Button>
            <Button
              type="link"
              size="small"
              danger
              className="p-0 h-auto"
              onClick={() => onDelete?.(groupId, answer.id)}
            >
              删除
            </Button>
          </Space>
        )}
      </div>
    </Card>
  );
};

export default AnswerItem;
