import { EditOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Card, Input, Table, Tabs } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useState } from 'react';

const { TabPane } = Tabs;

// AI推荐答案数据接口
interface AIReviewItem {
  key: string;
  consultationLevel: number;
  aiRecommendation: {
    category: string;
    question: string;
  };
  actualQuestion: string;
  productInfo: {
    title: string;
    image: string;
    id: string;
  };
  intelligentRecommendation: string;
  action: string;
}

// 模拟数据
const aiReviewItems: AIReviewItem[] = [
  {
    key: '1',
    consultationLevel: 3,
    aiRecommendation: {
      category: '',
      question: '1. 敏感肌可以用吗?',
    },
    actualQuestion: '敏感肌可以用吗?',
    productInfo: {
      title:
        'QINKUNG经典功效面膜深层补水外贸纯羊毛气体后面做居家学户外生打造好用的高端',
      image:
        'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',
      id: '101417746640359',
    },
    intelligentRecommendation:
      '敏感肌肤请先进行小范围试用，您可以选择敏感问题，可以加',
    action: 'AI审核不通过本次',
  },
  {
    key: '2',
    consultationLevel: 2,
    aiRecommendation: {
      category: '',
      question: '1. 身高165，体重是多少大号',
    },
    actualQuestion: '身高165，体重是多少大号',
    productInfo: {
      title:
        'QINKUNG设计2022年科技感单肩包+配饰+护肤品+口红+护手霜+洗面奶【套装】',
      image:
        'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',
      id: '101398515114485',
    },
    intelligentRecommendation:
      '友友您的身高很标准呢，咨询比较问题，需要决定选择以M尺寸',
    action: 'AI审核不通过本次',
  },
  {
    key: '3',
    consultationLevel: 2,
    aiRecommendation: {
      category: '',
      question: '1. 知道书看包产生少开',
    },
    actualQuestion: '知道书看包产生少开',
    productInfo: {
      title: 'QINKUNG设计2024年春季时装H白全国护肤+护手套B管经典管区支架',
      image:
        'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',
      id: '101032556680975',
    },
    intelligentRecommendation: 'L码可以适合1.5.5方的在',
    action: 'AI审核不通过本次',
  },
  {
    key: '4',
    consultationLevel: 2,
    aiRecommendation: {
      category: '',
      question: '1. 买什么尺寸合适',
    },
    actualQuestion: '买什么尺寸合适',
    productInfo: {
      title: 'QINKUNG设计2+二十四季亲服发女乡村女式分帽舱中帮对护舒智能健身器',
      image:
        'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',
      id: '101398009015737',
    },
    intelligentRecommendation: 'XL尺寸，XXL男XL',
    action: 'AI审核不通过本次',
  },
  {
    key: '5',
    consultationLevel: 2,
    aiRecommendation: {
      category: '',
      question: '1. 脚部不不爽走太歪比较什么尺寸的',
    },
    actualQuestion: '脚部不不爽走太歪比较什么尺寸的',
    productInfo: {
      title:
        'QINKUNG设计2022年科技感单肩包+配饰+护肤品+口红+护手霜+洗面奶【套装】',
      image:
        'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',
      id: '101398515114487',
    },
    intelligentRecommendation:
      '建议3XL码，5码是一个尺，还有买尺码的防潮，MM贾全身一些',
    action: 'AI审核不通过本次',
  },
  {
    key: '6',
    consultationLevel: 2,
    aiRecommendation: {
      category: '',
      question: '1. 160算什么型号大码',
    },
    actualQuestion: '160算什么型号大码',
    productInfo: {
      title: 'QINKUNG H类旅游马凤凰申贡J 黄荆顾地旗客高彩分女乡村二十四季亲服',
      image:
        'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',
      id: '101008238439686',
    },
    intelligentRecommendation: '相信得下面，通讯订购',
    action: 'AI审核不通过本次',
  },
  {
    key: '7',
    consultationLevel: 2,
    aiRecommendation: {
      category: '',
      question: '1. 168mm，60公斤，什么规格合适?',
    },
    actualQuestion: '168mm，60公斤，什么规格合适?',
    productInfo: {
      title:
        'QINKUNG设计2022年科技感单肩包+配饰+护肤品+口红+护手霜+洗面奶【套装】',
      image:
        'https://img12.360buyimg.com/n3/jfs/t1/255082/12/19750/125734/67aae426F254a6c56/dfaa8b2b061149df.jpg',
      id: '101398515114482',
    },
    intelligentRecommendation: '就240尺码',
    action: 'AI审核不通过本次',
  },
];

const AIReview: React.FC = () => {
  const [activeTab, setActiveTab] = useState('pending');
  const [searchValue, setSearchValue] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [editingRecord, setEditingRecord] = useState<string | null>(null);
  const [editValue, setEditValue] = useState('');
  const [editedTexts, setEditedTexts] = useState<Record<string, string>>({});

  // 开始编辑
  const handleStartEdit = (recordKey: string, currentValue: string) => {
    setEditingRecord(recordKey);
    setEditValue(currentValue);
  };

  // 保存编辑
  const handleSaveEdit = () => {
    if (editingRecord) {
      // 保存编辑后的文本
      setEditedTexts((prev) => ({
        ...prev,
        [editingRecord]: editValue,
      }));
      console.log('保存编辑:', editValue);
    }
    setEditingRecord(null);
    setEditValue('');
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setEditingRecord(null);
    setEditValue('');
  };

  // 获取要显示的文本（优先显示编辑后的文本，否则显示原始文本）
  const getDisplayText = (record: AIReviewItem) => {
    return editedTexts[record.key] || record.intelligentRecommendation;
  };

  // 待审核表格列定义
  const pendingColumns: ColumnsType<AIReviewItem> = [
    {
      title: (
        <div className="flex items-center gap-1" data-oid="m2f3k2z">
          咨询热度
        </div>
      ),

      dataIndex: 'consultationLevel',
      key: 'consultationLevel',
      width: 120,
      align: 'left',
      render: (level: number, record: AIReviewItem, index: number) => (
        <div className="flex items-center gap-2 py-2 pl-2" data-oid="5i_yvfr">
          <div
            className={`w-6 h-6 rounded-full flex items-center justify-center text-white text-sm font-bold ${
              index === 0
                ? 'bg-red-500'
                : index === 1
                ? 'bg-orange-500'
                : index === 2
                ? 'bg-yellow-500'
                : 'bg-gray-400'
            }`}
            data-oid="fw3omux"
          >
            {index + 1}
          </div>
          <div className="text-sm text-gray-700 font-medium" data-oid="yufak5w">
            {level}
          </div>
        </div>
      ),
    },
    {
      title: (
        <div className="flex items-center gap-1" data-oid="z45u85o">
          AI建议场景
        </div>
      ),

      dataIndex: 'aiRecommendation',
      key: 'aiRecommendation',
      width: 200,
      render: (recommendation: { category: string; question: string }) => (
        <div className="space-y-2 py-2" data-oid="tr8hfm3">
          <div className="text-sm text-blue-600 font-medium" data-oid="zhyjm-o">
            {recommendation.category}
          </div>
          <div
            className="text-sm text-gray-700 leading-relaxed"
            data-oid="-pc4uce"
          >
            {recommendation.question}
          </div>
        </div>
      ),
    },
    {
      title: (
        <div className="flex items-center gap-1" data-oid="5ia_nbm">
          买家问法原声
        </div>
      ),

      dataIndex: 'actualQuestion',
      key: 'actualQuestion',
      width: 180,
      render: (question: string) => (
        <div
          className="text-sm text-gray-700 py-2 leading-relaxed"
          data-oid="d2m4h_q"
        >
          {question}
        </div>
      ),
    },
    {
      title: '商品名称',
      dataIndex: 'productInfo',
      key: 'productInfo',
      width: 320,
      render: (product: { title: string; image: string; id: string }) => (
        <div className="flex items-start gap-3 py-2" data-oid="fru0xm7">
          <div
            className="w-16 h-16 bg-gray-200 rounded flex-shrink-0 overflow-hidden"
            data-oid="w9gsqe2"
          >
            <img
              src={product.image}
              alt="商品图片"
              className="w-full h-full object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src =
                  'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0xMiAxMkgyOFYyOEgxMlYxMloiIGZpbGw9IiNEOUQ5RDkiLz4KPC9zdmc+';
              }}
              data-oid="-pwcf1k"
            />
          </div>
          <div className="flex-1" data-oid="554j780">
            <div
              className="text-sm text-gray-800 leading-5 mb-2"
              style={{
                display: '-webkit-box',
                WebkitLineClamp: 3,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
              }}
              data-oid="z-g6c67"
            >
              {product.title}
            </div>
            <div className="text-sm text-gray-500" data-oid="61djtaj">
              商品ID: {product.id}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: (
        <div className="flex items-center gap-1" data-oid=":ro8fvd">
          客服原始回复
        </div>
      ),

      dataIndex: 'intelligentRecommendation',
      key: 'intelligentRecommendation',
      width: 240,
      render: (text: string) => (
        <div
          className="text-sm text-gray-700 py-2 leading-relaxed"
          data-oid="tt:8y:c"
        >
          {text}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 280,
      fixed: 'right',
      render: (_, record) => (
        <div className="space-y-2 py-2" data-oid="yld1g0e">
          {editingRecord === record.key ? (
            // 编辑模式
            <div className="space-y-3" data-oid="xfk9p4a">
              <div
                className="flex items-center gap-1 text-blue-500 text-sm mb-2"
                data-oid="5bf-v:."
              >
                <span data-oid="-7w_h3a">AI建议采纳话术</span>
              </div>
              <Input.TextArea
                value={editValue}
                onChange={(e) => setEditValue(e.target.value)}
                placeholder={record.intelligentRecommendation}
                rows={3}
                maxLength={500}
                showCount
                className="text-sm"
                data-oid="88ohcnj"
              />

              <div className="flex gap-2" data-oid="twiuly8">
                <Button
                  size="small"
                  type="primary"
                  onClick={handleSaveEdit}
                  data-oid="v-1ncwt"
                >
                  保存
                </Button>
                <Button
                  size="small"
                  onClick={handleCancelEdit}
                  data-oid="_h4.k8z"
                >
                  取消
                </Button>
              </div>
            </div>
          ) : (
            // 正常显示模式
            <div data-oid="97voxng">
              <div className="text-blue-500 text-sm mb-2" data-oid="mvms.rb">
                AI建议采纳话术
              </div>
              <div
                className="bg-gray-100 shadow-sm rounded-md p-3 mb-3 cursor-pointer"
                onClick={() =>
                  handleStartEdit(record.key, getDisplayText(record))
                }
                data-oid="q_z2x.i"
              >
                <div
                  className="text-sm text-gray-700 leading-relaxed underline inline"
                  data-oid="g1l2bu2"
                >
                  {getDisplayText(record)}
                </div>
                <EditOutlined
                  className="text-xs text-gray-500 ml-1"
                  data-oid="1:i5o.x"
                />
              </div>
              <div className="flex flex-wrap gap-2" data-oid="w:81bwk">
                <Button
                  type="link"
                  size="small"
                  className="text-blue-500 px-0 text-sm h-auto"
                  data-oid="2m-er.d"
                >
                  通过
                </Button>
                <Button
                  type="link"
                  size="small"
                  className="text-blue-500 px-0 text-sm h-auto"
                  data-oid="cies5.m"
                >
                  查看详情
                </Button>
                <Button
                  type="link"
                  size="small"
                  className="text-blue-500 px-0 text-sm h-auto"
                  data-oid="v:-e-i."
                >
                  忽略
                </Button>
              </div>
            </div>
          )}
        </div>
      ),
    },
  ];

  // 已处理表格列定义（与图片保持一致）
  const approvedColumns: ColumnsType<AIReviewItem> = [
    {
      title: (
        <div className="flex items-center gap-1" data-oid="4ttxw-v">
          咨询热度
        </div>
      ),

      dataIndex: 'consultationLevel',
      key: 'consultationLevel',
      width: 120,
      align: 'left',
      render: (level: number, record: AIReviewItem, index: number) => (
        <div className="flex items-center gap-2 py-2 pl-2" data-oid="rjpp1e-">
          <div
            className={`w-6 h-6 rounded-full flex items-center justify-center text-white text-sm font-bold ${
              index === 0
                ? 'bg-red-500'
                : index === 1
                ? 'bg-orange-500'
                : index === 2
                ? 'bg-yellow-500'
                : 'bg-gray-400'
            }`}
            data-oid="td9_uyi"
          >
            {index + 1}
          </div>
          <div className="text-sm text-gray-700 font-medium" data-oid="ksvec5z">
            {level}
          </div>
        </div>
      ),
    },
    {
      title: (
        <div className="flex items-center gap-1" data-oid="6g_vzm-">
          AI建议收做答
        </div>
      ),

      dataIndex: 'aiRecommendation',
      key: 'aiRecommendation',
      width: 200,
      render: (recommendation: { category: string; question: string }) => (
        <div className="space-y-2 py-2" data-oid="ofacp9x">
          <div className="text-sm text-blue-600 font-medium" data-oid="c1ctdi.">
            {recommendation.category}
          </div>
          <div
            className="text-sm text-gray-700 leading-relaxed"
            data-oid="2oj4-qj"
          >
            {recommendation.question}
          </div>
        </div>
      ),
    },
    {
      title: (
        <div className="flex items-center gap-1" data-oid="i08q4qv">
          买家问法应答
        </div>
      ),

      dataIndex: 'actualQuestion',
      key: 'actualQuestion',
      width: 180,
      render: (question: string) => (
        <div
          className="text-sm text-gray-700 py-2 leading-relaxed"
          data-oid="_1zt-pr"
        >
          {question}
        </div>
      ),
    },
    {
      title: '商品名称',
      dataIndex: 'productInfo',
      key: 'productInfo',
      width: 320,
      render: (product: { title: string; image: string; id: string }) => (
        <div className="flex items-start gap-3 py-2" data-oid="qwe8zyu">
          <div
            className="w-16 h-16 bg-gray-200 rounded flex-shrink-0 overflow-hidden"
            data-oid=":ben.s:"
          >
            <img
              src={product.image}
              alt="商品图片"
              className="w-full h-full object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src =
                  'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0xMiAxMkgyOFYyOEgxMlYxMloiIGZpbGw9IiNEOUQ5RDkiLz4KPC9zdmc+';
              }}
              data-oid="q-l_p.6"
            />
          </div>
          <div className="flex-1" data-oid="8y0o8pq">
            <div
              className="text-sm text-gray-800 leading-5 mb-2"
              style={{
                display: '-webkit-box',
                WebkitLineClamp: 3,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
              }}
              data-oid="yzw-k6d"
            >
              {product.title}
            </div>
            <div className="text-sm text-gray-500" data-oid="t7-hn9c">
              商品ID: {product.id}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: (
        <div className="flex items-center gap-1" data-oid="rfg_wqk">
          智能推荐回答案
        </div>
      ),

      dataIndex: 'intelligentRecommendation',
      key: 'intelligentRecommendation',
      width: 240,
      render: (text: string) => (
        <div
          className="text-sm text-gray-700 py-2 leading-relaxed"
          data-oid="u7fk.bw"
        >
          {text}
        </div>
      ),
    },
  ];

  // 根据当前标签页获取对应的表格配置
  const getTableConfig = () => {
    switch (activeTab) {
      case 'pending':
        return {
          columns: pendingColumns,
          dataSource: aiReviewItems,
        };
      case 'approved':
        return {
          columns: approvedColumns,
          dataSource: aiReviewItems, // 这里可以用不同的数据源
        };
      default:
        return {
          columns: pendingColumns,
          dataSource: aiReviewItems,
        };
    }
  };

  return (
    <div className="px-4 md:px-6 pb-6" data-oid="6if-4fp">
      {/* AI智能提示框和数据卡片 */}
      <div className="mb-6 flex justify-between items-start" data-oid="bggo_2i">
        {/* 左侧AI提示框 */}
        <div
          className="bg-blue-50 border border-blue-200 rounded-lg p-4 flex items-start gap-3 max-w-2xl"
          data-oid="uklisgr"
        >
          <div
            className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0"
            data-oid=".6.7q5c"
          >
            <span className="text-white text-sm font-bold" data-oid="i7exm2:">
              AI
            </span>
          </div>
          <div data-oid="t20w2:j">
            <div className="text-sm text-blue-800 mb-2" data-oid="womh-j_">
              Hi～，我是您的专属 AI 训练师陪同!
            </div>
            <div
              className="text-xs text-blue-700 leading-relaxed"
              data-oid="eeycj4o"
            >
              自动学习当前店铺&quot;未回复&quot;的买家问题，新发现有
              <span className="font-semibold" data-oid="42q91ml">
                391
              </span>
              个商品相关问题和
              <span className="font-semibold" data-oid="0huvy82">
                0
              </span>
              个通用问题，并推荐了最佳答案话术给您，审核后就可以提升店铺应答率哦，快来看看吧！
            </div>
          </div>
        </div>

        {/* 右侧数据卡片 */}
        <div className="flex gap-3" data-oid="18t5qwv">
          <div
            className="bg-blue-500 text-white px-6 py-4 rounded-xl text-right min-w-[180px]"
            data-oid=":u6n5t-"
          >
            <div className="text-sm mb-1" data-oid="7:_n2r2">
              商品问答知识
            </div>
            <div className="text-3xl font-bold" data-oid="lehu0g1">
              391
            </div>
          </div>
          <div
            className="bg-gradient-to-br from-blue-50 to-blue-100 text-gray-700 px-6 py-4 rounded-xl text-right min-w-[180px] relative"
            data-oid="ni1kv9x"
          >
            <div className="text-sm mb-1" data-oid="dz0ns98">
              通用问答知识
            </div>
            <div className="text-3xl font-bold mb-2" data-oid="5o.755g">
              0
            </div>
            <div
              className="text-sm text-blue-500 cursor-pointer"
              data-oid="iuh8o-6"
            >
              点击查看
            </div>
          </div>
        </div>
      </div>

      <Card data-oid=":gi9tlc">
        {/* 标签页和搜索 */}
        <div
          className="flex flex-col md:flex-row md:items-center md:justify-between mb-4 gap-4"
          data-oid=":obv8he"
        >
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            className="[&_.ant-tabs-tab]:px-4 [&_.ant-tabs-tab]:py-2 [&_.ant-tabs-tab]:text-sm [&_.ant-tabs-tab-active]:text-blue-500 [&_.ant-tabs-ink-bar]:bg-blue-500 [&_.ant-tabs-ink-bar]:h-0.5"
            data-oid="bo532xw"
          >
            <TabPane tab="待审核" key="pending" data-oid="us3vjfb" />
            <TabPane tab="已处理" key="approved" data-oid="-w7ox_h" />
          </Tabs>

          <div className="flex items-center gap-3" data-oid="2zft5cm">
            <span
              className="text-sm text-gray-600 whitespace-nowrap"
              data-oid="e-ox.0g"
            >
              共 391 个问题
            </span>
            <Input
              placeholder="请输入关键词"
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              prefix={
                <SearchOutlined className="text-gray-400" data-oid="8b1kp_i" />
              }
              className="w-full md:w-64"
              allowClear
              data-oid="riahiv8"
            />
          </div>
        </div>

        {/* AI推荐答案表格 */}
        <div className="overflow-x-auto" data-oid="hr_:619">
          <Table
            columns={getTableConfig().columns as any}
            dataSource={getTableConfig().dataSource}
            pagination={{
              current: currentPage,
              pageSize: pageSize,
              total: getTableConfig().dataSource.length,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
              onChange: (page, size) => {
                setCurrentPage(page);
                setPageSize(size || 10);
              },
              onShowSizeChange: (current, size) => {
                setCurrentPage(1);
                setPageSize(size);
              },
              pageSizeOptions: ['5', '10', '20', '50'],
              responsive: true,
            }}
            className="[&_.ant-table-thead>tr>th]:bg-gray-50 [&_.ant-table-thead>tr>th]:font-medium [&_.ant-table-thead>tr>th]:text-gray-700 [&_.ant-table-thead>tr>th]:py-4 [&_.ant-table-tbody>tr>td]:py-4 [&_.ant-table-tbody>tr:hover>td]:bg-blue-50"
            size="middle"
            bordered={false}
            scroll={{ x: 'max-content', y: 'calc(100vh - 500px)' }}
            data-oid="g2clw8y"
          />
        </div>
      </Card>
    </div>
  );
};

export default AIReview;
