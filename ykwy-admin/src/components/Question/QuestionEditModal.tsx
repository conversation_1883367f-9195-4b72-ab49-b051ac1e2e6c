import type {
  KnowledgeCategorySimple,
  QuestionAndAnswer,
  QuestionAndAnswerInput,
} from '@/models/questionAndAnswer';
import { getOrderStatusLabel } from '@/models/questionAndAnswer';
import { CloseOutlined } from '@ant-design/icons';
import { Form, Input, Modal, Select, Tag } from 'antd';
import React, { useState } from 'react';

const { TextArea } = Input;
const { Option } = Select;

interface QuestionEditModalProps {
  visible: boolean;
  currentRecord: QuestionAndAnswer | null;
  categories: KnowledgeCategorySimple[];
  onCancel: () => void;
  onSave: (values: QuestionAndAnswerInput) => Promise<void>;
}

// 订单状态选项配置 - 与查询栏保持一致
const ORDER_STATUS_OPTIONS = [
  '1000', // 售前
  '0100', // 发货前
  '0010', // 发货后
  '0001', // 售后
  '1100', // 售前|发货前
  '1010', // 售前|发货后
  '1001', // 售前|售后
  '0110', // 发货前|发货后
  '0101', // 发货前|售后
  '0011', // 发货后|售后
  '1110', // 售前|发货前|发货后
  '1101', // 售前|发货前|售后
  '1011', // 售前|发货后|售后
  '0111', // 发货前|发货后|售后
  '1111', // 全流程
  '0000', // 无
].map((val) => ({ label: getOrderStatusLabel(val), value: val }));

const QuestionEditModal: React.FC<QuestionEditModalProps> = ({
  visible,
  currentRecord,
  categories,
  onCancel,
  onSave,
}) => {
  const [form] = Form.useForm();

  // 常见问法样本状态
  const [questionSamples, setQuestionSamples] = useState<string[]>([]);
  const [questionInputValue, setQuestionInputValue] = useState('');

  // 回答内容状态
  const [answers, setAnswers] = useState<string[]>([]);
  const [answerInputValue, setAnswerInputValue] = useState('');

  // 当弹窗打开时，设置表单值和状态
  React.useEffect(() => {
    if (visible && currentRecord) {
      // 设置基本表单字段
      form.setFieldsValue({
        questionType: currentRecord.questionType,
        categoryCode: currentRecord.categoryCode,
        orderStatus: currentRecord.orderStatus,
        // 暂时隐藏店铺和商品相关字段
        // shopName: currentRecord.shopName,
        // shopId: currentRecord.shopId,
        // productName: currentRecord.productName,
        // productUrl: currentRecord.productUrl,
      });

      // 设置常见问法样本
      setQuestionSamples(currentRecord.commonQuestionSamples || []);
      setQuestionInputValue('');

      // 设置回答内容
      setAnswers(currentRecord.answers || []);
      setAnswerInputValue('');
    } else if (visible) {
      // 新增时重置表单和状态
      form.resetFields();
      setQuestionSamples([]);
      setQuestionInputValue('');
      setAnswers([]);
      setAnswerInputValue('');
    }
  }, [visible, currentRecord, form]);

  // 处理常见问法输入
  const handleQuestionInputKeyDown = (
    e: React.KeyboardEvent<HTMLTextAreaElement>,
  ) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      const value = questionInputValue.trim();
      if (value && !questionSamples.includes(value)) {
        setQuestionSamples([...questionSamples, value]);
        setQuestionInputValue('');
      }
    }
  };

  // 删除常见问法样本
  const removeQuestionSample = (indexToRemove: number) => {
    setQuestionSamples(
      questionSamples.filter((_, index) => index !== indexToRemove),
    );
  };

  // 处理回答内容输入
  const handleAnswerInputKeyDown = (
    e: React.KeyboardEvent<HTMLTextAreaElement>,
  ) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      const value = answerInputValue.trim();
      if (value && !answers.includes(value)) {
        setAnswers([...answers, value]);
        setAnswerInputValue('');
      }
    }
  };

  // 删除回答内容
  const removeAnswer = (indexToRemove: number) => {
    setAnswers(answers.filter((_, index) => index !== indexToRemove));
  };

  const handleOk = async () => {
    try {
      const values = await form.validateFields();

      // 如果输入框中还有内容，先添加到列表中
      const finalQuestionSamples = [...questionSamples];
      if (questionInputValue.trim()) {
        finalQuestionSamples.push(questionInputValue.trim());
      }

      const finalAnswers = [...answers];
      if (answerInputValue.trim()) {
        finalAnswers.push(answerInputValue.trim());
      }

      // 验证必填字段
      if (finalAnswers.length === 0) {
        form.setFields([
          {
            name: 'answers',
            errors: ['请输入至少一个回答内容'],
          },
        ]);
        return;
      }

      const data: QuestionAndAnswerInput = {
        ...values,
        id: currentRecord?.id,
        commonQuestionSamples: finalQuestionSamples,
        answers: finalAnswers,
      };

      await onSave(data);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setQuestionSamples([]);
    setQuestionInputValue('');
    setAnswers([]);
    setAnswerInputValue('');
    onCancel();
  };

  return (
    <Modal
      title={currentRecord ? '编辑问答' : '新增问答'}
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      width={800}
      destroyOnHidden
    >
      <Form form={form} layout="vertical">
        <Form.Item
          name="questionType"
          label="问题类型"
          rules={[{ required: true, message: '请输入问题类型' }]}
        >
          <Input placeholder="请输入问题类型" />
        </Form.Item>

        <Form.Item
          name="categoryCode"
          label="分类"
          rules={[{ required: true, message: '请选择分类' }]}
        >
          <Select
            placeholder="请选择分类"
            showSearch
            filterOption={(input, option) =>
              option?.children
                ?.toString()
                .toLowerCase()
                .includes(input.toLowerCase()) || false
            }
          >
            {categories.map((category) => (
              <Option key={category.code} value={category.code}>
                {category.name}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="orderStatus"
          label="订单状态"
          rules={[{ required: true, message: '请选择订单状态' }]}
        >
          <Select
            placeholder="请选择订单状态"
            showSearch
            filterOption={(input, option) =>
              option?.children
                ?.toString()
                .toLowerCase()
                .includes(input.toLowerCase()) || false
            }
          >
            {ORDER_STATUS_OPTIONS.map((option) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          label="常见问法样本"
          help="输入问法后按回车键添加，如需在问法内换行请使用 Shift+Enter"
        >
          <div
            style={{
              border: '1px solid #d9d9d9',
              borderRadius: '6px',
              padding: '8px',
              minHeight: '100px',
            }}
          >
            {/* 显示已添加的样本 */}
            <div
              style={{ marginBottom: questionSamples.length > 0 ? '8px' : '0' }}
            >
              {questionSamples.map((sample, index) => (
                <Tag
                  key={`question-${index}-${sample.slice(0, 10)}`}
                  closable
                  closeIcon={<CloseOutlined />}
                  onClose={() => removeQuestionSample(index)}
                  style={{
                    marginBottom: '4px',
                    marginRight: '4px',
                    maxWidth: '300px',
                    whiteSpace: 'pre-wrap',
                    height: 'auto',
                    lineHeight: '1.4',
                  }}
                >
                  {sample}
                </Tag>
              ))}
            </div>
            {/* 输入框 */}
            <TextArea
              value={questionInputValue}
              onChange={(e) => setQuestionInputValue(e.target.value)}
              onKeyDown={handleQuestionInputKeyDown}
              placeholder="请输入常见问法样本，按回车键添加..."
              autoSize={{ minRows: 1, maxRows: 4 }}
              bordered={false}
              style={{ padding: 0, resize: 'none' }}
            />
          </div>
        </Form.Item>

        <Form.Item
          name="answers"
          label="回答内容"
          required
          rules={[
            {
              validator: () => {
                if (answers.length === 0 && !answerInputValue.trim()) {
                  return Promise.reject(new Error('请输入至少一个回答内容'));
                }
                return Promise.resolve();
              },
            },
          ]}
          help="输入回答后按回车键添加，如需在回答内换行请使用 Shift+Enter"
        >
          <div
            style={{
              border: '1px solid #d9d9d9',
              borderRadius: '6px',
              padding: '8px',
              minHeight: '120px',
            }}
          >
            {/* 显示已添加的回答 */}
            <div style={{ marginBottom: answers.length > 0 ? '8px' : '0' }}>
              {answers.map((answer, index) => (
                <Tag
                  key={`answer-${index}-${answer.slice(0, 10)}`}
                  closable
                  closeIcon={<CloseOutlined />}
                  onClose={() => removeAnswer(index)}
                  style={{
                    marginBottom: '4px',
                    marginRight: '4px',
                    maxWidth: '400px',
                    whiteSpace: 'pre-wrap',
                    height: 'auto',
                    lineHeight: '1.4',
                  }}
                >
                  {answer}
                </Tag>
              ))}
            </div>
            {/* 输入框 */}
            <TextArea
              value={answerInputValue}
              onChange={(e) => setAnswerInputValue(e.target.value)}
              onKeyDown={handleAnswerInputKeyDown}
              placeholder="请输入回答内容，按回车键添加..."
              autoSize={{ minRows: 2, maxRows: 6 }}
              bordered={false}
              style={{ padding: 0, resize: 'none' }}
            />
          </div>
        </Form.Item>

        {/* 暂时隐藏店铺和商品相关字段 */}
        {/* <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="shopName" label="店铺名称">
              <Input placeholder="请输入店铺名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="shopId" label="店铺ID">
              <Input placeholder="请输入店铺ID" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="productName" label="商品名称">
              <Input placeholder="请输入商品名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="productUrl" label="商品链接">
              <Input placeholder="请输入商品链接" />
            </Form.Item>
          </Col>
        </Row> */}
      </Form>
    </Modal>
  );
};

export default QuestionEditModal;
