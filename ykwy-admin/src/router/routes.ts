const routes = [
  {
    path: '/',
    redirect: '/home',
  },
  {
    name: '登录',
    path: '/login',
    component: './Login',
    layout: false,
  },
  {
    name: '首页',
    path: '/home',
    component: './Home',
    layout: false,
    wrappers: ['@/components/ProtectedRoute'],
  },
  {
    name: '店铺首页',
    path: '/shop/shop-home',
    component: './Shop/ShopHome',
    wrappers: ['@/components/ProtectedRoute'],
  },
  {
    name: '问答',
    path: '/shop/question',
    wrappers: ['@/components/ProtectedRoute'],
    routes: [
      {
        name: '问答知识库',
        path: '/shop/question/knowledge',
        component: './Shop/Question/Knowledge',
      },
      {
        name: '自动学习',
        path: '/shop/question/auto-learning',
        component: './Shop/Question/AutoLearning',
      },
      {
        name: '精准意图',
        path: '/shop/question/intent',
        component: './Shop/Question/Intent',
      },
      {
        name: '活动管理',
        path: '/shop/question/activity',
        component: './Shop/Question/Activity',
      },
    ],
  },
  {
    name: '商品知识库',
    path: '/shop/product',
    wrappers: ['@/components/ProtectedRoute'],
    routes: [
      {
        name: '商品列表',
        path: '/shop/product/list-simple',
        component: './Shop/Product/ListSimple',
      },
      {
        name: '尺码表',
        path: '/shop/product/size-table-simple',
        component: './Shop/Product/SizeTableSimple',
      },
    ],
  },
  {
    name: '智能跟单',
    path: '/shop/smart-orders',
    wrappers: ['@/components/ProtectedRoute'],
    routes: [
      {
        name: '跟单任务管理',
        path: '/shop/smart-orders/order-management',
        component: './Shop/SmartOrders/OrderManagement',
      },
      {
        name: '发货受限地址',
        path: '/shop/smart-orders/shipping-restricted-addresses',
        component: './Shop/SmartOrders/ShippingRestrictedAddresses',
      },
    ],
  },
];

export default routes;
