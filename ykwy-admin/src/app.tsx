import type { RunTimeLayoutConfig } from '@umijs/max';
import { RequestConfig } from '@umijs/max';
import { App as AntdApp, Dropdown, message } from 'antd';
import Cookies from 'js-cookie';
import React from 'react';
import { history } from 'umi';
import ErrorBoundary from './components/ErrorBoundary';
import './index.css';
import {
  setupGlobalErrorHandler,
  setupReactErrorHandler,
} from './utils/errorHandler';
import logger from './utils/logger';
import { setupRequestInterceptor } from './utils/requestInterceptor';

// 全局变量控制是否已经显示过过期提示
let hasShownExpiredMessage = false;

export async function getInitialState(): Promise<{
  name: string;
  shop: string;
  avatar?: boolean;
}> {
  // 初始化日志系统
  if (typeof window !== 'undefined') {
    setupGlobalErrorHandler();
    setupReactErrorHandler();
    setupRequestInterceptor();

    // 日志系统已启动，只记录错误和警告
  }

  return { name: '', shop: '轻功跑步官方旗舰店', avatar: false };
}

export function rootContainer(container: React.ReactNode) {
  return (
    <ErrorBoundary>
      <AntdApp>{container}</AntdApp>
    </ErrorBoundary>
  );
}

export const layout: RunTimeLayoutConfig = ({ initialState }) => {
  if (
    typeof window !== 'undefined' &&
    (window.location.pathname === '/home' ||
      window.location.pathname === '/login')
  ) {
    return {
      logo: false,
      title: '易康无忧智能客服',
      menuRender: false,
      menuDataRender: () => [],
      headerContentRender: false,
    };
  }

  return {
    logo: false,
    title: '易康无忧智能客服',
    menu: {
      locale: false,
    },
    headerContentRender: () => {
      return (
        <div style={{ display: 'flex', alignItems: 'center', height: '100%' }}>
          <span style={{ fontWeight: 600, fontSize: 16, marginRight: 24 }}>
            {initialState?.shop || '店铺名'}
          </span>
          <Dropdown
            menu={{
              items: [
                { key: 'account', label: '账户信息' },
                { key: 'logout', label: '退出登录' },
              ],
              onClick: ({ key }) => {
                if (key === 'logout') {
                  logger.clearUserId();
                  Cookies.remove('access_token');
                  history.push('/login');
                }
              },
            }}
            placement="bottomRight"
          >
            <span style={{ cursor: 'pointer' }}>
              {initialState?.name || '用户'}
            </span>
          </Dropdown>
        </div>
      );
    },
    menuDataRender: () => [
      {
        name: '首页',
        path: '/shop/shop-home',
      },
      {
        name: '问答',
        path: '/shop/question',
        children: [
          { name: '问答知识库', path: '/shop/question/knowledge' },
          { name: '自动学习', path: '/shop/question/auto-learning' },
          { name: '精准意图', path: '/shop/question/intent' },
          { name: '活动管理', path: '/shop/question/activity' },
        ],
      },
      {
        name: '商品知识库',
        path: '/shop/product',
        children: [
          { name: '商品列表', path: '/shop/product/list-simple' },
          { name: '尺码表', path: '/shop/product/size-table-simple' },
        ],
      },
      {
        name: '智能跟单',
        path: '/shop/smart-orders',
        children: [
          { name: '跟单任务管理', path: '/shop/smart-orders/order-management' },
          {
            name: '发货受限地址',
            path: '/shop/smart-orders/shipping-restricted-addresses',
          },
        ],
      },
    ],
  };
};

function handleUnauthorized() {
  // 清除token
  Cookies.remove('access_token');

  // 重置标志变量
  if (!hasShownExpiredMessage) {
    // 设置标志为已显示
    hasShownExpiredMessage = true;

    // 先跳转到登录页面
    history.push('/login');

    // 使用setTimeout确保在页面跳转后显示消息
    setTimeout(() => {
      message.error('登录已过期，请重新登录', 2);
    }, 100);
  }
}

// 处理成功响应
const handleResponseSuccess = (response: { data: any }) => {
  return response;
};

// 处理错误响应
const handleResponseError = (error: any) => {
  // 处理HTTP错误状态码
  if (error.response) {
    const status = error.response.status;
    switch (status) {
      case 400:
        message.error(error.response.data?.message || '请求参数错误');
        break;
      case 401:
        handleUnauthorized();
        break;
      case 403:
        message.error(error.response.data?.message || '服务器拒绝访问');
        break;
      case 404:
        message.error(error.response.data?.message || '请求资源不存在');
        break;
      case 500:
        message.error(error.response.data?.message || '服务器内部错误');
        break;
      default:
        message.error(error.response.data?.message || '请求错误');
    }
  } else {
    message.error('网络错误或服务器未响应');
  }

  return Promise.reject(error);
};

export const request: RequestConfig = {
  timeout: 20000,
  headers: {
    'Content-Type': 'application/json',
  },
  requestInterceptors: [
    (url, options) => {
      const token = Cookies.get('access_token');
      if (token) {
        options.headers = {
          ...options.headers,
          Authorization: `Bearer ${token}`,
        };
      }
      return { url, options };
    },
  ],
  responseInterceptors: [
    [
      (response: any) => handleResponseSuccess(response) as any,
      (error: any) => handleResponseError(error) as any,
    ],
  ],
};
