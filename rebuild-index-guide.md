# 线上索引重建指南

## 概述
您的项目包含AI销售代理的向量索引，用于知识检索和智能回复。当数据更新或索引损坏时，需要重建索引。

## 重建方法

### 方法1：通过API接口重建（推荐）

销售代理服务提供了专门的重建索引API接口：

```bash
# 调用重建索引API
curl -X POST "http://your-sales-agent-url:8000/rebuild-index" \
  -H "Content-Type: application/json"
```

**线上环境示例：**
```bash
# 如果销售代理部署在线上，替换为实际地址
curl -X POST "https://your-sales-agent-domain.com/rebuild-index" \
  -H "Content-Type: application/json"
```

### 方法2：通过脚本重建

如果您有服务器访问权限，可以直接在服务器上运行重建脚本：

```bash
# 进入项目目录
cd /path/to/taobao-product-imformation

# 强制重建索引
python scripts/build_index.py --force
```

### 方法3：Docker容器内重建

如果服务运行在Docker容器中：

```bash
# 进入容器
docker exec -it your-container-name bash

# 运行重建脚本
python scripts/build_index.py --force
```

## 重建过程说明

重建索引时会执行以下步骤：

1. **清理旧索引**：删除现有的向量索引文件
2. **加载数据**：重新加载产品信息、对话数据、销售策略
3. **构建向量索引**：使用LlamaIndex创建新的向量索引
4. **持久化存储**：将索引保存到storage目录
5. **重新初始化代理**：重新初始化销售代理实例

## 注意事项

### 环境变量要求
确保以下环境变量已正确配置：
```bash
OPENAI_API_KEY=your_openai_api_key
OPENAI_API_BASE=your_openai_base_url
```

### 重建时间
- 索引重建可能需要几分钟时间
- 重建期间服务可能暂时不可用
- 建议在低峰期进行重建

### 数据完整性
- 确保data目录下的数据文件完整
- 包括产品描述、QA数据库、对话记录等

## 验证重建结果

重建完成后，可以通过以下方式验证：

1. **检查API响应**：
```bash
curl -X GET "http://your-sales-agent-url:8000/"
```

2. **测试智能回复**：
```bash
curl -X POST "http://your-sales-agent-url:8000/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "介绍一下AI PPT制作工具"}'
```

## 故障排除

### 常见问题

1. **API密钥错误**
   - 检查OPENAI_API_KEY是否正确
   - 确认API密钥有足够的配额

2. **数据文件缺失**
   - 检查data目录下的文件是否完整
   - 确认文件格式正确（JSON格式）

3. **内存不足**
   - 索引构建需要一定内存
   - 可能需要增加服务器内存或优化数据量

### 日志查看
```bash
# 查看应用日志
tail -f logs/app.log

# 或者查看Docker容器日志
docker logs your-container-name -f
```

## 自动化重建

可以设置定期重建索引的计划任务：

```bash
# 添加到crontab，每天凌晨2点重建
0 2 * * * curl -X POST "http://your-sales-agent-url:8000/rebuild-index"
```
