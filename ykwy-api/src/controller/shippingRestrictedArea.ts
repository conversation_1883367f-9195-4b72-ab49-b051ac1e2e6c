import Bun from 'bun';
import type { Context } from 'hono';

import redis from '../client/redis.js';
import { BusinessLogicError, ValidationError } from '../errors/custom.error.ts';
import { ShippingRestrictedAreaRagflowService } from '../services/shippingRestrictedAreaRagflowService';
import { ShippingRestrictedAreaService } from '../services/shippingRestrictedAreaService';
import {
  shippingRestrictedAreaBulkCreateSchema,
  shippingRestrictedAreaBulkDeleteSchema,
  shippingRestrictedAreaParamIdSchema,
  shippingRestrictedAreaQuerySchema,
  upsertShippingRestrictedAreaSchema,
} from '../types/validators/shippingRestrictedAreaValidator';
import { removeScanKeys } from '../utils/cache.js';
import { R } from '../utils/Response.ts';

const shippingRestrictedAreaService = new ShippingRestrictedAreaService();
const shippingRestrictedAreaRagflowService = new ShippingRestrictedAreaRagflowService();

/**
 * 发货受限地址控制器，处理发货受限地址相关的请求
 */
export class ShippingRestrictedAreaController {
  /**
   * 创建或更新发货受限地址
   * @param c Hono Context
   */
  public async upsert(c: Context) {
    const body = await c.req.json();
    // 参数校验
    const validation = upsertShippingRestrictedAreaSchema.safeParse(body);
    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestBody: body,
        endpoint: 'POST /api/v1/shipping-restricted-area',
      });
    }
    // 调用服务层
    const result = await shippingRestrictedAreaService.upsert(validation.data);
    // 业务逻辑校验示例：假设服务层返回null表示地址已存在
    if (!result) {
      throw new BusinessLogicError('地址已存在，不能重复创建', 'BIZ_AREA_EXISTS', 400, { requestBody: body, endpoint: 'POST /api/v1/shipping-restricted-area' });
    }
    // 清除相关缓存
    try {
      // 如果是更新操作（有id），清除详情缓存
      if (validation.data.id) {
        await redis.del(`shipping-restricted-area:detail:${validation.data.id}`);
      }
      // 清除所有列表缓存
      await removeScanKeys(redis, 'shipping-restricted-area:list');
    } catch (cacheError) {
      console.error('缓存清理失败:', cacheError);
    }
    return R.success(c, result);
  }

  /**
   * 获取单个发货受限地址详情
   * @param c Hono Context
   */
  public async findById(c: Context) {
    const params = c.req.param();
    // 参数校验
    const validation = shippingRestrictedAreaParamIdSchema.safeParse(params);
    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestParams: params,
        endpoint: 'GET /api/v1/shipping-restricted-area/:id',
      });
    }
    const cacheKey = `shipping-restricted-area:detail:${validation.data.id}`;
    try {
      // 尝试从缓存获取
      const cached = await redis.get(cacheKey);
      if (cached) {
        return R.success(c, JSON.parse(cached));
      }
    } catch (cacheError) {
      console.error('缓存读取失败:', cacheError);
    }
    // 查询发货受限地址
    const result = await shippingRestrictedAreaService.findById(validation.data.id);
    if (!result) {
      throw new BusinessLogicError('发货受限地址未找到', 'BIZ_AREA_NOT_FOUND', 404, {
        areaId: validation.data.id,
        endpoint: 'GET /api/v1/shipping-restricted-area/:id',
      });
    }
    // 缓存结果
    try {
      const expire = parseInt(Bun.env['REDIS_CACHE_EXPIRE'] || '3600');
      await redis.setex(cacheKey, expire, JSON.stringify(result));
    } catch (cacheError) {
      console.error('缓存写入失败:', cacheError);
    }
    return R.success(c, result);
  }

  /**
   * 获取发货受限地址列表（支持分页、过滤）
   * @param c Hono Context
   */
  public async findMany(c: Context) {
    const query = c.req.query();
    // 参数校验
    const validation = shippingRestrictedAreaQuerySchema.safeParse(query);
    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestQuery: query,
        endpoint: 'GET /api/v1/shipping-restricted-areas',
      });
    }
    // 业务逻辑校验示例：分页参数非法
    if (validation.data.take <= 0 || validation.data.skip < 0) {
      throw new BusinessLogicError('分页参数非法', 'BIZ_INVALID_PAGINATION', 400, { requestQuery: query, endpoint: 'GET /api/v1/shipping-restricted-areas' });
    }
    // 构建缓存键
    const { skip, take, province, city, district, searchText, isActive } = validation.data;
    const cacheKey = `shipping-restricted-area:list:${skip}-${take}-${province || ''}-${city || ''}-${district || ''}-${searchText || ''}-${isActive || ''}`;
    try {
      // 尝试从缓存获取
      const cached = await redis.get(cacheKey);
      if (cached) {
        return R.success(c, JSON.parse(cached));
      }
    } catch (cacheError) {
      console.error('缓存读取失败:', cacheError);
    }
    // 查询发货受限地址列表
    const result = await shippingRestrictedAreaService.findMany(validation.data);
    // 缓存结果
    try {
      const expire = parseInt(Bun.env['REDIS_CACHE_EXPIRE'] || '3600');
      await redis.setex(cacheKey, expire, JSON.stringify(result));
    } catch (cacheError) {
      console.error('缓存写入失败:', cacheError);
    }
    return R.success(c, result);
  }

  /**
   * 删除发货受限地址
   * @param c Hono Context
   */
  public async delete(c: Context) {
    const params = c.req.param();
    // 参数校验
    const validation = shippingRestrictedAreaParamIdSchema.safeParse(params);
    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestParams: params,
        endpoint: 'DELETE /api/v1/shipping-restricted-area/:id',
      });
    }
    // 删除
    const result = await shippingRestrictedAreaService.delete(validation.data.id);
    // 业务逻辑校验示例：假设服务层返回null表示地址不允许删除
    if (!result) {
      throw new BusinessLogicError('地址状态不允许删除', 'BIZ_AREA_DELETE_DENIED', 400, {
        areaId: validation.data.id,
        endpoint: 'DELETE /api/v1/shipping-restricted-area/:id',
      });
    }
    // 清除相关缓存
    try {
      // 清除详情缓存
      await redis.del(`shipping-restricted-area:detail:${validation.data.id}`);
      // 清除所有列表缓存
      await removeScanKeys(redis, 'shipping-restricted-area:list');
    } catch (cacheError) {
      console.error('缓存清理失败:', cacheError);
    }
    return R.success(c, result);
  }

  /**
   * 批量删除发货受限地址
   * @param c Hono Context
   */
  public async bulkDelete(c: Context) {
    const body = await c.req.json();
    // 参数校验
    const validation = shippingRestrictedAreaBulkDeleteSchema.safeParse(body);
    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestBody: body,
        endpoint: 'POST /api/v1/shipping-restricted-areas/bulk-delete',
      });
    }
    // 业务逻辑校验：ids 不能为空
    if (!body.ids || !Array.isArray(body.ids) || body.ids.length === 0) {
      throw new BusinessLogicError('删除ID列表不能为空', 'BIZ_BULK_DELETE_EMPTY', 400, {
        requestBody: body,
        endpoint: 'POST /api/v1/shipping-restricted-areas/bulk-delete',
      });
    }
    // 批量删除
    const result = await shippingRestrictedAreaService.bulkDelete(validation.data.ids);
    // 业务逻辑校验：假设服务层返回null或空数组表示未删除任何地址
    if (!result || result.count === 0) {
      throw new BusinessLogicError('未删除任何地址', 'BIZ_BULK_DELETE_NONE', 400, {
        requestBody: body,
        endpoint: 'POST /api/v1/shipping-restricted-areas/bulk-delete',
      });
    }
    // 清除相关缓存
    try {
      // 清除所有相关详情缓存
      for (const id of validation.data.ids) {
        await redis.del(`shipping-restricted-area:detail:${id}`);
      }
      // 清除所有列表缓存
      await removeScanKeys(redis, 'shipping-restricted-area:list');
    } catch (cacheError) {
      console.error('缓存清理失败:', cacheError);
    }
    return R.success(c, result);
  }

  /**
   * 批量创建发货受限地址
   * @param c Hono Context
   */
  public async bulkCreate(c: Context) {
    const body = await c.req.json();
    // 参数校验 - 验证是数组且内容符合格式
    const validation = shippingRestrictedAreaBulkCreateSchema.safeParse(body);
    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestBody: body,
        endpoint: 'POST /api/v1/shipping-restricted-areas/bulk-create',
      });
    }
    // 业务逻辑校验：数组不能为空
    if (body.length === 0) {
      throw new BusinessLogicError('批量创建列表不能为空', 'BIZ_BULK_CREATE_EMPTY', 400, {
        requestBody: body,
        endpoint: 'POST /api/v1/shipping-restricted-areas/bulk-create',
      });
    }
    // 批量创建
    const result = await shippingRestrictedAreaService.bulkCreate(validation.data);
    // 业务逻辑校验：假设服务层返回null或成功数量为0表示未创建任何地址
    if (!result || result.successCount === 0) {
      throw new BusinessLogicError('未创建任何地址', 'BIZ_BULK_CREATE_NONE', 400, {
        requestBody: body,
        endpoint: 'POST /api/v1/shipping-restricted-areas/bulk-create',
      });
    }
    // 清除相关缓存
    try {
      await removeScanKeys(redis, 'shipping-restricted-area:list');
    } catch (cacheError) {
      console.error('缓存清理失败:', cacheError);
    }
    return R.success(c, result);
  }

  /**
   * 更新发货受限地址状态
   * @param c Hono Context
   */
  public async updateStatus(c: Context) {
    const params = c.req.param();
    const body = await c.req.json();
    // 参数校验
    const paramsValidation = shippingRestrictedAreaParamIdSchema.safeParse(params);
    if (!paramsValidation.success) {
      throw new ValidationError(paramsValidation.error.issues, {
        requestParams: params,
        endpoint: 'PUT /api/v1/shipping-restricted-area/:id/status',
      });
    }
    // 状态值校验
    if (typeof body.isActive !== 'boolean') {
      throw new ValidationError([], {
        message: '状态值必须是布尔类型',
        requestBody: body,
        endpoint: 'PUT /api/v1/shipping-restricted-area/:id/status',
      });
    }
    // 更新状态
    const result = await shippingRestrictedAreaService.updateStatus(paramsValidation.data.id, body.isActive);
    // 业务逻辑校验：假设服务层返回null表示更新失败
    if (!result) {
      throw new BusinessLogicError('状态更新失败', 'BIZ_STATUS_UPDATE_FAILED', 400, {
        areaId: paramsValidation.data.id,
        endpoint: 'PUT /api/v1/shipping-restricted-area/:id/status',
      });
    }
    // 清除相关缓存
    try {
      // 清除详情缓存
      await redis.del(`shipping-restricted-area:detail:${paramsValidation.data.id}`);
      // 清除所有列表缓存
      await removeScanKeys(redis, 'shipping-restricted-area:list');
    } catch (cacheError) {
      console.error('缓存清理失败:', cacheError);
    }
    return R.success(c, result);
  }
  /**
   * 批量更新发货受限地址状态
   * @param c Hono Context
   */
  public async bulkUpdateStatus(c: Context) {
    const body = await c.req.json();
    // 简单校验
    if (!body.ids || !Array.isArray(body.ids) || body.ids.length === 0) {
      throw new ValidationError([], {
        message: 'ids数组不能为空',
        requestBody: body,
        endpoint: 'PUT /api/v1/shipping-restricted-areas/bulk-status',
      });
    }
    if (typeof body.isActive !== 'boolean') {
      throw new ValidationError([], {
        message: '状态值必须是布尔类型',
        requestBody: body,
        endpoint: 'PUT /api/v1/shipping-restricted-areas/bulk-status',
      });
    }
    // 批量更新状态
    const result = await shippingRestrictedAreaService.bulkUpdateStatus(body.ids, body.isActive);
    // 业务逻辑校验：假设服务层返回更新数量为0表示未更新任何地址
    if (!result || result.count === 0) {
      throw new BusinessLogicError('未更新任何地址状态', 'BIZ_BULK_STATUS_UPDATE_NONE', 400, {
        requestBody: body,
        endpoint: 'PUT /api/v1/shipping-restricted-areas/bulk-status',
      });
    }
    // 清除相关缓存
    try {
      // 清除所有相关详情缓存
      for (const id of body.ids) {
        await redis.del(`shipping-restricted-area:detail:${id}`);
      }
      // 清除所有列表缓存
      await removeScanKeys(redis, 'shipping-restricted-area:list');
    } catch (cacheError) {
      console.error('缓存清理失败:', cacheError);
    }
    return R.success(c, result);
  }
  /**
   * 检查地址是否受限
   * @param c Hono Context
   */
  public async checkRestriction(c: Context) {
    const query = c.req.query();
    // 参数校验
    if (!query['province'] || !query['city'] || !query['district']) {
      throw new ValidationError([], {
        message: '省份、城市、县/区参数都必须提供',
        requestQuery: query,
        endpoint: 'GET /api/v1/shipping-restricted-areas/check',
      });
    }
    // 检查是否受限
    const isRestricted = await shippingRestrictedAreaService.checkRestriction(query['province'], query['city'], query['district']);
    return R.success(c, { isRestricted });
  }
  /**
   * 清空所有发货受限地址
   * @param c Hono Context
   */
  public async truncate(c: Context) {
    const result = await shippingRestrictedAreaService.truncate();

    // 业务逻辑校验：假设服务层返回false表示清空失败
    if (!result) {
      throw new BusinessLogicError('清空发货受限地址失败', 'BIZ_TRUNCATE_FAILED', 500, { endpoint: 'DELETE /api/v1/shipping-restricted-areas/truncate' });
    }
    // 清除相关缓存
    try {
      await removeScanKeys(redis, 'shipping-restricted-area:list');
      await removeScanKeys(redis, 'shipping-restricted-area:detail');
    } catch (cacheError) {
      console.error('缓存清理失败:', cacheError);
    }
    return R.success(c, result);
  }

  /**
   * 同步发货受限地址数据到Ragflow知识库
   * @param c Hono Context
   */
  public syncRagflow = async (c: Context) => {
    try {
      const result = await shippingRestrictedAreaRagflowService.syncToRagflow();
      return c.json({ code: 0, data: result });
    } catch (err: unknown) {
      const message = err instanceof Error ? `同步失败: ${err.message}` : '同步失败: 未知错误';
      return c.json({ code: 500, message }, 500);
    }
  };
}
