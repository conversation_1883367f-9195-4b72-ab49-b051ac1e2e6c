import type { Context } from 'hono';

import redis from '../client/redis';
import { ValidationError } from '../errors/custom.error';
import { TimeValidityService } from '../services/timeValidityService';
import { createTimeValiditySchema, timeValidityBulkDeleteSchema, timeValidityParamIdSchema, timeValidityQuerySchema, updateTimeValiditySchema } from '../types/validators/timeValidityValidator';
import { removeScanKeys } from '../utils/cache';
import { R } from '../utils/Response';

const timeValidityService = new TimeValidityService();

export class TimeValidityController {
  /**
   * 创建时效配置
   */
  public async create(c: Context) {
    const body = await c.req.json();
    const validation = createTimeValiditySchema.safeParse(body);

    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestBody: body,
        endpoint: 'POST /api/v1/time-validity',
      });
    }

    const result = await timeValidityService.create(validation.data);

    // 清除相关缓存
    await this.clearCache();

    return R.success(c, result);
  }

  /**
   * 更新时效配置
   */
  public async update(c: Context) {
    const params = c.req.param();
    const body = await c.req.json();

    const paramValidation = timeValidityParamIdSchema.safeParse(params);
    if (!paramValidation.success) {
      throw new ValidationError(paramValidation.error.issues, {
        requestParams: params,
        endpoint: 'PUT /api/v1/time-validity/:id',
      });
    }

    const bodyValidation = updateTimeValiditySchema.safeParse({
      ...body,
      id: paramValidation.data.id,
    });
    if (!bodyValidation.success) {
      throw new ValidationError(bodyValidation.error.issues, {
        requestBody: body,
        endpoint: 'PUT /api/v1/time-validity/:id',
      });
    }

    const result = await timeValidityService.update(bodyValidation.data);

    // 清除相关缓存
    await this.clearCache();
    await redis.del(`time-validity:detail:${paramValidation.data.id}`);

    return R.success(c, result);
  }

  /**
   * 根据ID获取时效配置详情
   */
  public async findById(c: Context) {
    const params = c.req.param();
    const validation = timeValidityParamIdSchema.safeParse(params);

    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestParams: params,
        endpoint: 'GET /api/v1/time-validity/:id',
      });
    }

    // 构建缓存键
    const cacheKey = `time-validity:detail:${validation.data.id}`;
    const cacheString = await redis.get(cacheKey);

    // 如果有缓存，优先返回缓存
    if (cacheString) {
      const result = JSON.parse(cacheString);
      return R.success(c, result);
    }

    const result = await timeValidityService.findById(validation.data.id);

    if (!result) {
      return c.json(
        {
          code: 404,
          message: '时效配置记录不存在',
          data: null,
        },
        404,
      );
    }

    // 加入缓存
    await redis.set(cacheKey, JSON.stringify(result), 'EX', 3600); // 1小时缓存

    return R.success(c, result);
  }

  /**
   * 获取时效配置列表
   */
  public async findMany(c: Context) {
    const query = c.req.query();
    const validation = timeValidityQuerySchema.safeParse(query);

    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestQuery: query,
        endpoint: 'GET /api/v1/time-validity',
      });
    }

    // 构建缓存键（基于查询参数）
    const queryString = JSON.stringify(validation.data);
    const cacheKey = `time-validity:list:${Buffer.from(queryString).toString('base64')}`;
    const cacheString = await redis.get(cacheKey);

    // 如果有缓存，优先返回缓存
    if (cacheString) {
      const result = JSON.parse(cacheString);
      return R.success(c, result);
    }

    const result = await timeValidityService.findMany(validation.data);

    // 加入缓存
    await redis.set(cacheKey, JSON.stringify(result), 'EX', 1800); // 30分钟缓存

    return R.success(c, result);
  }

  /**
   * 删除时效配置
   */
  public async delete(c: Context) {
    const params = c.req.param();
    const validation = timeValidityParamIdSchema.safeParse(params);

    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestParams: params,
        endpoint: 'DELETE /api/v1/time-validity/:id',
      });
    }

    const result = await timeValidityService.delete(validation.data.id);

    // 清除相关缓存
    await this.clearCache();
    await redis.del(`time-validity:detail:${validation.data.id}`);

    return R.success(c, result);
  }

  /**
   * 批量删除时效配置
   */
  public async bulkDelete(c: Context) {
    const body = await c.req.json();
    const validation = timeValidityBulkDeleteSchema.safeParse(body);

    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestBody: body,
        endpoint: 'POST /api/v1/time-validity/bulk-delete',
      });
    }

    const result = await timeValidityService.bulkDelete(validation.data.ids);

    // 清除相关缓存
    await this.clearCache();
    for (const id of validation.data.ids) {
      await redis.del(`time-validity:detail:${id}`);
    }

    return R.success(c, result);
  }

  /**
   * 检查时效配置是否当前有效
   */
  public async checkValidity(c: Context) {
    const params = c.req.param();
    const validation = timeValidityParamIdSchema.safeParse(params);

    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestParams: params,
        endpoint: 'GET /api/v1/time-validity/:id/check',
      });
    }

    const isValid = await timeValidityService.isCurrentlyValid(validation.data.id);

    return R.success(c, {
      id: validation.data.id,
      isValid,
      checkTime: new Date().toISOString(),
    });
  }

  /**
   * 清除相关缓存
   */
  private async clearCache(): Promise<void> {
    await removeScanKeys(redis, 'time-validity:list:');
  }
}
