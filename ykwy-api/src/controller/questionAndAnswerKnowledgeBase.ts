import type { Context } from 'hono';

import redis from '../client/redis';
import { ErrorCode } from '../constants/errorCodes';
import { AppError, ValidationError } from '../errors/custom.error';
import { QuestionAndAnswerKnowledgeBaseService } from '../services/questionAndAnswerKnowledgeBaseService';
import {
  createQuestionAndAnswerKnowledgeBaseSchema,
  questionAndAnswerKnowledgeBaseBulkDeleteSchema,
  questionAndAnswerKnowledgeBaseCategoryCodeParamSchema,
  questionAndAnswerKnowledgeBaseParamIdSchema,
  questionAndAnswerKnowledgeBaseQuerySchema,
  questionAndAnswerKnowledgeBaseSearchSchema,
} from '../types/validators/questionAndAnswerKnowledgeBaseValidator';
import { removeScanKeys } from '../utils/cache';
import { R } from '../utils/Response';

const questionAndAnswerKnowledgeBaseService = new QuestionAndAnswerKnowledgeBaseService();

export class QuestionAndAnswerKnowledgeBaseController {
  /**
   * 创建或更新问答知识库
   */
  public async upsert(c: Context) {
    const body = await c.req.json();
    const validation = createQuestionAndAnswerKnowledgeBaseSchema.safeParse(body);

    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestBody: body,
        endpoint: 'POST /api/v1/qa-knowledge-base',
      });
    }

    const result = await questionAndAnswerKnowledgeBaseService.upsert(validation.data);

    // 清除相关缓存
    await this.clearCache();

    return R.success(c, result);
  }

  /**
   * 根据ID获取问答知识库详情
   */
  public async findById(c: Context) {
    const params = c.req.param();
    const validation = questionAndAnswerKnowledgeBaseParamIdSchema.safeParse(params);

    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestParams: params,
        endpoint: 'GET /api/v1/qa-knowledge-base/:id',
      });
    }

    // 构建缓存键
    const cacheKey = `qa-kb:detail:${validation.data.id}`;
    const cacheString = await redis.get(cacheKey);

    // 如果有缓存，优先返回缓存
    if (cacheString) {
      const result = JSON.parse(cacheString);
      return R.success(c, result);
    }

    const result = await questionAndAnswerKnowledgeBaseService.findById(validation.data.id);

    if (!result) {
      throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, {
        message: '问答知识库记录不存在',
      });
    }

    // 加入缓存
    await redis.set(cacheKey, JSON.stringify(result), 'EX', 3600); // 1小时缓存

    return R.success(c, result);
  }

  /**
   * 获取问答知识库列表
   */
  public async findMany(c: Context) {
    const query = c.req.query();
    const validation = questionAndAnswerKnowledgeBaseQuerySchema.safeParse(query);

    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestQuery: query,
        endpoint: 'GET /api/v1/qa-knowledge-base',
      });
    }

    // 构建缓存键（基于查询参数）
    const queryString = JSON.stringify(validation.data);
    const cacheKey = `qa-kb:list:${Buffer.from(queryString).toString('base64')}`;
    const cacheString = await redis.get(cacheKey);

    // 如果有缓存，优先返回缓存
    if (cacheString) {
      const result = JSON.parse(cacheString);
      return R.success(c, result);
    }

    const result = await questionAndAnswerKnowledgeBaseService.findMany(validation.data);

    // 加入缓存
    await redis.set(cacheKey, JSON.stringify(result), 'EX', 1800); // 30分钟缓存

    return R.success(c, result);
  }

  /**
   * 根据分类编码获取问答知识库
   */
  public async findByCategoryCode(c: Context) {
    const params = c.req.param();
    const validation = questionAndAnswerKnowledgeBaseCategoryCodeParamSchema.safeParse(params);

    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestParams: params,
        endpoint: 'GET /api/v1/qa-knowledge-base/category/:categoryCode',
      });
    }

    const { categoryCode } = validation.data;

    // 构建缓存键
    const cacheKey = `qa-kb:category:${categoryCode}`;
    const cacheString = await redis.get(cacheKey);

    // 如果有缓存，优先返回缓存
    if (cacheString) {
      const result = JSON.parse(cacheString);
      return R.success(c, result);
    }

    const result = await questionAndAnswerKnowledgeBaseService.findByCategoryCode(categoryCode);

    // 加入缓存
    await redis.set(cacheKey, JSON.stringify(result), 'EX', 1800); // 30分钟缓存

    return R.success(c, result);
  }

  /**
   * 软删除问答知识库
   */
  public async delete(c: Context) {
    const params = c.req.param();
    const validation = questionAndAnswerKnowledgeBaseParamIdSchema.safeParse(params);

    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestParams: params,
        endpoint: 'DELETE /api/v1/qa-knowledge-base/:id',
      });
    }

    const result = await questionAndAnswerKnowledgeBaseService.softDelete(validation.data.id);

    // 清除相关缓存
    await this.clearCache();
    await redis.del(`qa-kb:detail:${validation.data.id}`);

    return R.success(c, result);
  }

  /**
   * 批量软删除问答知识库
   */
  public async bulkDelete(c: Context) {
    const body = await c.req.json();
    const validation = questionAndAnswerKnowledgeBaseBulkDeleteSchema.safeParse(body);

    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestBody: body,
        endpoint: 'POST /api/v1/qa-knowledge-base/bulk-delete',
      });
    }

    const result = await questionAndAnswerKnowledgeBaseService.bulkDelete(validation.data.ids);

    // 清除相关缓存
    await this.clearCache();
    for (const id of validation.data.ids) {
      await redis.del(`qa-kb:detail:${id}`);
    }

    return R.success(c, result);
  }

  /**
   * 根据查询字符串搜索问答知识库
   */
  public async searchByQuery(c: Context) {
    const query = c.req.query();
    const validation = questionAndAnswerKnowledgeBaseSearchSchema.safeParse(query);

    if (!validation.success) {
      throw new ValidationError(validation.error.issues, {
        requestQuery: query,
        endpoint: 'GET /api/v1/qa-knowledge-base/search',
      });
    }

    try {
      const result = await questionAndAnswerKnowledgeBaseService.searchByQuery(validation.data.query, {
        matchMode: validation.data.matchMode,
        validityType: validation.data.validityType,
        onlyValid: validation.data.onlyValid,
      });

      return R.success(c, result);
    } catch (error) {
      console.error('搜索问答知识库失败:', error);
      return c.json(
        {
          code: 500,
          message: '搜索失败',
          data: [],
        },
        500,
      );
    }
  }

  /**
   * 清除相关缓存
   */
  private async clearCache(): Promise<void> {
    await Promise.all([removeScanKeys(redis, 'qa-kb:list:'), removeScanKeys(redis, 'qa-kb:stats:'), removeScanKeys(redis, 'qa-kb:category:')]);
  }
}
