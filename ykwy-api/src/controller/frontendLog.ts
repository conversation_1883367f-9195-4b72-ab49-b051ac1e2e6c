import type { Context } from 'hono';

import { frontendLogRequestSchema } from '../types/validators/frontendLogValidator';
import sendLogT<PERSON>Loki from '../utils/logger';

const serverName = 'ykwy-admin';

export const submitFrontendLog = async (c: Context) => {
  try {
    const body = await c.req.json();
    const validatedData = frontendLogRequestSchema.parse(body);

    // 构建日志消息
    const baseInfo = [`[${validatedData.module.toUpperCase()}]`, validatedData.message];

    // 添加详细信息
    const details = [];
    if (validatedData.url) {
      details.push(`页面: ${validatedData.url}`);
    }
    if (validatedData.userId) {
      details.push(`用户: ${validatedData.userId}`);
    }
    if (validatedData.sessionId) {
      details.push(`会话: ${validatedData.sessionId}`);
    }

    if (validatedData.details) {
      const { details: logDetails } = validatedData;
      if (logDetails.method && logDetails.requestUrl) {
        details.push(`请求: ${logDetails.method} ${logDetails.requestUrl}`);
      }
      if (logDetails.responseStatus) {
        details.push(`状态码: ${logDetails.responseStatus}`);
      }
      if (logDetails.duration) {
        details.push(`耗时: ${logDetails.duration}ms`);
      }
      if (logDetails.stack) {
        details.push(`堆栈: ${logDetails.stack}`);
      }
      if (logDetails.errorType) {
        details.push(`错误类型: ${logDetails.errorType}`);
      }
    }

    const fullMessage = [...baseInfo, ...details].join(' | ');

    // 发送到Loki
    await sendLogToLoki(
      {
        service: serverName,
        level: validatedData.level,
        module: validatedData.module,
        source: 'frontend',
        ...(validatedData.userId && { userId: validatedData.userId }),
        ...(validatedData.sessionId && { sessionId: validatedData.sessionId }),
      },
      fullMessage,
    );

    return c.json({ code: 200, message: '日志提交成功', data: null });
  } catch (error) {
    console.error('提交前端日志失败:', error);

    if (error instanceof Error && error.name === 'ZodError') {
      return c.json({ code: 400, message: '数据验证失败', data: null }, 400);
    }

    return c.json({ code: 500, message: '提交日志失败', data: null }, 500);
  }
};
