import Bun from 'bun';
import type { Context } from 'hono';

import { ErrorCode } from '../constants/errorCodes';
import { AppError } from '../errors/custom.error';
import { UserService } from '../services/userService';
import { generateAccessToken, verifyToken } from '../utils/jwtUtils';

/**
 * 用户控制器，处理用户相关请求
 */
export class UserController {
  private userService: UserService;

  constructor() {
    this.userService = new UserService();
  }

  /**
   * 用户注册
   */
  async register(c: Context) {
    try {
      const { email, password } = await c.req.json();

      if (!email || !password) {
        return c.json(
          {
            code: 400,
            message: '邮箱和密码为必填项',
            data: null,
          },
          400,
        );
      }

      // 使用 Bun 加密密码
      const hashedPassword = await Bun.password.hash(password);
      const user = await this.userService.createUser(email, hashedPassword);
      return c.json(
        {
          code: 0,
          message: '用户注册成功',
          data: { user },
        },
        201,
      );
    } catch (err) {
      if (err instanceof AppError && err.errorCode === ErrorCode.AUTH_USER_ALREADY_EXISTS.code) {
        return c.json(
          {
            code: 409,
            message: err.message,
            data: null,
          },
          409,
        );
      }

      return c.json(
        {
          code: 500,
          message: '注册失败',
          data: null,
        },
        500,
      );
    }
  }

  /**
   * 用户登录（邮箱+密码，返回JWT令牌）
   */
  async login(c: Context) {
    try {
      const { email, password } = await c.req.json();
      if (!email || !password) {
        return c.json({ code: 400, message: '邮箱和密码不能为空', data: null }, 400);
      }

      const user = await this.userService.getUserByEmail(email);
      if (!user) {
        return c.json({ code: 404, message: '用户不存在', data: null }, 404);
      }

      // 用 Bun 校验密码
      const passwordMatch = await Bun.password.verify(password, user.password);

      if (!passwordMatch) {
        return c.json({ code: 401, message: '密码错误', data: null }, 401);
      }

      // 生成JWT令牌
      const accessToken = await generateAccessToken(user.id.toString());
      return c.json({
        code: 0,
        message: '登录成功',
        data: {
          accessToken,
          user: {
            id: user.id,
            email: user.email,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
          },
        },
      });
    } catch {
      return c.json({ code: 500, message: '登录失败', data: null }, 500);
    }
  }

  /**
   * 获取当前用户信息
   */
  async getCurrentUser(c: Context) {
    try {
      const user = c.get('user');
      if (!user) {
        return c.json({ message: '用户未认证' }, 401);
      }
      return c.json({ user }, 200);
    } catch (err) {
      console.error('获取用户信息失败:', err);
      return c.json({ message: '获取用户信息失败', code: 500, data: null }, 500);
    }
  }

  /**
   * 修改用户信息
   */
  async updateUserInfo(c: Context) {
    try {
      const user = c.get('user');
      if (!user) {
        return c.json({ message: '用户未认证' }, 401);
      }
      const updates = await c.req.json();
      const updatedUser = await this.userService.updateUser(user.id, updates);
      return c.json({
        code: 0,
        message: '用户信息更新成功',
        data: updatedUser,
      });
    } catch (err) {
      console.error('更新用户信息失败:', err);
      return c.json({ message: '更新用户信息失败', code: 500, data: null }, 500);
    }
  }

  /**
   * 修改密码
   */
  async changePassword(c: Context) {
    try {
      const user = c.get('user');
      if (!user) {
        return c.json({ error: '用户未认证' }, 401);
      }
      const { oldPassword, newPassword } = await c.req.json();
      if (!oldPassword || !newPassword) {
        return c.json({ code: 400, message: '旧密码和新密码不能为空', data: null }, 400);
      }

      const existingUser = await this.userService.getUserById(user.id);
      if (!existingUser) {
        return c.json({ code: 404, message: '用户不存在', data: null }, 404);
      }
      const passwordMatch = await Bun.password.verify(oldPassword, existingUser.password);
      if (!passwordMatch) {
        return c.json({ code: 401, message: '旧密码错误', data: null }, 401);
      }

      const hashedNewPassword = await Bun.password.hash(newPassword);
      await this.userService.updatePassword(user.id, hashedNewPassword);
      return c.json({ code: 0, message: '密码修改成功', data: null });
    } catch (err) {
      console.error('修改密码失败:', err);
      return c.json({ message: '修改密码失败', code: 500, data: null }, 500);
    }
  }

  async refreshToken(c: Context) {
    const { refreshToken } = await c.req.json();
    if (!refreshToken) {
      throw new AppError(ErrorCode.AUTH_NO_CREDENTIALS);
    }

    const decoded = await verifyToken(refreshToken);
    if (decoded.userId !== 'refresh') {
      throw new AppError(ErrorCode.AUTH_JWT_INVALID);
    }

    const user = await this.userService.getUserById(decoded.userId);
    if (!user) {
      throw new AppError(ErrorCode.AUTH_USER_NOT_FOUND);
    }

    const accessToken = await generateAccessToken(user.id.toString());
    return c.json({
      code: 0,
      message: 'Token refreshed successfully',
      data: { accessToken },
    });
  }
}
