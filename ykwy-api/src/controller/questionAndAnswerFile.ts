import type { Context } from 'hono';
import Papa from 'papaparse';
import * as XLSX from 'xlsx';

import { KnowledgeCategoryService } from '../services/knowledgeCategoryService';
import { QuestionAndAnswerService } from '../services/questionAndAnswerService';

const questionAndAnswerService = new QuestionAndAnswerService();
const knowledgeCategoryService = new KnowledgeCategoryService();

// 下载问答知识库导入模板（代码生成，不读静态文件）
export const downloadTemplate = async (c: Context) => {
  try {
    const query = c.req.query();
    const format = (query['format'] as 'excel' | 'csv') || 'excel';

    // 严格按晓多问答知识库（精简版）.xlsx的列名和顺序
    const templateData = [
      {
        分类: '商品问题',
        问题类型: '咨询',
        回答: '["您好，有什么可以帮您？","请问需要什么帮助"]',
        常见问法样本: '["你好","请问"]',
        订单状态: '1100',
      },
      {
        分类: '售后问题',
        问题类型: '退货',
        回答: '["售后问题请联系客服"]',
        常见问法样本: '["退货","售后"]',
        订单状态: '0001',
      },
    ];

    let buffer: Buffer;
    let filename: string;
    let contentType: string;

    if (format === 'csv') {
      // 为CSV特殊处理订单状态，在前面加单引号防止Excel自动转换
      const csvData = templateData.map((row) => ({
        ...row,
        订单状态: `'${row.订单状态}`, // 在前面加单引号
      }));
      const csv = Papa.unparse(csvData, {
        header: true,
        delimiter: ',',
        quotes: true,
      });
      buffer = Buffer.from('\uFEFF' + csv, 'utf-8');
      filename = `问答知识库导入模板_${new Date().toISOString().slice(0, 10)}.csv`;
      contentType = 'text/csv; charset=utf-8';
    } else {
      // 生成Excel
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(templateData);
      worksheet['!cols'] = [
        { wch: 20 }, // 分类
        { wch: 20 }, // 问题类型
        { wch: 40 }, // 回答
        { wch: 40 }, // 常见问法样本
        { wch: 12 }, // 订单状态
      ];
      XLSX.utils.book_append_sheet(workbook, worksheet, '问答知识库');
      const xlsxBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
      buffer = Buffer.from(xlsxBuffer);
      filename = `问答知识库导入模板_${new Date().toISOString().slice(0, 10)}.xlsx`;
      contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    }

    // 直接返回Response对象，避免使用c.header和c.body
    return new Response(buffer, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${encodeURIComponent(filename)}"`,
        'Content-Length': buffer.length.toString(),
      },
    });
  } catch (error) {
    console.error('模板生成失败:', error);
    return c.json({ code: 500, msg: '模版生成失败', data: null }, 500);
  }
};

// 导入问答知识库（支持excel/csv文件上传）
export const importQAKnowledge = async (c: Context) => {
  const startTime = Date.now();
  try {
    // 1. 获取上传文件
    const formData = await c.req.formData();
    const file = formData.get('file') as File | null;
    if (!file) {
      return c.json({ code: 400, msg: '未上传文件', data: null }, 400);
    }
    const ext = file.name.split('.').pop()?.toLowerCase();
    if (!['xlsx', 'xls', 'csv'].includes(ext || '')) {
      return c.json({ code: 400, msg: '仅支持xlsx/xls/csv文件', data: null }, 400);
    }
    // 3. 读取文件内容
    const arrayBuffer = await file.arrayBuffer();
    let rows: Record<string, unknown>[] = [];
    if (ext === 'csv') {
      const text = Buffer.from(arrayBuffer).toString('utf-8');
      const wb = XLSX.read(text, { type: 'string' });
      const sheetName = wb.SheetNames[0];
      if (!sheetName) return c.json({ code: 400, msg: 'CSV文件无内容', data: null }, 400);
      const sheet = wb.Sheets[sheetName];
      if (!sheet) return c.json({ code: 400, msg: 'CSV文件无内容', data: null }, 400);
      rows = XLSX.utils.sheet_to_json(sheet, { defval: '' });
    } else {
      const workbook = XLSX.read(arrayBuffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      if (!sheetName) return c.json({ code: 400, msg: 'Excel文件无内容', data: null }, 400);
      const sheet = workbook.Sheets[sheetName];
      if (!sheet) return c.json({ code: 400, msg: 'Excel文件无内容', data: null }, 400);
      rows = XLSX.utils.sheet_to_json(sheet, { defval: '' });
    }

    // 新增：清洗每个单元格的前后空格和换行符
    rows = rows.map((row) => {
      const cleaned: Record<string, unknown> = {};
      Object.keys(row).forEach((key) => {
        const val = row[key];
        if (typeof val === 'string') {
          cleaned[key] = val.replace(/^[\s\r\n]+|[\s\r\n]+$/g, '');
        } else {
          cleaned[key] = val;
        }
      });
      return cleaned;
    });

    // 4. 分类名->编码映射
    const allCategories = await knowledgeCategoryService.findAllActive();
    const nameToCodeMap: Record<string, string> = {};
    allCategories.forEach((cat) => {
      nameToCodeMap[cat.name] = cat.code;
    });

    const errors: string[] = [];
    const importList = rows
      .map((row: Record<string, unknown>, idx: number) => {
        try {
          const categoryName = (row['分类'] as string) || '';
          const mappedCode = nameToCodeMap[categoryName];
          if (!mappedCode) {
            throw new Error(`第${idx + 2}行分类"${categoryName}"不存在，请检查！`);
          }
          const answers = parseJsonArray(row['回答']);
          const samples = parseJsonArray(row['常见问法样本']);
          // 处理订单状态，去掉CSV中可能的前缀单引号
          let orderStatus = String(row['订单状态'] ?? '');
          if (orderStatus.startsWith("'")) {
            orderStatus = orderStatus.substring(1);
          }

          return {
            questionType: (row['问题类型'] as string) || '',
            categoryCode: mappedCode,
            commonQuestionSamples: samples,
            answers,
            orderStatus,
          };
        } catch (e: unknown) {
          if (e instanceof Error) {
            errors.push(e.message);
          } else {
            errors.push(`第${idx + 2}行数据处理异常`);
          }
          return null;
        }
      })
      .filter(
        (
          item,
        ): item is {
          questionType: string;
          categoryCode: string;
          commonQuestionSamples: string[];
          answers: string[];
          orderStatus: string;
        } => !!item,
      );
    // 5. 批量写入数据库
    if (importList.length > 0) {
      const result = await questionAndAnswerService.batchImport(importList);
      if (result.errors) {
        errors.push(...result.errors);
      }

      const duration = Date.now() - startTime;

      return c.json({
        code: 200,
        msg: '导入成功',
        data: {
          successCount: result.successCount,
          failCount: result.failCount,
          newCount: result.newCount,
          updatedCount: result.updatedCount,
          errors: errors.length > 0 ? errors : undefined,
          duration,
        },
      });
    } else {
      return c.json(
        {
          code: 400,
          msg: '没有有效数据',
          data: { errors },
        },
        400,
      );
    }
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`❌ 导入失败！耗时: ${duration}ms`);
    console.error('导入异常:', error);
    console.error('异常堆栈:', error instanceof Error ? error.stack : error);

    return c.json(
      {
        code: 500,
        msg: error instanceof Error ? error.message : '系统发生意外错误',
        data: { duration },
      },
      500,
    );
  }
};

function parseJsonArray(raw: unknown): string[] {
  if (Array.isArray(raw)) return raw.map(String);

  if (typeof raw === 'string') {
    const trimmed = raw.trim();
    try {
      const parsed = JSON.parse(trimmed);
      if (Array.isArray(parsed)) {
        return parsed.map(String);
      }
    } catch {
      // fallback: 用逗号分割兜底
      return trimmed
        .split(',')
        .map((s) => s.trim().replace(/^['"]+|['"]+$/g, ''))
        .filter(Boolean);
    }
  }

  return [];
}
