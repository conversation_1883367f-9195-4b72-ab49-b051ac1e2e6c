import type { z } from 'zod';

import prisma, { type Prisma } from '../client/prisma.ts';
import type { BulkDeleteResultDto, QuestionAndAnswerDetailDto, QuestionAndAnswerDto, QuestionAndAnswerListDto, QuestionAndAnswerStatsDto } from '../types/dto/questionAndAnswer';
import type { QuestionAndAnswerInput, QuestionAndAnswerQuery } from '../types/request/questionAndAnswer';
import { upsertQuestionAndAnswerSchema } from '../types/validators/questionAndAnswerValidator';
import { type QAImportResult, QAKnowledgeImportTool } from '../utils/qaKnowledgeImportTool.ts';

type UpsertQuestionAndAnswerInput = z.infer<typeof upsertQuestionAndAnswerSchema>;

export class QuestionAndAnswerService {
  /**
   * 创建问答
   */
  async create(data: QuestionAndAnswerInput): Promise<QuestionAndAnswerDto> {
    return prisma.questionAndAnswer.create({
      data,
    });
  }

  /**
   * 更新问答
   */
  async update(id: string, data: Omit<QuestionAndAnswerInput, 'id'>): Promise<QuestionAndAnswerDto> {
    return prisma.questionAndAnswer.update({
      where: { id },
      data,
    });
  }

  /**
   * 创建或更新问答（支持软删除记录的复活）
   */
  async upsert(data: UpsertQuestionAndAnswerInput): Promise<QuestionAndAnswerDto> {
    if (data.id) {
      // 有ID则更新
      const { id, ...updateData } = data;
      return this.update(id, updateData);
    } else {
      // 无ID时，先检查是否存在相同的记录（包括已软删除的）
      const existingRecord = await prisma.questionAndAnswer.findFirst({
        where: {
          questionType: data.questionType,
          orderStatus: data.orderStatus,
          categoryCode: data.categoryCode,
        },
      });
      if (existingRecord) {
        if (existingRecord.isDeleted === 1) {
          // 如果记录被软删除，则复活并更新
          return prisma.questionAndAnswer.update({
            where: { id: existingRecord.id },
            data: {
              ...data,
              isDeleted: 0, // 复活记录
            },
          });
        } else {
          // 如果记录存在且未删除，则更新
          return prisma.questionAndAnswer.update({
            where: { id: existingRecord.id },
            data,
          });
        }
      } else {
        // 如果记录不存在，则创建新记录
        return prisma.questionAndAnswer.create({
          data,
        });
      }
    }
  }

  /**
   * 根据ID获取问答详情（包含分类信息）
   */
  async findById(id: string): Promise<QuestionAndAnswerDetailDto | null> {
    return prisma.questionAndAnswer.findUnique({
      where: { id, isDeleted: 0 },
      include: {
        category: {
          include: {
            parent: {
              select: {
                id: true,
                name: true,
                code: true,
              },
            },
          },
        },
      },
    });
  }

  /**
   * 获取问答列表（支持分页和筛选）
   */
  async findMany(query: QuestionAndAnswerQuery): Promise<QuestionAndAnswerListDto> {
    const { questionType, content, categoryCode, orderStatus, shopName, shopId, productName, skip, take, sortBy, sortOrder, includeDeleted } = query;
    // 构建查询条件
    const where: Prisma.QuestionAndAnswerWhereInput = {
      ...(includeDeleted ? {} : { isDeleted: 0 }),
      ...(questionType && {
        questionType: { contains: questionType, mode: 'insensitive' },
      }),
      ...(content && {
        OR: [{ answers: { has: content } }, { commonQuestionSamples: { has: content } }],
      }),
      ...(categoryCode && { categoryCode }),
      ...(orderStatus && { orderStatus }),
      ...(shopName && {
        shopNmae: { contains: shopName, mode: 'insensitive' },
      }),
      ...(shopId && { shopId }),
      ...(productName && {
        productName: { contains: productName, mode: 'insensitive' },
      }),
    };
    // 并行查询总数和数据
    const [total, items] = await Promise.all([
      prisma.questionAndAnswer.count({ where }),
      prisma.questionAndAnswer.findMany({
        where,
        skip,
        take,
        orderBy: { [sortBy]: sortOrder },
        include: {
          category: {
            select: {
              id: true,
              name: true,
              code: true,
              level: true,
              description: true,
            },
          },
        },
      }),
    ]);
    return { items, total };
  }

  /**
   * 根据问题类型查找问答
   */
  async findByQuestionType(questionType: string): Promise<QuestionAndAnswerDto[]> {
    return prisma.questionAndAnswer.findMany({
      where: {
        questionType: { contains: questionType, mode: 'insensitive' },
        isDeleted: 0,
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * 根据分类编码查找问答
   */
  async findByCategoryCode(categoryCode: string): Promise<QuestionAndAnswerDto[]> {
    return prisma.questionAndAnswer.findMany({
      where: {
        categoryCode,
        isDeleted: 0,
      },
      orderBy: { createdAt: 'desc' },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            code: true,
            level: true,
            description: true,
          },
        },
      },
    });
  }

  /**
   * 根据订单状态查找问答
   */
  async findByOrderStatus(orderStatus: string): Promise<QuestionAndAnswerDto[]> {
    return prisma.questionAndAnswer.findMany({
      where: {
        orderStatus,
        isDeleted: 0,
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * 软删除单个问答
   */
  async softDelete(id: string): Promise<QuestionAndAnswerDto> {
    return prisma.questionAndAnswer.update({
      where: { id },
      data: { isDeleted: 1 },
    });
  }

  /**
   * 批量软删除问答
   */
  async bulkDelete(ids: string[]): Promise<BulkDeleteResultDto> {
    try {
      const result = await prisma.questionAndAnswer.updateMany({
        where: { id: { in: ids } },
        data: { isDeleted: 1 },
      });
      return {
        deletedCount: result.count,
        failedIds: [],
      };
    } catch {
      // 如果批量删除失败，尝试逐个删除以识别失败的ID
      const failedIds: string[] = [];
      let deletedCount = 0;
      for (const id of ids) {
        try {
          await this.softDelete(id);
          deletedCount++;
        } catch {
          failedIds.push(id);
        }
      }
      return { deletedCount, failedIds };
    }
  }

  /**
   * 更新订单状态
   */
  async updateOrderStatus(id: string, orderStatus: string): Promise<QuestionAndAnswerDto> {
    return prisma.questionAndAnswer.update({
      where: { id },
      data: { orderStatus },
    });
  }

  /**
   * 获取分类统计数据
   */
  private async getCategoryStats(whereCondition: Prisma.QuestionAndAnswerWhereInput) {
    const categoryStats = await prisma.questionAndAnswer.groupBy({
      by: ['categoryCode'],
      where: whereCondition,
      _count: { id: true },
      orderBy: { _count: { id: 'desc' } },
    });
    // 获取分类名称
    const categoryNames = await prisma.knowledgeCategory.findMany({
      where: {
        code: { in: categoryStats.map((stat) => stat.categoryCode) },
        isDeleted: 0,
      },
      select: { code: true, name: true },
    });
    const categoryNameMap = new Map(categoryNames.map((cat) => [cat.code, cat.name]));
    return categoryStats.map((stat) => ({
      categoryCode: stat.categoryCode,
      categoryName: categoryNameMap.get(stat.categoryCode) || '未知分类',
      count: stat._count.id,
    }));
  }

  /**
   * 获取问答统计信息
   */
  async getStats(startDate?: string, endDate?: string, groupBy: 'questionType' | 'categoryCode' | 'orderStatus' = 'questionType'): Promise<QuestionAndAnswerStatsDto> {
    const whereCondition: Prisma.QuestionAndAnswerWhereInput = {
      isDeleted: 0,
      ...(startDate && endDate && { createdAt: { gte: new Date(startDate), lte: new Date(endDate) } }),
    };
    const total = await prisma.questionAndAnswer.count({
      where: whereCondition,
    });
    if (groupBy === 'questionType') {
      const typeStats = await prisma.questionAndAnswer.groupBy({
        by: ['questionType'],
        where: whereCondition,
        _count: { id: true },
        orderBy: { _count: { id: 'desc' } },
      });
      const categoryStats = await this.getCategoryStats(whereCondition);
      return {
        total,
        typeStats: typeStats.map((stat) => ({
          questionType: stat.questionType,
          count: stat._count.id,
        })),
        categoryStats,
      };
    } else if (groupBy === 'categoryCode') {
      const categoryStats = await this.getCategoryStats(whereCondition);
      return { total, typeStats: [], categoryStats };
    } else {
      // 按订单状态分组
      const orderStatusStats = await prisma.questionAndAnswer.groupBy({
        by: ['orderStatus'],
        where: whereCondition,
        _count: { id: true },
        orderBy: { _count: { id: 'desc' } },
      });
      return {
        total,
        typeStats: orderStatusStats.map((stat) => ({
          questionType: `订单状态:${stat.orderStatus}`,
          count: stat._count.id,
        })),
        categoryStats: [],
      };
    }
  }

  /**
   * 获取所有唯一的问题类型
   */
  async getUniqueQuestionTypes(): Promise<string[]> {
    const result = await prisma.questionAndAnswer.findMany({
      where: { isDeleted: 0 },
      select: { questionType: true },
      distinct: ['questionType'],
      orderBy: { questionType: 'asc' },
    });
    return result.map((item) => item.questionType);
  }
  /**
   * 批量导入问答知识库
   * @param importList 导入数据数组
   */
  async batchImport(importList: QuestionAndAnswerInput[]): Promise<QAImportResult> {
    return QAKnowledgeImportTool.batchImport(importList, (item) => this.upsert(item));
  }

  /**
   * 根据查询字符串搜索问答知识库
   * 同时匹配问题类型和常见问法样本，返回匹配的回答数组
   */
  async searchByQuery(query: string): Promise<string[]> {
    try {
      // 查询所有未删除的问答记录
      const questionAndAnswers = await prisma.questionAndAnswer.findMany({
        where: {
          isDeleted: 0,
        },
        select: { questionType: true, commonQuestionSamples: true, answers: true },
      });
      // 收集所有匹配的回答
      const allAnswers: string[] = [];
      for (const qa of questionAndAnswers) {
        let isMatched = false;
        // 1. 检查问题类型是否匹配（等于查询字符串）
        if (qa.questionType === query) {
          isMatched = true;
        }
        // 2. 检查常见问法样本是否匹配（与数组中每个元素单独匹配）
        if (!isMatched && qa.commonQuestionSamples && qa.commonQuestionSamples.length > 0) {
          for (const sample of qa.commonQuestionSamples) {
            if (sample === query) {
              isMatched = true;
              break;
            }
          }
        }
        // 如果匹配，添加所有回答
        if (isMatched) {
          allAnswers.push(...qa.answers);
        }
      }
      // 去重并返回
      return [...new Set(allAnswers)];
    } catch (error) {
      console.error('搜索问答知识库时出错:', error);
      return [];
    }
  }

  // 循环分页查全表
  async findAllFromQuestionAndAnswer(baseQuery: Omit<QuestionAndAnswerQuery, 'skip' | 'take'>, pageSize = 100): Promise<QuestionAndAnswerDto[]> {
    let skip = 0;
    let allItems: QuestionAndAnswerDto[] = [];
    while (true) {
      const { items } = await this.findMany({
        ...baseQuery,
        skip,
        take: pageSize,
      });
      allItems = allItems.concat(items);
      if (items.length < pageSize) {
        break;
      }
      skip += pageSize;
    }
    return allItems;
  }
}
