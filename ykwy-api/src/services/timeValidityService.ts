import type { Prisma } from '@prisma/client';

import prisma from '../client/prisma';
import { ErrorCode } from '../constants/errorCodes';
import { AppError } from '../errors/custom.error';
import type { CreateTimeValidityDto, TimeValidityDto, UpdateTimeValidityDto } from '../types/dto/timeValidity';
import type { TimeValidityQuery } from '../types/request/timeValidity';

export class TimeValidityService {
  /**
   * 创建时效配置
   */
  async create(input: CreateTimeValidityDto): Promise<TimeValidityDto> {
    try {
      return await prisma.timeValidity.create({
        data: {
          label: input.label,
          validityType: input.validityType,
          startDateTime: input.startDateTime,
          endDateTime: input.endDateTime,
          startTimeHHMMSS: input.startTimeHHMMSS,
          endTimeHHMMSS: input.endTimeHHMMSS,
          repeatWeekdays: input.repeatWeekdays || [],
          customStartDate: input.customStartDate,
          customEndDate: input.customEndDate,
          customDailyStartTime: input.customDailyStartTime,
          customDailyEndTime: input.customDailyEndTime,
        },
      });
    } catch (error) {
      throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, {
        message: '创建时效配置失败',
        originalError: error,
      });
    }
  }

  /**
   * 更新时效配置
   */
  async update(input: UpdateTimeValidityDto): Promise<TimeValidityDto> {
    try {
      // 检查记录是否存在
      const existing = await prisma.timeValidity.findUnique({
        where: { id: input.id },
      });

      if (!existing) {
        throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, {
          message: '时效配置记录不存在',
        });
      }

      const updateData: Prisma.TimeValidityUpdateInput = {};

      // 只更新提供的字段
      if (input.label !== undefined) updateData.label = input.label;
      if (input.validityType !== undefined) updateData.validityType = input.validityType;
      if (input.startDateTime !== undefined) updateData.startDateTime = input.startDateTime;
      if (input.endDateTime !== undefined) updateData.endDateTime = input.endDateTime;
      if (input.startTimeHHMMSS !== undefined) updateData.startTimeHHMMSS = input.startTimeHHMMSS;
      if (input.endTimeHHMMSS !== undefined) updateData.endTimeHHMMSS = input.endTimeHHMMSS;
      if (input.repeatWeekdays !== undefined) updateData.repeatWeekdays = input.repeatWeekdays;
      if (input.customStartDate !== undefined) updateData.customStartDate = input.customStartDate;
      if (input.customEndDate !== undefined) updateData.customEndDate = input.customEndDate;
      if (input.customDailyStartTime !== undefined) updateData.customDailyStartTime = input.customDailyStartTime;
      if (input.customDailyEndTime !== undefined) updateData.customDailyEndTime = input.customDailyEndTime;

      return await prisma.timeValidity.update({
        where: { id: input.id },
        data: updateData,
      });
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, {
        message: '更新时效配置失败',
        originalError: error,
      });
    }
  }

  /**
   * 根据ID获取时效配置详情
   */
  async findById(id: string): Promise<TimeValidityDto | null> {
    try {
      return await prisma.timeValidity.findUnique({
        where: { id },
      });
    } catch (error) {
      throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, {
        message: '获取时效配置详情失败',
        originalError: error,
      });
    }
  }

  /**
   * 获取时效配置列表
   */
  async findMany(params: TimeValidityQuery): Promise<{
    data: TimeValidityDto[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }> {
    try {
      const where: Prisma.TimeValidityWhereInput = {
        ...(params.label && { label: { contains: params.label, mode: 'insensitive' } }),
        ...(params.validityType && { validityType: params.validityType }),
      };
      const skip = params.skip ?? 0;
      const take = params.take ?? 10;
      const sortBy = params.sortBy ?? 'createdAt';
      const sortOrder = params.sortOrder ?? 'desc';
      const [data, total] = await Promise.all([
        prisma.timeValidity.findMany({
          where,
          skip,
          take,
          orderBy: { [sortBy]: sortOrder },
        }),
        prisma.timeValidity.count({ where }),
      ]);
      const pageSize = take;
      const page = Math.floor(skip / pageSize) + 1;
      return { data, total, page, pageSize, totalPages: Math.ceil(total / pageSize) };
    } catch (error) {
      throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, {
        message: '获取时效配置列表失败',
        originalError: error,
      });
    }
  }

  /**
   * 删除时效配置
   */
  async delete(id: string): Promise<TimeValidityDto> {
    try {
      // 检查是否被问答知识库使用
      const usageCount = await prisma.questionAndAnswerKnowledgeBase.count({
        where: {
          timeValidityId: id,
          isDeleted: 0,
        },
      });

      if (usageCount > 0) {
        throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, {
          message: `该时效配置正在被 ${usageCount} 个问答知识库使用，无法删除`,
        });
      }

      return await prisma.timeValidity.delete({
        where: { id },
      });
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, {
        message: '删除时效配置失败',
        originalError: error,
      });
    }
  }

  /**
   * 批量删除时效配置
   */
  async bulkDelete(ids: string[]): Promise<{
    deletedCount: number;
    deletedIds: string[];
    failedIds: string[];
    errors?: Array<{ id: string; error: string }>;
  }> {
    try {
      const deletedIds: string[] = [];
      const failedIds: string[] = [];
      const errors: Array<{ id: string; error: string }> = [];

      for (const id of ids) {
        try {
          await this.delete(id);
          deletedIds.push(id);
        } catch (error) {
          failedIds.push(id);
          errors.push({
            id,
            error: error instanceof Error ? error.message : '未知错误',
          });
        }
      }

      return {
        deletedCount: deletedIds.length,
        deletedIds,
        failedIds,
        errors: errors.length > 0 ? errors : undefined,
      };
    } catch (error) {
      throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, {
        message: '批量删除时效配置失败',
        originalError: error,
      });
    }
  }

  /**
   * 检查时效配置是否当前有效
   */
  async isCurrentlyValid(id: string): Promise<boolean> {
    try {
      const config = await this.findById(id);
      if (!config) {
        return false;
      }

      const now = new Date();
      const currentTime = now.toTimeString().slice(0, 8); // HH:MM:SS
      const currentWeekday = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][now.getDay()] as string;

      switch (config.validityType) {
        case 'fixed':
          // 固定时段：检查当前时间是否在起止时间范围内
          if (config.startDateTime && config.endDateTime) {
            return now >= config.startDateTime && now <= config.endDateTime;
          }
          return false;

        case 'daily':
          // 每日重复
          if (config.startTimeHHMMSS && config.endTimeHHMMSS) {
            return currentTime >= config.startTimeHHMMSS && currentTime <= config.endTimeHHMMSS;
          }
          return false;

        case 'weekly':
          // 每周重复
          if (config.repeatWeekdays.includes(currentWeekday) && config.startTimeHHMMSS && config.endTimeHHMMSS) {
            return currentTime >= config.startTimeHHMMSS && currentTime <= config.endTimeHHMMSS;
          }
          return false;

        case 'custom':
          // 自定义时段
          if (config.customStartDate && config.customEndDate && config.customDailyStartTime && config.customDailyEndTime) {
            const isWithinDateRange = now >= config.customStartDate && now <= config.customEndDate;
            const isWithinTimeRange = currentTime >= config.customDailyStartTime && currentTime <= config.customDailyEndTime;
            return isWithinDateRange && isWithinTimeRange;
          }
          return false;

        default:
          return false;
      }
    } catch (error) {
      console.error('检查时效配置有效性失败:', error);
      return false;
    }
  }
}
