import prisma, { type Prisma } from '../client/prisma.ts';
import type { KnowledgeCategoryDto, KnowledgeCategoryListDto, KnowledgeCategorySimpleDto, KnowledgeCategoryTreeDto } from '../types/dto/knowledgeCategory';
import type { KnowledgeCategoryQuery } from '../types/request/knowledgeCategory';

export class KnowledgeCategoryService {
  /**
   * 获取知识库分类列表（支持分页和筛选）
   */
  async findMany(query: KnowledgeCategoryQuery): Promise<KnowledgeCategoryListDto> {
    const { name, code, level, parentId, activeOnly, skip, take, sortBy, sortOrder, includeDeleted } = query;

    // 构建查询条件
    const where: Prisma.KnowledgeCategoryWhereInput = {
      ...(includeDeleted ? {} : { isDeleted: 0 }),
      ...(activeOnly && { isActive: true }),
      ...(name && {
        name: { contains: name, mode: 'insensitive' },
      }),
      ...(code && { code }),
      ...(level && { level }),
      ...(parentId && { parentId }),
    };

    // 并行查询总数和数据
    const [total, items] = await Promise.all([
      prisma.knowledgeCategory.count({ where }),
      prisma.knowledgeCategory.findMany({
        where,
        skip,
        take,
        orderBy: { [sortBy]: sortOrder },
      }),
    ]);

    return { items, total };
  }

  /**
   * 根据ID获取分类详情
   */
  async findById(id: string): Promise<KnowledgeCategoryDto | null> {
    return prisma.knowledgeCategory.findUnique({
      where: { id, isDeleted: 0 },
    });
  }

  /**
   * 根据编码获取分类详情
   */
  async findByCode(code: string): Promise<KnowledgeCategoryDto | null> {
    return prisma.knowledgeCategory.findUnique({
      where: { code, isDeleted: 0 },
    });
  }

  /**
   * 获取所有启用的分类（用于下拉选择）
   */
  async findAllActive(): Promise<KnowledgeCategorySimpleDto[]> {
    const categories = await prisma.knowledgeCategory.findMany({
      where: {
        isDeleted: 0,
        isActive: true,
      },
      select: {
        code: true,
        name: true,
        level: true,
        parent: {
          select: {
            code: true,
          },
        },
      },
      orderBy: [{ level: 'asc' }, { sortOrder: 'asc' }],
    });

    return categories.map((category) => ({
      code: category.code,
      name: category.name,
      level: category.level,
      parentCode: category.parent?.code || null,
    }));
  }

  /**
   * 获取分类树形结构
   */
  async getTree(): Promise<KnowledgeCategoryTreeDto[]> {
    const categories = await prisma.knowledgeCategory.findMany({
      where: {
        isDeleted: 0,
        isActive: true,
      },
      orderBy: [{ level: 'asc' }, { sortOrder: 'asc' }],
    });

    // 构建树形结构
    const categoryMap = new Map<string, KnowledgeCategoryTreeDto>();
    const roots: KnowledgeCategoryTreeDto[] = [];

    // 第一遍：创建所有节点
    categories.forEach((category) => {
      categoryMap.set(category.id, {
        ...category,
        children: [],
      });
    });

    // 第二遍：建立父子关系
    categories.forEach((category) => {
      const node = categoryMap.get(category.id)!;
      if (category.parentId && categoryMap.has(category.parentId)) {
        const parent = categoryMap.get(category.parentId)!;
        parent.children!.push(node);
        node.parent = {
          id: parent.id,
          name: parent.name,
          code: parent.code,
        };
      } else {
        roots.push(node);
      }
    });

    return roots;
  }

  /**
   * 根据编码列表批量获取分类信息
   */
  async findByCodes(codes: string[]): Promise<KnowledgeCategorySimpleDto[]> {
    if (codes.length === 0) return [];

    const categories = await prisma.knowledgeCategory.findMany({
      where: {
        code: { in: codes },
        isDeleted: 0,
      },
      select: {
        code: true,
        name: true,
        level: true,
        parent: {
          select: {
            code: true,
          },
        },
      },
      orderBy: [{ level: 'asc' }, { sortOrder: 'asc' }],
    });

    return categories.map((category) => ({
      code: category.code,
      name: category.name,
      level: category.level,
      parentCode: category.parent?.code || null,
    }));
  }

  /**
   * 获取编码到名称的映射
   */
  async getCodeNameMap(): Promise<Record<string, string>> {
    const categories = await prisma.knowledgeCategory.findMany({
      where: {
        isDeleted: 0,
      },
      select: {
        code: true,
        name: true,
      },
    });

    const map: Record<string, string> = {};
    categories.forEach((category) => {
      map[category.code] = category.name;
    });

    return map;
  }

  /**
   * 根据级别获取分类
   */
  async findByLevel(level: number): Promise<KnowledgeCategorySimpleDto[]> {
    const categories = await prisma.knowledgeCategory.findMany({
      where: {
        level,
        isDeleted: 0,
        isActive: true,
      },
      select: {
        code: true,
        name: true,
        level: true,
        parent: {
          select: {
            code: true,
          },
        },
      },
      orderBy: {
        sortOrder: 'asc',
      },
    });

    return categories.map((category) => ({
      code: category.code,
      name: category.name,
      level: category.level,
      parentCode: category.parent?.code || null,
    }));
  }
}
