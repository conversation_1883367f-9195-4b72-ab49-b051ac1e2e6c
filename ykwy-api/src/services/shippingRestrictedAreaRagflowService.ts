import type { ShippingRestrictedAreaDto } from '../types/dto/shippingRestrictedArea.ts';
import { getFormattedDateTimeFileName } from '../utils/dateTimeTool.ts';
import { RagflowUploader } from '../utils/ragflowUploader.ts';
import { ShippingRestrictedAreaService } from './shippingRestrictedAreaService.ts';

const shippingRestrictedAreaService = new ShippingRestrictedAreaService();
const ragflowUploader = new RagflowUploader();

/**
 * 文本清理函数
 */
function sanitizeSimpleText(text: string | null | undefined): string {
  if (!text) return '';
  return text.trim().replace(/\n/g, ' ').replace(/\r/g, '');
}

export class ShippingRestrictedAreaRagflowService {
  async syncToRagflow() {
    // 获取所有发货受限地址数据（循环分页查询）
    const allAreas = await shippingRestrictedAreaService.findAllFromShippingRestrictedArea();

    // 构建TXT内容，每条数据格式化为指定格式
    const lines = allAreas.map((area: ShippingRestrictedAreaDto) => {
      const province = sanitizeSimpleText(area.province);
      const city = sanitizeSimpleText(area.city);
      const district = sanitizeSimpleText(area.district);
      const status = area.isActive ? '受限' : '不受限';

      return [
        `省份/直辖市：${province}`,
        `城市：${city}`,
        `县/区：${district}`,
        `发货受限程度：${status}`,
        '！@#￥', // 使用指定分隔符作为数据分段
      ].join('\n');
    });

    const fileContent = lines.join('\n');
    const fileName = getFormattedDateTimeFileName('发货受限地址');
    const delimiter = '！@#￥';

    // 完整调用Ragflow工具链：删除旧文档 -> 上传 -> 设置分隔符 -> 解析
    await ragflowUploader.clearDocumentsByPrefix('发货受限地址');
    const newDoc = await ragflowUploader.uploadFileFromStream(fileContent, fileName);
    await ragflowUploader.updateDocumentParserConfig(newDoc.id, delimiter);
    await ragflowUploader.parseDocuments([newDoc.id]);

    return newDoc.id;
  }
}
