import fs from 'fs/promises';
import * as cron from 'node-cron';
import path from 'path';

import { ImportExportTaskService } from './importExportTaskService';

/**
 * 定时任务服务类
 * 处理各种定时任务，如清理过期文件等
 */
export class SchedulerService {
  private importExportTaskService: ImportExportTaskService;

  constructor() {
    this.importExportTaskService = new ImportExportTaskService();
  }

  /**
   * 启动所有定时任务
   */
  public startAllTasks() {
    console.log('🕐 定时任务服务启动');

    // 启动清理过期导出文件的任务
    this.startCleanupExpiredExportsTask();
  }

  /**
   * 启动清理过期导出文件的定时任务
   * 每天00:00:00执行
   */
  private startCleanupExpiredExportsTask() {
    // cron表达式: 秒 分 时 日 月 周
    // '0 0 0 * * *' 表示每天的00:00:00
    cron.schedule(
      '0 0 0 * * *',
      async () => {
        console.log('📁 开始清理过期导出文件...');
        try {
          await this.cleanupExpiredExportFiles();
          console.log('✅ 过期导出文件清理完成');
        } catch (error) {
          console.error('❌ 清理过期导出文件失败:', error);
        }
      },
      {
        timezone: 'Asia/Shanghai', // 设置时区为中国时间
      },
    );

    console.log('📅 已启动清理过期导出文件定时任务 (每天00:00:00执行)');
  }

  /**
   * 清理过期的导出文件
   * 删除30天前的导出任务对应的文件
   */
  private async cleanupExpiredExportFiles() {
    try {
      // 计算30天前的日期
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      console.log(`🔍 查询${thirtyDaysAgo.toISOString()}之前的导出任务...`);

      // 查询30天前的导出任务
      const expiredTasks = await this.importExportTaskService.findExpiredExportTasks(thirtyDaysAgo);

      if (expiredTasks.length === 0) {
        console.log('📋 没有找到需要清理的过期导出文件');
        return;
      }

      console.log(`📋 找到 ${expiredTasks.length} 个过期导出任务需要清理`);

      let deletedCount = 0;
      let errorCount = 0;
      const errors: string[] = [];

      // 遍历过期任务，删除对应的文件
      for (const task of expiredTasks) {
        try {
          if (!task.filePath) {
            console.log(`⚠️  任务 ${task.id} 没有文件路径，跳过`);
            continue;
          }

          // 验证文件路径安全性（确保在temp_files目录内）
          const tempFilesDir = path.join(process.cwd(), 'temp_files');
          const absoluteFilePath = path.resolve(task.filePath);
          const absoluteTempDir = path.resolve(tempFilesDir);

          if (!absoluteFilePath.startsWith(absoluteTempDir)) {
            console.error(`⚠️  任务 ${task.id} 的文件路径不安全，跳过: ${task.filePath}`);
            continue;
          }

          // 检查文件是否存在
          try {
            await fs.access(absoluteFilePath);
          } catch {
            console.log(`📄 任务 ${task.id} 的文件已不存在: ${task.filePath}`);
            // 更新任务状态，标记文件已删除
            await this.updateTaskFileStatus(task.id, 'file_deleted');
            continue;
          }

          // 删除文件
          await fs.unlink(absoluteFilePath);
          console.log(`🗑️  已删除文件: ${task.filePath}`);

          // 更新任务状态，标记文件已删除
          await this.updateTaskFileStatus(task.id, 'file_deleted');

          deletedCount++;
        } catch (error) {
          const errorMsg = `删除任务 ${task.id} 的文件失败: ${error instanceof Error ? error.message : '未知错误'}`;
          console.error(`❌ ${errorMsg}`);
          errors.push(errorMsg);
          errorCount++;
        }
      }

      // 输出清理结果
      console.log(`📊 清理完成统计:`);
      console.log(`   ✅ 成功删除: ${deletedCount} 个文件`);
      console.log(`   ❌ 删除失败: ${errorCount} 个文件`);

      if (errors.length > 0) {
        console.log(`🚨 错误详情:`);
        errors.forEach((error) => console.log(`   - ${error}`));
      }
    } catch (error) {
      console.error('❌ 清理过期导出文件时发生错误:', error);
      throw error;
    }
  }

  /**
   * 更新任务的文件状态
   */
  private async updateTaskFileStatus(taskId: string, status: string) {
    try {
      await this.importExportTaskService.updateStatus(taskId, {
        status: 'success', // 保持原状态，只更新结果信息
        result: `${status === 'file_deleted' ? '文件已自动清理' : status}`,
      });
    } catch (error) {
      console.error(`更新任务 ${taskId} 状态失败:`, error);
    }
  }

  /**
   * 手动执行清理过期文件（用于测试或手动触发）
   */
  public async manualCleanupExpiredFiles() {
    console.log('🔧 手动执行清理过期导出文件...');
    await this.cleanupExpiredExportFiles();
  }

  /**
   * 停止所有定时任务
   */
  public stopAllTasks() {
    cron.getTasks().forEach((task) => {
      task.stop();
    });
    console.log('🛑 所有定时任务已停止');
  }
}
