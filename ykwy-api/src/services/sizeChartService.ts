import { Prisma } from '@prisma/client';

import prisma from '../client/prisma.ts';
import type { SizeChartDetailDto, SizeChartDto, SizeChartEntryDto, SizeChartListDto, SizeChartTypeDetailDto, SizeChartTypeDto, SizeChartTypeListDto } from '../types/dto/sizeChart';
import type { SizeChartEntryInput, SizeChartInput, SizeChartQuery, SizeChartTypeInput, SizeChartTypeQuery } from '../types/request/sizeChart';

export class SizeChartService {
  // ============ 尺码表类型相关操作 ============

  /**
   * 创建或更新尺码表类型
   */
  async upsertSizeChartType(data: SizeChartTypeInput): Promise<SizeChartTypeDto> {
    const { id, name, xAxis, yAxis, desc } = data;

    return prisma.sizeChartType.upsert({
      where: { id: id ?? '00000000-0000-0000-0000-000000000000' },
      update: {
        name,
        xAxis,
        yAxis,
        desc,
      },
      create: {
        name,
        xAxis,
        yAxis,
        desc,
      },
    });
  }

  /**
   * 获取单个尺码表类型详情
   */
  async findSizeChartTypeById(id: string): Promise<SizeChartTypeDetailDto | null> {
    const sizeChartType = await prisma.sizeChartType.findUnique({
      where: {
        id,
        isDeleted: 0,
      },
      include: {
        sizeCharts: {
          where: { isDeleted: 0 },
          orderBy: { sortOrder: 'asc' },
        },
      },
    });

    if (!sizeChartType) return null;

    return {
      ...sizeChartType,
      subTableDefs: [], // 简化：不再查询附表定义
    };
  }

  /**
   * 获取多个尺码表类型（分页）
   */
  async findManySizeChartTypes(params: SizeChartTypeQuery): Promise<SizeChartTypeListDto> {
    const { name, skip = 0, take = 10, includeDeleted = false } = params;

    const where: Prisma.SizeChartTypeWhereInput = {};

    if (!includeDeleted) {
      where.isDeleted = 0;
    }

    if (name) {
      where.name = { contains: name, mode: 'insensitive' };
    }

    const [sizeChartTypes, total] = await prisma.$transaction([
      prisma.sizeChartType.findMany({
        where,
        skip,
        take,
        orderBy: { createdAt: 'desc' },
        include: {
          _count: {
            select: { sizeCharts: true },
          },
        },
      }),
      prisma.sizeChartType.count({ where }),
    ]);

    const items = sizeChartTypes.map((type) => ({
      ...type,
      sizeChartsCount: type._count.sizeCharts,
    }));

    return { items, total };
  }

  /**
   * 删除尺码表类型
   */
  async deleteSizeChartType(id: string): Promise<SizeChartTypeDto> {
    return prisma.sizeChartType.update({
      where: { id },
      data: {
        isDeleted: 1,
      },
    });
  }

  // ============ 尺码表相关操作 ============

  /**
   * 创建或更新尺码表
   */
  async upsertSizeChart(data: SizeChartInput): Promise<SizeChartDto> {
    const { id, name, typeId, isComposite, thresholdRecommendation, parentId, groupName, sortOrder } = data;

    return prisma.sizeChart.upsert({
      where: { id: id ?? '00000000-0000-0000-0000-000000000000' },
      update: {
        name,
        isComposite,
        thresholdRecommendation,
        parentId,
        groupName,
        sortOrder,
      },
      create: {
        name,
        typeId,
        isComposite,
        thresholdRecommendation,
        parentId,
        groupName,
        sortOrder,
      },
    });
  }

  /**
   * 获取单个尺码表详情
   */
  async findSizeChartById(id: string): Promise<SizeChartDetailDto | null> {
    const sizeChart = await prisma.sizeChart.findUnique({
      where: {
        id,
        isDeleted: 0,
      },
      include: {
        type: true,
        parent: true,
        children: {
          where: { isDeleted: 0 },
          orderBy: { sortOrder: 'asc' },
        },
        entries: {
          orderBy: [{ xValue: 'asc' }, { yValue: 'asc' }],
        },
      },
    });

    if (!sizeChart) return null;

    return {
      ...sizeChart,
      parent: sizeChart.parent || undefined,
      products: [], // 简化：不再查询产品关联
      subEntries: [], // 简化：不再查询附表条目
    };
  }

  /**
   * 获取多个尺码表（分页）
   */
  async findManySizeCharts(params: SizeChartQuery): Promise<SizeChartListDto> {
    const { name, typeId, isComposite, parentId, skip = 0, take = 10, includeDeleted = false } = params;

    const where: Prisma.SizeChartWhereInput = {};

    if (!includeDeleted) {
      where.isDeleted = 0;
    }

    if (name) {
      where.name = { contains: name, mode: 'insensitive' };
    }

    if (typeId) {
      where.typeId = typeId;
    }

    if (typeof isComposite === 'boolean') {
      where.isComposite = isComposite;
    }

    if (parentId) {
      where.parentId = parentId;
    }

    const [sizeCharts, total] = await prisma.$transaction([
      prisma.sizeChart.findMany({
        where,
        skip,
        take,
        orderBy: [{ sortOrder: 'asc' }, { createdAt: 'desc' }],
        include: {
          type: true,
          parent: true,
          _count: {
            select: {
              entries: true,
            },
          },
        },
      }),
      prisma.sizeChart.count({ where }),
    ]);

    const items = sizeCharts.map((chart) => ({
      ...chart,
      parent: chart.parent || undefined,
      entriesCount: chart._count.entries,
      productCount: 0, // 简化：不再统计产品数量
    }));

    return { items, total };
  }

  /**
   * 删除尺码表
   */
  async deleteSizeChart(id: string): Promise<SizeChartDto> {
    return prisma.sizeChart.update({
      where: { id },
      data: {
        isDeleted: 1,
      },
    });
  }

  // ============ 尺码表条目相关操作 ============

  /**
   * 创建或更新尺码表条目
   */
  async upsertSizeChartEntry(data: SizeChartEntryInput): Promise<SizeChartEntryDto> {
    const { id, chartId, xValue, yValue, size } = data;

    return prisma.sizeChartEntry.upsert({
      where: { id: id ?? '00000000-0000-0000-0000-000000000000' },
      update: {
        xValue,
        yValue,
        size,
      },
      create: {
        chartId,
        xValue,
        yValue,
        size,
      },
    });
  }

  /**
   * 获取尺码表的所有条目
   */
  async findSizeChartEntries(chartId: string): Promise<SizeChartEntryDto[]> {
    return prisma.sizeChartEntry.findMany({
      where: { chartId },
      orderBy: [{ xValue: 'asc' }, { yValue: 'asc' }],
    });
  }

  /**
   * 批量创建条目
   */
  async batchCreateEntries(chartId: string, entries: SizeChartEntryInput[]): Promise<void> {
    await prisma.sizeChartEntry.createMany({
      data: entries.map((entry) => ({
        chartId,
        xValue: entry.xValue,
        yValue: entry.yValue,
        size: entry.size,
      })),
    });
  }

  /**
   * 删除尺码表条目
   */
  async deleteSizeChartEntry(id: string): Promise<SizeChartEntryDto> {
    return prisma.sizeChartEntry.delete({
      where: { id },
    });
  }

  /**
   * 清空尺码表的所有条目
   */
  async clearSizeChartEntries(chartId: string): Promise<void> {
    await prisma.sizeChartEntry.deleteMany({
      where: { chartId },
    });
  }
}
