import type { Prisma } from '@prisma/client';

import prisma from '../client/prisma';
import { ErrorCode } from '../constants/errorCodes';
import { AppError } from '../errors/custom.error';
import type {
  BulkDeleteQuestionAndAnswerKnowledgeBaseResultDto,
  CreateQuestionAndAnswerKnowledgeBaseDto,
  QuestionAndAnswerKnowledgeBaseDetailDto,
  QuestionAndAnswerKnowledgeBaseDto,
  QuestionAndAnswerKnowledgeBaseListDto,
} from '../types/dto/questionAndAnswerKnowledgeBase';
import type { QuestionAndAnswerKnowledgeBaseQuery, QuestionAndAnswerKnowledgeBaseSearchOptions } from '../types/request/questionAndAnswerKnowledgeBase';
import { QAIncludes } from '../types/request/questionAndAnswerKnowledgeBase';
export class QuestionAndAnswerKnowledgeBaseService {
  /**
   * 创建或更新问答知识库（支持基于ID更新和复合键upsert）
   */
  async upsert(input: CreateQuestionAndAnswerKnowledgeBaseDto & { id?: string }): Promise<QuestionAndAnswerKnowledgeBaseDetailDto> {
    try {
      const { answers, id, ...baseData } = input;

      // 如果提供了ID，则基于ID进行更新
      if (id) {
        return await prisma.questionAndAnswerKnowledgeBase.update({
          where: { id },
          data: {
            ...baseData,
            answers: {
              deleteMany: {},
              create: answers,
            },
          },
          include: QAIncludes.detail,
        });
      }

      // 否则使用原有的复合键upsert逻辑
      return await prisma.questionAndAnswerKnowledgeBase.upsert({
        where: {
          questionType_orderStatus_categoryCode: {
            questionType: input.questionType,
            orderStatus: input.orderStatus,
            categoryCode: input.categoryCode,
          },
        },
        update: {
          ...baseData,
          answers: {
            deleteMany: {},
            create: answers,
          },
        },
        create: {
          ...baseData,
          commonQuestionSamples: input.commonQuestionSamples || [],
          isCustom: input.isCustom || false,
          matchMode: input.matchMode || 'full',
          answers: {
            create: answers,
          },
        },
        include: QAIncludes.detail,
      });
    } catch (error) {
      throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, {
        message: '创建或更新问答知识库失败',
        originalError: error,
      });
    }
  }

  /**
   * 根据ID获取问答知识库详情
   */
  async findById(id: string): Promise<QuestionAndAnswerKnowledgeBaseDetailDto | null> {
    try {
      return await prisma.questionAndAnswerKnowledgeBase.findFirst({
        where: {
          id,
          isDeleted: 0,
        },
        include: QAIncludes.detail,
      });
    } catch (error) {
      throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, {
        message: '获取问答知识库详情失败',
        originalError: error,
      });
    }
  }

  /**
   * 获取问答知识库列表
   */
  async findMany(params: QuestionAndAnswerKnowledgeBaseQuery): Promise<QuestionAndAnswerKnowledgeBaseListDto> {
    try {
      const {
        questionType,
        categoryCode,
        orderStatus,
        matchMode,
        isCustom,
        validityType,
        shopId,
        productId,
        timeValidityId,
        skip = 0,
        take = 10,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        includeDeleted = false,
      } = params;

      const where: Prisma.QuestionAndAnswerKnowledgeBaseWhereInput = {};

      if (!includeDeleted) where.isDeleted = 0;
      if (questionType) where.questionType = { contains: questionType, mode: 'insensitive' };
      if (categoryCode) where.categoryCode = categoryCode;
      if (orderStatus) where.orderStatus = orderStatus;
      if (matchMode) where.matchMode = matchMode;
      if (isCustom !== undefined) where.isCustom = isCustom;
      if (shopId) where.shopId = shopId;

      // 通过关联的 TimeValidity 模型过滤时效类型
      if (validityType) {
        where.timeValidity = {
          validityType,
        };
      }
      if (productId) where.productId = productId;
      if (timeValidityId) where.timeValidityId = timeValidityId;

      const [data, total] = await Promise.all([
        prisma.questionAndAnswerKnowledgeBase.findMany({
          where,
          skip,
          take,
          orderBy: { [sortBy]: sortOrder },
          include: QAIncludes.list,
        }),
        prisma.questionAndAnswerKnowledgeBase.count({ where }),
      ]);

      const pageSize = take;
      const page = Math.floor(skip / pageSize) + 1;

      return {
        data,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize),
      };
    } catch (error) {
      throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, {
        message: '获取问答知识库列表失败',
        originalError: error,
      });
    }
  }

  /**
   * 根据分类编码获取问答知识库
   */
  async findByCategoryCode(categoryCode: string): Promise<QuestionAndAnswerKnowledgeBaseDto[]> {
    try {
      return await prisma.questionAndAnswerKnowledgeBase.findMany({
        where: {
          categoryCode,
          isDeleted: 0,
        },
        include: QAIncludes.basic,
        orderBy: {
          createdAt: 'desc',
        },
      });
    } catch (error) {
      throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, {
        message: '根据分类编码获取问答知识库失败',
        originalError: error,
      });
    }
  }

  /**
   * 软删除问答知识库
   */
  async softDelete(id: string): Promise<QuestionAndAnswerKnowledgeBaseDto> {
    try {
      const existing = await prisma.questionAndAnswerKnowledgeBase.findUnique({
        where: { id },
      });

      if (!existing || existing.isDeleted === 1) {
        throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, {
          message: '问答知识库记录不存在',
        });
      }

      return await prisma.questionAndAnswerKnowledgeBase.update({
        where: { id },
        data: { isDeleted: 1 },
        include: QAIncludes.basic,
      });
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, {
        message: '删除问答知识库失败',
        originalError: error,
      });
    }
  }

  /**
   * 批量软删除问答知识库
   */
  async bulkDelete(ids: string[]): Promise<BulkDeleteQuestionAndAnswerKnowledgeBaseResultDto> {
    try {
      const deletedIds: string[] = [];
      const failedIds: string[] = [];
      const errors: Array<{ id: string; error: string }> = [];

      for (const id of ids) {
        try {
          await this.softDelete(id);
          deletedIds.push(id);
        } catch (error) {
          failedIds.push(id);
          errors.push({
            id,
            error: error instanceof Error ? error.message : '未知错误',
          });
        }
      }

      return {
        deletedCount: deletedIds.length,
        deletedIds,
        failedIds,
        errors: errors.length > 0 ? errors : undefined,
      };
    } catch (error) {
      throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, {
        message: '批量删除问答知识库失败',
        originalError: error,
      });
    }
  }

  /**
   * 根据查询字符串搜索问答知识库
   */
  async searchByQuery(query: string, options?: QuestionAndAnswerKnowledgeBaseSearchOptions): Promise<string[]> {
    try {
      const where: Prisma.QuestionAndAnswerKnowledgeBaseWhereInput = {
        isDeleted: 0,
      };

      // 如果只搜索当前有效的问答，添加时效性检查
      if (options?.onlyValid !== false) {
        where.timeValidity = {
          validityType: 'permanent',
        };
      }

      // 按时效类型过滤
      if (options?.validityType) {
        where.timeValidity = where.timeValidity || {};
        where.timeValidity.validityType = options.validityType;
      }

      const matchMode = options?.matchMode || 'keyword';

      if (matchMode === 'full') {
        where.OR = [
          { questionType: query },
          {
            commonQuestionSamples: {
              has: query,
            },
          },
        ];
      } else if (matchMode === 'keyword') {
        where.OR = [
          {
            questionType: {
              contains: query,
              mode: 'insensitive',
            },
          },
          {
            commonQuestionSamples: {
              hasSome: [query],
            },
          },
        ];
      }
      const results = await prisma.questionAndAnswerKnowledgeBase.findMany({
        where,
        include: QAIncludes.search,
      });
      // 收集所有匹配的回答
      const allAnswers: string[] = [];
      for (const item of results) {
        allAnswers.push(...item.answers.map((answer) => answer.content));
      }
      // 去重并返回
      return [...new Set(allAnswers)];
    } catch (error) {
      throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, {
        message: '搜索问答知识库失败',
        originalError: error,
      });
    }
  }
}
