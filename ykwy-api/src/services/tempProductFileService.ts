import Papa from 'papaparse';
import * as XLSX from 'xlsx';

import prisma from '../client/prisma.ts';
import type { TempProductDto } from '../types/dto/tempProduct';

// 批量导入结果接口
interface BatchImportResult {
  successCount: number;
  failCount: number;
  newCount: number;
  updatedCount: number;
  errors?: string[];
}

// Excel行数据接口
interface ExcelRowData {
  商品名称?: string;
  商品链接或id?: string;
  商品ID?: string;
  商品状态?: string;
  货号款号?: string;
  商品图片链接?: string;
  描述?: string;
  店铺ID?: string;
}

export class TempProductFileService {
  /**
   * 批量导入临时商品数据
   */
  async batchImport(buffer: Uint8Array, filename: string): Promise<BatchImportResult> {
    try {
      let jsonData: ExcelRowData[] = [];

      // 根据文件类型选择解析方式
      if (filename.toLowerCase().endsWith('.csv')) {
        jsonData = await this.parseCsvFile(buffer);
      } else {
        jsonData = await this.parseExcelFile(buffer);
      }

      return this.processImportData(jsonData);
    } catch (error) {
      throw new Error(`文件解析失败：${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 解析CSV文件
   */
  private async parseCsvFile(buffer: Uint8Array): Promise<ExcelRowData[]> {
    const text = new TextDecoder('utf-8').decode(buffer);
    const { data, errors } = Papa.parse(text, {
      header: true,
      skipEmptyLines: true,
    });

    if (errors.length > 0) {
      const errorMessages = errors.map((e) => `行${e.row !== undefined ? e.row + 1 : '未知行'}：${e.message || '未知错误'}`);
      throw new Error(`CSV解析错误：${errorMessages.join(', ')}`);
    }

    this.validateCsvHeaders(data as Record<string, string>[]);
    return this.transformToExcelRowData(data as Record<string, string>[]);
  }

  /**
   * 解析Excel文件
   */
  private async parseExcelFile(buffer: Uint8Array): Promise<ExcelRowData[]> {
    const workbook = XLSX.read(buffer, { type: 'array' });
    const firstSheetName = workbook.SheetNames[0];
    const worksheet = firstSheetName ? workbook.Sheets[firstSheetName] : undefined;
    if (!worksheet) {
      throw new Error('Excel文件中未找到工作表');
    }
    const jsonData = XLSX.utils.sheet_to_json(worksheet);
    if (jsonData.length === 0) {
      throw new Error('Excel文件没有数据');
    }
    return jsonData as ExcelRowData[];
  }

  /**
   * 验证CSV文件标题
   */
  private validateCsvHeaders(data: Record<string, string>[]): void {
    if (data.length === 0) {
      throw new Error('CSV文件没有数据');
    }

    const expectedHeaders = ['商品名称', '商品链接或id', '商品ID', '商品状态', '货号/款号', '商品图片链接'];
    const actualHeaders = Object.keys(data[0] || {});
    const hasValidHeaders = expectedHeaders.every((header) => actualHeaders.includes(header) || actualHeaders.includes(header.replace('/', '')));

    if (!hasValidHeaders) {
      throw new Error(`CSV文件标题不正确，应包含：${expectedHeaders.join('、')}，实际包含：${actualHeaders.join('、')}`);
    }
  }

  /**
   * 转换CSV数据为标准格式
   */
  private transformToExcelRowData(data: Record<string, string>[]): ExcelRowData[] {
    if (data.length === 0) return [];

    return data.map((row) => ({
      商品名称: row['商品名称'],
      商品链接或id: row['商品链接或id'],
      商品ID: row['商品ID'],
      商品状态: row['商品状态'],
      货号款号: row['货号/款号'] || row['货号款号'],
      商品图片链接: row['商品图片链接'],
    }));
  }

  /**
   * 处理导入数据并保存到数据库
   */
  private async processImportData(jsonData: ExcelRowData[]): Promise<BatchImportResult> {
    if (jsonData.length === 0) {
      return {
        successCount: 0,
        failCount: 0,
        newCount: 0,
        updatedCount: 0,
        errors: ['文件没有数据'],
      };
    }

    const { validData, errors, failCount } = this.validateAndCleanData(jsonData);
    const { successCount, newCount, updatedCount } = await this.saveImportData(validData);

    return {
      successCount,
      failCount,
      newCount,
      updatedCount,
      errors: errors.length > 0 ? errors : undefined,
    };
  }

  /**
   * 验证和清理导入数据
   */
  private validateAndCleanData(jsonData: ExcelRowData[]): {
    validData: Omit<TempProductDto, 'id'>[];
    errors: string[];
    failCount: number;
  } {
    const validData: Omit<TempProductDto, 'id'>[] = [];
    const errors: string[] = [];
    let failCount = 0;

    for (let i = 0; i < jsonData.length; i++) {
      const row = jsonData[i];
      if (!row) continue;

      const rowIndex = i + 2; // 行号从2开始（第1行是标题）
      try {
        this.validateRequiredFields(row, rowIndex, errors);
        const cleanProductId = this.cleanProductId(row.商品ID, rowIndex, errors);
        validData.push(this.createValidItem(row, cleanProductId));
      } catch {
        failCount++;
      }
    }

    return { validData, errors, failCount };
  }

  /**
   * 验证必填字段
   */
  private validateRequiredFields(row: ExcelRowData, rowIndex: number, errors: string[]): void {
    if (!row.商品名称 || !row.商品链接或id || !row.商品ID || !row.商品状态) {
      errors.push(`第${rowIndex}行：缺少必填字段（商品名称、商品链接或id、商品ID、商品状态）`);
      throw new Error('缺少必填字段');
    }
  }

  /**
   * 清理商品ID
   */
  private cleanProductId(productId: string | undefined, rowIndex: number, errors: string[]): string {
    let cleanProductId = String(productId || '').trim();
    cleanProductId = cleanProductId.replace(/^['"`]+|['"`]+$/g, '');

    if (!cleanProductId) {
      errors.push(`第${rowIndex}行：商品ID清理后为空`);
      throw new Error('商品ID无效');
    }

    return cleanProductId;
  }

  /**
   * 创建有效数据项
   */
  private createValidItem(row: ExcelRowData, productId: string): Omit<TempProductDto, 'id'> {
    return {
      name: String(row.商品名称).trim(),
      linkOrId: String(row.商品链接或id).trim(),
      productId,
      status: String(row.商品状态).trim(),
      styleNumber: row.货号款号 ? String(row.货号款号).trim() : null,
      imageUrl: row.商品图片链接 ? String(row.商品图片链接).trim() : null,
      description: row.描述 ? JSON.parse(String(row.描述).trim()) : null,
      shopId: row.店铺ID ? String(row.店铺ID).trim() : null,
      createdAt: new Date(),
      updatedAt: new Date(),
      isDeleted: 0,
    };
  }

  /**
   * 保存导入数据到数据库
   */
  private async saveImportData(validData: Omit<TempProductDto, 'id'>[]): Promise<{
    successCount: number;
    newCount: number;
    updatedCount: number;
    errors: string[];
  }> {
    let successCount = 0;
    let newCount = 0;
    let updatedCount = 0;
    const errors: string[] = [];

    for (const item of validData) {
      try {
        const existingProduct = await prisma.tempProduct.findFirst({
          where: { productId: item.productId },
        });

        if (existingProduct && existingProduct.isDeleted === 1) {
          errors.push(`商品ID ${item.productId} 已存在但已被删除，请先恢复或使用不同的商品ID`);
          continue;
        }

        if (existingProduct) {
          await prisma.tempProduct.update({
            where: { id: existingProduct.id },
            data: {
              name: item.name,
              linkOrId: item.linkOrId,
              productId: item.productId,
              status: item.status,
              styleNumber: item.styleNumber,
              imageUrl: item.imageUrl,
              description: item.description || undefined,
              shopId: item.shopId,
              updatedAt: new Date(),
            },
          });
          updatedCount++;
        } else {
          await prisma.tempProduct.create({
            data: {
              name: item.name,
              linkOrId: item.linkOrId,
              productId: item.productId,
              status: item.status,
              styleNumber: item.styleNumber,
              imageUrl: item.imageUrl,
              description: item.description || undefined,
              shopId: item.shopId,
              createdAt: item.createdAt,
              isDeleted: item.isDeleted,
            },
          });
          newCount++;
        }
        successCount++;
      } catch (itemError) {
        errors.push(this.handleSaveError(item, itemError));
      }
    }

    return { successCount, newCount, updatedCount, errors };
  }

  /**
   * 处理保存错误
   */
  private handleSaveError(item: Omit<TempProductDto, 'id'>, error: unknown): string {
    if (error instanceof Error && error.message.includes('Unique constraint')) {
      return `商品ID重复：${item.productId} (${item.name})`;
    }
    return `处理失败：${item.name} - ${error instanceof Error ? error.message : '未知错误'}`;
  }
}
