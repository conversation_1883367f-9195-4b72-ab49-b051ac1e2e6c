import { Prisma } from '@prisma/client';

import prisma from '../client/prisma.ts';
import { ErrorCode } from '../constants/errorCodes.ts';
import { AppError } from '../errors/custom.error.ts';
import type { BulkDeleteResultDto, TempProductDto, TempProductListDto } from '../types/dto/tempProduct';
import type { TempProductInput, TempProductQuery } from '../types/validators/tempProductValidator';
import { TempProductFileService } from './tempProductFileService.ts';

// 批量导入结果接口
interface BatchImportResult {
  successCount: number;
  failCount: number;
  newCount: number; // 新增数据数量
  updatedCount: number; // 更新数据数量
  errors?: string[];
}

/**
 * 临时商品服务层，负责与数据库的交互
 */
export class TempProductService {
  /**
   * 创建或更新临时商品
   * @param data 临时商品输入参数
   */
  async upsert(data: TempProductInput): Promise<TempProductDto> {
    const { id, name, linkOrId, productId, status, styleNumber, imageUrl, description, shopId } = data;

    // 构建更新/创建数据对象
    const tempProductData: Prisma.TempProductCreateInput = {
      name,
      linkOrId,
      productId,
      status,
      styleNumber,
      imageUrl,
      description,
      shopId,
    };

    try {
      // upsert: 有id则更新，无id则创建
      return await prisma.tempProduct.upsert({
        where: { id: id ?? '00000000-0000-0000-0000-000000000000' },
        update: tempProductData,
        create: tempProductData,
      });
    } catch (error) {
      // 捕获Prisma唯一约束错误
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2002') {
        const target = error.meta?.['target'];
        if (Array.isArray(target) && target.includes('product_id')) {
          throw new AppError(ErrorCode.G002_VALIDATION_ERROR, {
            message: `商品ID已存在: ${productId}，请使用其他商品ID`,
          });
        }
      }
      throw error;
    }
  }

  /**
   * 获取单个临时商品详情
   * @param id 临时商品ID
   */
  async findById(id: string): Promise<TempProductDto | null> {
    return prisma.tempProduct.findUnique({
      where: { id, isDeleted: 0 },
    });
  }

  /**
   * 根据productId获取单个临时商品详情
   * @param productId 商品ID
   */
  async findByProductId(productId: string): Promise<TempProductDto | null> {
    return prisma.tempProduct.findFirst({
      where: { productId, isDeleted: 0 },
    });
  }

  /**
   * 根据查询字符串搜索临时商品
   * 同时对productId、name、styleNumber、linkOrId进行精确查询
   * 只返回未删除且已上架的商品
   * @param query 查询字符串（可选，为空时查询shopId下所有商品）
   * @param shopId 店铺ID（必填）
   */
  async searchByQuery(query: string | undefined, shopId: string): Promise<TempProductDto[]> {
    try {
      const whereCondition: Prisma.TempProductWhereInput = {
        // 必须是未删除的
        isDeleted: 0,
        // 必须是已上架的
        status: '已上架',
        // 必须匹配shopId
        shopId: shopId,
      };

      // 如果提供了query且不为空，添加查询条件
      if (query && query.trim() !== '') {
        whereCondition.OR = [
          {
            productId: query.trim(),
          },
          {
            name: query.trim(),
          },
          {
            styleNumber: query.trim(),
          },
          {
            linkOrId: query.trim(),
          },
        ];
      }

      return await prisma.tempProduct.findMany({
        where: whereCondition,
        orderBy: {
          createdAt: 'desc',
        },
      });
    } catch (error) {
      console.error('搜索临时商品时出错:', error);
      return [];
    }
  }

  /**
   * 获取多个临时商品（分页）
   * @param params 查询参数
   */
  async findMany(params: TempProductQuery): Promise<TempProductListDto> {
    const { name, status, productId, skip = 0, take = 10, includeDeleted = false, onlyWithoutShopId = false } = params;
    const where: Prisma.TempProductWhereInput = {};

    if (!includeDeleted) where.isDeleted = 0;
    if (name) where.name = { contains: name, mode: 'insensitive' };
    if (status) where.status = { contains: status, mode: 'insensitive' };
    if (productId) where.productId = { contains: productId, mode: 'insensitive' };
    // 如果设置了只查询没有shopId的数据，添加shopId为null的过滤条件
    if (onlyWithoutShopId) where.shopId = null;

    // 分别查询已上架和其他状态的商品，然后合并结果
    let tempProducts: TempProductDto[] = [];
    let total = 0;

    // 如果有状态过滤且不是"已上架"，直接使用普通查询
    if (status && status !== '已上架') {
      const [products, count] = await prisma.$transaction([
        prisma.tempProduct.findMany({
          where,
          skip,
          take,
          orderBy: { createdAt: 'desc' },
        }),
        prisma.tempProduct.count({ where }),
      ]);
      tempProducts = products;
      total = count;
    } else {
      // 没有状态过滤或者过滤"已上架"时，实现自定义排序

      // 首先查询已上架的商品
      const publishedWhere = {
        ...where,
        status: { contains: '已上架', mode: 'insensitive' as const },
      };
      const [publishedProducts, publishedCount] = await prisma.$transaction([
        prisma.tempProduct.findMany({
          where: publishedWhere,
          orderBy: { createdAt: 'desc' },
        }),
        prisma.tempProduct.count({ where: publishedWhere }),
      ]);

      // 如果只过滤已上架商品，直接返回结果
      if (status && status.includes('已上架')) {
        tempProducts = publishedProducts.slice(skip, skip + take);
        total = publishedCount;
      } else {
        // 查询其他状态的商品（非已上架）
        const otherWhere = {
          ...where,
          NOT: { status: { contains: '已上架', mode: 'insensitive' as const } },
        };
        const [otherProducts, otherCount] = await prisma.$transaction([
          prisma.tempProduct.findMany({
            where: otherWhere,
            orderBy: { createdAt: 'desc' },
          }),
          prisma.tempProduct.count({ where: otherWhere }),
        ]);

        // 合并结果：已上架在前，其他在后
        const allProducts = [...publishedProducts, ...otherProducts];
        total = publishedCount + otherCount;

        // 应用分页
        tempProducts = allProducts.slice(skip, skip + take);
      }
    }

    return { items: tempProducts, total };
  }

  /**
   * 软删除临时商品（逻辑删除）
   * @param id 临时商品ID
   */
  async softDelete(id: string): Promise<TempProductDto> {
    return prisma.tempProduct.update({
      where: { id },
      data: { isDeleted: 1 },
    });
  }

  /**
   * 恢复已删除临时商品
   * @param id 临时商品ID
   */
  async restore(id: string): Promise<TempProductDto> {
    return prisma.tempProduct.update({
      where: { id },
      data: { isDeleted: 0 },
    });
  }

  /**
   * 批量软删除临时商品
   * @param ids 临时商品ID数组
   */
  async bulkDelete(ids: string[]): Promise<BulkDeleteResultDto> {
    const result = await prisma.tempProduct.updateMany({
      where: { id: { in: ids }, isDeleted: 0 },
      data: { isDeleted: 1 },
    });

    return { count: result.count };
  }

  /**
   * 批量创建临时商品
   * @param dataList 临时商品数据数组
   */
  async bulkCreate(dataList: Omit<TempProductInput, 'id'>[]): Promise<BulkDeleteResultDto> {
    const result = await prisma.tempProduct.createMany({
      data: dataList,
      skipDuplicates: true,
    });

    return { count: result.count };
  }

  /**
   * 清空所有临时商品（物理删除）
   */
  async truncate(): Promise<BulkDeleteResultDto> {
    const result = await prisma.tempProduct.deleteMany({});
    return { count: result.count };
  }

  /**
   * 循环分页获取全部临时商品
   * @param baseQuery 查询参数（不包含skip和take）
   * @param pageSize 每页数量，默认100
   */
  async findAllFromTempProduct(
    baseQuery: Omit<TempProductQuery, 'skip' | 'take'> = {
      includeDeleted: false,
    },
    pageSize = 100,
  ): Promise<TempProductDto[]> {
    let skip = 0;
    let allItems: TempProductDto[] = [];

    while (true) {
      const { items } = await this.findMany({
        ...baseQuery,
        skip,
        take: pageSize,
      });

      allItems = allItems.concat(items);
      if (items.length < pageSize) {
        break;
      }
      skip += pageSize;
    }

    return allItems;
  }

  private fileService = new TempProductFileService();

  /**
   * 批量导入临时商品数据
   * 委托给专门的文件服务处理
   */
  async batchImport(buffer: Uint8Array, filename: string): Promise<BatchImportResult> {
    return this.fileService.batchImport(buffer, filename);
  }
}
