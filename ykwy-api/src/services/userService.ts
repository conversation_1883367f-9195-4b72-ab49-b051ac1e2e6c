import type { User } from '@prisma/client';
import Bun from 'bun';

import prisma from '../client/prisma';
import { ErrorCode } from '../constants/errorCodes';
import { AppError } from '../errors/custom.error';

export class UserService {
  async createUser(email: string, password: string): Promise<{ id: string; email: string; createdAt: Date }> {
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });
    if (existingUser) throw new AppError(ErrorCode.AUTH_USER_ALREADY_EXISTS);

    return prisma.user.create({
      data: { email, password },
      select: { id: true, email: true, createdAt: true },
    });
  }

  async getUserByEmail(email: string) {
    return prisma.user.findUnique({ where: { email } });
  }

  async getUserById(id: string) {
    return prisma.user.findUnique({ where: { id } });
  }

  async verifyPassword(plainPassword: string, hashedPassword: string) {
    return Bun.password.verify(plainPassword, hashedPassword);
  }

  async updateUser(id: string, data: Partial<{ email: string }>): Promise<Pick<User, 'id' | 'email' | 'createdAt' | 'updatedAt'>> {
    return prisma.user.update({
      where: { id },
      data,
      select: { id: true, email: true, createdAt: true, updatedAt: true },
    });
  }

  async updatePassword(id: string, password: string): Promise<void> {
    await prisma.user.update({
      where: { id },
      data: { password },
    });
  }
}
