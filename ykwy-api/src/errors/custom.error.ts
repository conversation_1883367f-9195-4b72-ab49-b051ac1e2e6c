import type { ContentfulStatusCode } from 'hono/utils/http-status';
import type { ZodIssue } from 'zod';

import type { ErrorCodeType } from '../constants/errorCodes';

/**
 * 应用程序的自定义错误基类
 */
export class AppError extends Error {
  public readonly statusCode: ContentfulStatusCode;
  public readonly errorCode: string;
  public readonly context?: Record<string, unknown>;

  constructor(errorType: ErrorCodeType, context?: Record<string, unknown>) {
    const message = String(context?.['message'] ?? errorType.message);
    super(message);
    this.statusCode = errorType.status;
    this.errorCode = errorType.code;
    this.context = context; // 用于日志记录的上下文信息

    Object.setPrototypeOf(this, new.target.prototype);
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * 验证错误类，专门处理参数验证失败
 */
export class ValidationError extends AppError {
  constructor(validationIssues: ZodIssue[], context?: Record<string, unknown>) {
    // 构建详细的验证错误消息
    const validationMessages = validationIssues
      .map((issue) => {
        const field = issue.path.join('.');
        const message = issue.message;
        return `${field}: ${message}`;
      })
      .join('; ');

    super(
      { code: 'G002', message: '参数验证失败', status: 400 },
      {
        ...context,
        validationIssues,
        detailedMessage: validationMessages,
      },
    );
  }
}

/**
 * 业务逻辑错误类，支持自定义业务错误码、消息、状态码和上下文
 */
export class BusinessLogicError extends AppError {
  constructor(message: string, errorCode: string = 'BIZ001', statusCode: number = 400, context?: Record<string, unknown>) {
    super(
      {
        code: errorCode,
        message,
        status: statusCode,
      } as ErrorCodeType,
      context,
    );
  }
}

/**
 * 资源未找到错误类
 */
export class NotFoundError extends AppError {}
