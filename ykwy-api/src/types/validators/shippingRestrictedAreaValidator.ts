import { z } from 'zod';

/**
 * 发货受限地址ID参数校验
 * 用于需要传递ID的接口
 */
export const shippingRestrictedAreaParamIdSchema = z.object({
  /** 发货受限地址ID */
  id: z.string().uuid({ message: '无效的ID格式' }),
});

/**
 * 创建/更新发货受限地址参数校验
 */
export const upsertShippingRestrictedAreaSchema = z.object({
  /** ID，更新时必传，创建时可选 */
  id: z.string().uuid().optional(),
  /** 省份/直辖市 (必须字段) */
  province: z.string().min(1, { message: '省份为必填项' }).max(50, { message: '省份长度不能超过50个字符' }),
  /** 城市 (必须字段) */
  city: z.string().min(1, { message: '城市为必填项' }).max(50, { message: '城市长度不能超过50个字符' }),
  /** 县/区 (必须字段) */
  district: z.string().min(1, { message: '县/区为必填项' }).max(50, { message: '县/区长度不能超过50个字符' }),
  /** 是否受限 (可选字段，默认false) */
  isActive: z.boolean().optional().default(false),
});

/**
 * 发货受限地址查询参数校验
 */
export const shippingRestrictedAreaQuerySchema = z.object({
  /** 按省份模糊搜索 */
  province: z.string().optional(),
  /** 按城市模糊搜索 */
  city: z.string().optional(),
  /** 按县/区模糊搜索 */
  district: z.string().optional(),
  /** 全文搜索（同时搜索省份/城市/县区） */
  searchText: z.string().optional(),
  /** 按状态过滤 */
  isActive: z
    .string()
    .optional()
    .transform((val) => {
      if (val === undefined || val === '') return undefined;
      return val === 'true';
    }),
  /** 跳过条数（分页） */
  skip: z
    .string()
    .optional()
    .default('0')
    .transform((val) => parseInt(val, 10))
    .refine((val) => val >= 0, { message: '跳过条数不能为负数' }),
  /** 取条数（分页） */
  take: z
    .string()
    .optional()
    .default('10')
    .transform((val) => parseInt(val, 10))
    .refine((val) => val > 0 && val <= 100, { message: '每页条数必须在1-100之间' }),
});

/**
 * 批量删除参数校验
 */
export const shippingRestrictedAreaBulkDeleteSchema = z.object({
  /** ID列表 */
  ids: z.array(z.string().uuid({ message: '无效的ID格式' })).min(1, { message: '至少选择一个要删除的项目' }),
});

/**
 * 批量创建参数校验 - 数组形式
 */
export const shippingRestrictedAreaBulkCreateSchema = z
  .array(
    z.object({
      /** 省份/直辖市 (必须字段) */
      province: z.string().min(1, { message: '省份为必填项' }).max(50, { message: '省份长度不能超过50个字符' }),
      /** 城市 (必须字段) */
      city: z.string().min(1, { message: '城市为必填项' }).max(50, { message: '城市长度不能超过50个字符' }),
      /** 县/区 (必须字段) */
      district: z.string().min(1, { message: '县/区为必填项' }).max(50, { message: '县/区长度不能超过50个字符' }),
      /** 是否受限 (可选字段，默认false) */
      isActive: z.boolean().optional().default(false),
    }),
  )
  .min(1, { message: '批量创建列表不能为空' });

// 导出推断类型
export type UpsertShippingRestrictedAreaInput = z.infer<typeof upsertShippingRestrictedAreaSchema>;
export type ShippingRestrictedAreaQueryInput = z.infer<typeof shippingRestrictedAreaQuerySchema>;
export type ShippingRestrictedAreaParamIdInput = z.infer<typeof shippingRestrictedAreaParamIdSchema>;
export type ShippingRestrictedAreaBulkDeleteInput = z.infer<typeof shippingRestrictedAreaBulkDeleteSchema>;
export type ShippingRestrictedAreaBulkCreateInput = z.infer<typeof shippingRestrictedAreaBulkCreateSchema>;
