import { z } from 'zod';

/**
 * 时效类型枚举
 */
const ValidityType = z.enum(['fixed', 'daily', 'weekly', 'custom'], {
  errorMap: () => ({ message: '时效类型必须是 fixed、daily、weekly 或 custom' }),
});

/**
 * 时分秒格式验证（HH:MM:SS）
 */
const timeHHMMSSPattern = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/;

/**
 * 周几枚举
 */
const WeekdayEnum = z.enum(['周一', '周二', '周三', '周四', '周五', '周六', '周日'], {
  errorMap: () => ({ message: '周几必须是周一到周日' }),
});

/**
 * 创建时效配置验证器
 */
export const createTimeValiditySchema = z
  .object({
    /** 时效标签名称 */
    label: z.string().min(1, { message: '时效标签名称不能为空' }).max(50, { message: '时效标签名称不能超过50个字符' }),
    /** 时效类型 */
    validityType: ValidityType,
    /** 固定时段起始时间（包含年月日时分秒） */
    startDateTime: z
      .string()
      .datetime()
      .optional()
      .transform((val) => (val ? new Date(val) : undefined)),
    /** 固定时段结束时间（包含年月日时分秒） */
    endDateTime: z
      .string()
      .datetime()
      .optional()
      .transform((val) => (val ? new Date(val) : undefined)),
    /** 每日/每周起始时分秒 */
    startTimeHHMMSS: z.string().regex(timeHHMMSSPattern, { message: '起始时间格式必须为 HH:MM:SS' }).optional(),
    /** 每日/每周结束时分秒 */
    endTimeHHMMSS: z.string().regex(timeHHMMSSPattern, { message: '结束时间格式必须为 HH:MM:SS' }).optional(),
    /** 每周重复时的配置 */
    repeatWeekdays: z.array(WeekdayEnum).optional().default([]),
    /** 自定义开始日期 */
    customStartDate: z
      .string()
      .datetime()
      .optional()
      .transform((val) => (val ? new Date(val) : undefined)),
    /** 自定义结束日期 */
    customEndDate: z
      .string()
      .datetime()
      .optional()
      .transform((val) => (val ? new Date(val) : undefined)),
    /** 每天的起始时间（时分秒） */
    customDailyStartTime: z.string().regex(timeHHMMSSPattern, { message: '每天起始时间格式必须为 HH:MM:SS' }).optional(),
    /** 每天的结束时间（时分秒） */
    customDailyEndTime: z.string().regex(timeHHMMSSPattern, { message: '每天结束时间格式必须为 HH:MM:SS' }).optional(),
  })
  .refine(
    (data) => {
      // 对于fixed类型，必须提供起始和结束时间
      if (data.validityType === 'fixed' && (!data.startDateTime || !data.endDateTime)) {
        return false;
      }

      // 对于daily和weekly类型，必须提供起始和结束时间
      if ((data.validityType === 'daily' || data.validityType === 'weekly') && (!data.startTimeHHMMSS || !data.endTimeHHMMSS)) {
        return false;
      }

      // 对于weekly类型，必须提供重复的周几
      if (data.validityType === 'weekly' && (!data.repeatWeekdays || data.repeatWeekdays.length === 0)) {
        return false;
      }

      // 对于custom类型，必须提供自定义日期和时间
      if (data.validityType === 'custom' && (!data.customStartDate || !data.customEndDate || !data.customDailyStartTime || !data.customDailyEndTime)) {
        return false;
      }

      return true;
    },
    {
      message: '根据时效类型，必须提供相应的时间配置',
    },
  );

/**
 * 更新时效配置验证器
 */
export const updateTimeValiditySchema = z.object({
  /** 时效配置ID */
  id: z.string().uuid({ message: '无效的ID格式' }),
  /** 时效标签名称 */
  label: z.string().min(1, { message: '时效标签名称不能为空' }).max(50, { message: '时效标签名称不能超过50个字符' }).optional(),
  /** 时效类型 */
  validityType: ValidityType.optional(),
  /** 固定时段起始时间（包含年月日时分秒） */
  startDateTime: z
    .string()
    .datetime()
    .optional()
    .transform((val) => (val ? new Date(val) : undefined)),
  /** 固定时段结束时间（包含年月日时分秒） */
  endDateTime: z
    .string()
    .datetime()
    .optional()
    .transform((val) => (val ? new Date(val) : undefined)),
  /** 每日/每周起始时分秒 */
  startTimeHHMMSS: z.string().regex(timeHHMMSSPattern, { message: '起始时间格式必须为 HH:MM:SS' }).optional(),
  /** 每日/每周结束时分秒 */
  endTimeHHMMSS: z.string().regex(timeHHMMSSPattern, { message: '结束时间格式必须为 HH:MM:SS' }).optional(),
  /** 每周重复时的配置 */
  repeatWeekdays: z.array(WeekdayEnum).optional(),
  /** 自定义开始日期 */
  customStartDate: z
    .string()
    .datetime()
    .optional()
    .transform((val) => (val ? new Date(val) : undefined)),
  /** 自定义结束日期 */
  customEndDate: z
    .string()
    .datetime()
    .optional()
    .transform((val) => (val ? new Date(val) : undefined)),
  /** 每天的起始时间（时分秒） */
  customDailyStartTime: z.string().regex(timeHHMMSSPattern, { message: '每天起始时间格式必须为 HH:MM:SS' }).optional(),
  /** 每天的结束时间（时分秒） */
  customDailyEndTime: z.string().regex(timeHHMMSSPattern, { message: '每天结束时间格式必须为 HH:MM:SS' }).optional(),
});

/**
 * 时效配置查询验证器
 */
export const timeValidityQuerySchema = z.object({
  /** 按标签名称模糊搜索 */
  label: z.string().optional(),
  /** 按时效类型精确匹配 */
  validityType: ValidityType.optional(),
  /** 跳过条数（分页） */
  skip: z
    .string()
    .optional()
    .default('0')
    .transform((val) => parseInt(val, 10)),
  /** 获取条数（分页） */
  take: z
    .string()
    .optional()
    .default('10')
    .transform((val) => Math.min(parseInt(val, 10), 100)),
  /** 排序字段 */
  sortBy: z.enum(['createdAt', 'updatedAt', 'label', 'validityType']).optional().default('createdAt'),
  /** 排序方向 */
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
});

/**
 * ID参数验证器
 */
export const timeValidityParamIdSchema = z.object({
  /** 时效配置ID */
  id: z.string().uuid({ message: '无效的ID格式' }),
});

/**
 * 批量删除验证器
 */
export const timeValidityBulkDeleteSchema = z.object({
  /** 要删除的ID数组 */
  ids: z.array(z.string().uuid({ message: '无效的ID格式' })).min(1, { message: '至少需要选择一个项目' }),
});
