import { z } from 'zod';

/**
 * 导入导出任务ID参数校验
 */
export const importExportTaskParamIdSchema = z.object({
  /** 任务ID */
  id: z.string().uuid({ message: '无效的ID格式' }),
});

/**
 * 创建/更新导入导出任务参数校验
 */
export const upsertImportExportTaskSchema = z.object({
  /** 任务ID，更新时必传，创建时可选 */
  id: z.string().uuid().optional(),
  /** 任务名称 (必须字段) */
  taskName: z.string().min(1, { message: '任务名称为必填项' }),
  /** 任务类型 (必须字段): import=导入, export=导出 */
  taskType: z.enum(['import', 'export'], { message: '任务类型必须是import或export' }),
  /** 任务时间 (必须字段) */
  taskTime: z.string().pipe(z.coerce.date()),
  /** 文件名 (必须字段) */
  fileName: z.string().min(1, { message: '文件名为必填项' }),
  /** 任务状态 (可选字段): pending=进行中, success=成功, failed=失败 */
  status: z.enum(['pending', 'success', 'failed']).optional().default('pending'),
  /** 任务结果 (可选字段) */
  result: z.any().optional(),
  /** 文件路径 (可选字段) */
  filePath: z.string().optional(),
});

/**
 * 导入导出任务查询参数校验
 */
export const importExportTaskQuerySchema = z.object({
  /** 按任务名称模糊搜索 */
  taskName: z.string().optional(),
  /** 按任务类型过滤 */
  taskType: z.enum(['import', 'export']).optional(),
  /** 按状态过滤 */
  status: z.enum(['pending', 'success', 'failed']).optional(),
  /** 按文件名模糊搜索 */
  fileName: z.string().optional(),
  /** 开始时间过滤 */
  startTime: z
    .string()
    .optional()
    .transform((val) => (val ? new Date(val) : undefined)),
  /** 结束时间过滤 */
  endTime: z
    .string()
    .optional()
    .transform((val) => (val ? new Date(val) : undefined)),
  /** 跳过条数（分页） */
  skip: z
    .string()
    .optional()
    .default('0')
    .transform((val) => parseInt(val, 10)),
  /** 获取条数（分页） */
  take: z
    .string()
    .optional()
    .default('10')
    .transform((val) => parseInt(val, 10)),
  /** 是否包含已删除 */
  includeDeleted: z
    .string()
    .optional()
    .default('false')
    .transform((val) => val === 'true'),
});

/**
 * 批量删除参数校验
 */
export const importExportTaskBulkDeleteSchema = z.object({
  /** 任务ID数组 */
  ids: z.array(z.string().uuid()),
});

/**
 * 任务状态更新参数校验
 */
export const importExportTaskStatusUpdateSchema = z.object({
  /** 任务状态 */
  status: z.enum(['pending', 'success', 'failed'], { message: '无效的状态值' }),
  /** 任务结果 */
  result: z.any().optional(),
  /** 文件路径 */
  filePath: z.string().optional(),
});
