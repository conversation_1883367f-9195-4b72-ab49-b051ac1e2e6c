import { z } from 'zod';

/**
 * 商品ID参数校验
 */
export const paramIdSchema = z.object({
  /** 商品ID */
  id: z.string().uuid({ message: '无效的ID格式' }),
});

/**
 * 创建/更新商品参数校验
 */
export const upsertProductSchema = z.object({
  /** 商品ID，更新时必传，创建时可选 */
  id: z.string().uuid().optional(),
  /** 商品名称 (必须字段) */
  title: z.string().min(1, { message: '商品标题为必填项' }),
  /** 商品链接或id (必须字段) */
  sourceUrl: z.string().min(1, { message: '商品来源URL为必填项' }),
  /** 商品状态 (必选字段): true=已上架, false=未上架 */
  status: z.boolean({ message: '商品状态为必填项' }),

  // ============ 可选字段 ============
  /** 商品分类 (可选) */
  category: z.string().optional(),
  /** 型号 (可选) */
  model: z.string().optional(),
  /** 商品标签，使用数组存储 (可选) */
  tags: z.array(z.string()).optional(),

  // ============ 平台相关字段 (可选) ============
  /** 100146-平台 */
  platform100146: z.string().optional(),
  /** 162887-平台 */
  platform162887: z.string().optional(),
  /** 180243-平台 */
  platform180243: z.string().optional(),
  /** 上市时间-平台 */
  platformLaunchTime: z.string().optional(),
  /** 功能-平台 */
  platformFunction: z.string().optional(),
  /** 厚度-平台 */
  platformThickness: z.string().optional(),
  /** 图案-平台 */
  platformPattern: z.string().optional(),
  /** 插片-平台 */
  platformInsert: z.string().optional(),
  /** 材质-平台 */
  platformMaterial: z.string().optional(),
  /** 款式-平台 */
  platformStyle: z.string().optional(),
  /** 流行元素-平台 */
  platformTrendElement: z.string().optional(),
  /** 版型-平台 */
  platformFit: z.string().optional(),
  /** 穿着方式-平台 */
  platformWearingMethod: z.string().optional(),
  /** 类别-平台 */
  platformCategory: z.string().optional(),
  /** 罩杯款式-平台 */
  platformCupStyle: z.string().optional(),
  /** 肩带-平台 */
  platformShoulder: z.string().optional(),
  /** 衣长-平台 */
  platformClothingLength: z.string().optional(),
  /** 衣门襟-平台 */
  platformClothingFront: z.string().optional(),
  /** 袖长-平台 */
  platformSleeveLength: z.string().optional(),
  /** 裤长-平台 */
  platformPantsLength: z.string().optional(),
  /** 裤门襟-平台 */
  platformPantsFront: z.string().optional(),
  /** 适用人群-平台 */
  platformTargetGroup: z.string().optional(),
  /** 适用季节-平台 */
  platformSeason: z.string().optional(),
  /** 适用性别-平台 */
  platformGender: z.string().optional(),
  /** 适用运动-平台 */
  platformSport: z.string().optional(),
  /** 里料材质-平台 */
  platformLiningMaterial: z.string().optional(),
  /** 面料-平台 */
  platformFabric: z.string().optional(),
  /** 领型-平台 */
  platformCollarType: z.string().optional(),
});

/**
 * 商品查询参数校验
 */
export const productQuerySchema = z.object({
  /** 按标题模糊搜索 */
  title: z.string().optional(),
  /** 按状态过滤 */
  status: z
    .string()
    .optional()
    .transform((val) => {
      if (val === undefined) return undefined;
      return val === 'true';
    }),
  /** 按分类过滤 */
  category: z.string().optional(),
  /** 按型号过滤 */
  model: z.string().optional(),
  /** 跳过条数（分页） */
  skip: z
    .string()
    .optional()
    .default('0')
    .transform((val) => parseInt(val, 10)),
  /** 获取条数（分页） */
  take: z
    .string()
    .optional()
    .default('10')
    .transform((val) => parseInt(val, 10)),
  /** 是否包含已删除 */
  includeDeleted: z
    .string()
    .optional()
    .default('false')
    .transform((val) => val === 'true'),
});

/**
 * 批量删除参数校验
 */
export const bulkDeleteSchema = z.object({
  /** 商品ID数组 */
  ids: z.array(z.string().uuid()),
});
