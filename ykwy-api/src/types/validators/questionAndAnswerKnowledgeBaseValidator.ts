import { z } from 'zod';

/**
 * 订单状态验证 - 四位字符串
 * 第1位：售前，第2位：发货前，第3位：发货后，第4位：售后
 */
const orderStatusPattern = /^[01]{4}$/;

/**
 * 匹配方式枚举
 */
const MatchModeEnum = z.enum(['full', 'keyword', 'regex'], {
  errorMap: () => ({ message: '匹配方式必须是 full、keyword 或 regex' }),
});

/**
 * 回答验证器
 */
const answerSchema = z.object({
  /** 回答内容 */
  content: z.string().min(1, { message: '回答内容不能为空' }).max(1000, { message: '回答内容不能超过1000个字符' }),
  /** 第几次回复（1=首次，2=第二次…） */
  replyOrder: z.number().int().min(1, { message: '回复顺序必须从1开始' }).max(10, { message: '回复顺序不能超过10' }),
});

/**
 * 创建问答知识库验证器
 */
export const createQuestionAndAnswerKnowledgeBaseSchema = z
  .object({
    /** 问题类型 */
    questionType: z.string().min(1, { message: '问题类型为必填项' }).max(100, { message: '问题类型不能超过100个字符' }),
    /** 订单状态，使用四位字符串表示 */
    orderStatus: z.string().regex(orderStatusPattern, {
      message: '订单状态必须是4位二进制字符串，如：1100表示售前|发货前，1111表示全流程',
    }),
    /** 分类编码 */
    categoryCode: z.string().min(1, { message: '分类编码为必填项' }),
    /** 常见问法样本（可以为空） */
    commonQuestionSamples: z.array(z.string()).optional().default([]),
    /** 是否为自定义问答（true=用户添加，false=行业标准） */
    isCustom: z.boolean().optional().default(false),
    /** 匹配方式：full=整句，keyword=关键词，regex=正则 */
    matchMode: MatchModeEnum.optional().default('full'),
    /** 商品相关ID */
    productId: z.string().uuid().optional(),
    /** 店铺ID */
    shopId: z.string().uuid().optional(),
    /** 时效配置ID */
    timeValidityId: z.string().uuid({ message: '时效配置ID必须是有效的UUID' }),
    /** 回答列表 */
    answers: z.array(answerSchema).min(1, { message: '至少需要一个回答' }).max(10, { message: '回答数量不能超过10个' }),
  })
  .refine(
    (data) => {
      // 检查回复顺序是否连续且唯一
      const replyOrders = data.answers.map((answer) => answer.replyOrder);
      const uniqueOrders = [...new Set(replyOrders)];
      return uniqueOrders.length === replyOrders.length && uniqueOrders.every((order, index) => order === index + 1);
    },
    {
      message: '回复顺序必须从1开始且连续',
      path: ['answers'],
    },
  );

/**
 * 更新问答知识库验证器
 */
export const updateQuestionAndAnswerKnowledgeBaseSchema = z
  .object({
    /** 问答知识库ID */
    id: z.string().uuid({ message: '无效的ID格式' }),
    /** 问题类型 */
    questionType: z.string().min(1, { message: '问题类型为必填项' }).max(100, { message: '问题类型不能超过100个字符' }).optional(),
    /** 订单状态，使用四位字符串表示 */
    orderStatus: z
      .string()
      .regex(orderStatusPattern, {
        message: '订单状态必须是4位二进制字符串，如：1100表示售前|发货前，1111表示全流程',
      })
      .optional(),
    /** 分类编码 */
    categoryCode: z.string().min(1, { message: '分类编码为必填项' }).optional(),
    /** 常见问法样本（可以为空） */
    commonQuestionSamples: z.array(z.string()).optional(),
    /** 是否为自定义问答（true=用户添加，false=行业标准） */
    isCustom: z.boolean().optional(),
    /** 匹配方式：full=整句，keyword=关键词，regex=正则 */
    matchMode: MatchModeEnum.optional(),
    /** 商品相关ID */
    productId: z.string().uuid().optional(),
    /** 店铺ID */
    shopId: z.string().uuid().optional(),
    /** 时效配置ID */
    timeValidityId: z.string().uuid({ message: '时效配置ID必须是有效的UUID' }).optional(),
    /** 回答列表 */
    answers: z.array(answerSchema).min(1, { message: '至少需要一个回答' }).max(10, { message: '回答数量不能超过10个' }).optional(),
  })
  .refine(
    (data) => {
      // 如果有回答数组，检查回复顺序是否连续且唯一
      if (data.answers) {
        const replyOrders = data.answers.map((answer) => answer.replyOrder);
        const uniqueOrders = [...new Set(replyOrders)];
        return uniqueOrders.length === replyOrders.length && uniqueOrders.every((order, index) => order === index + 1);
      }
      return true;
    },
    {
      message: '回复顺序必须从1开始且连续',
      path: ['answers'],
    },
  );

/**
 * 问答知识库查询验证器
 */
export const questionAndAnswerKnowledgeBaseQuerySchema = z.object({
  /** 按问题类型模糊搜索 */
  questionType: z.string().optional(),
  /** 按分类编码精确匹配 */
  categoryCode: z.string().optional(),
  /** 按订单状态精确匹配 */
  orderStatus: z.string().regex(orderStatusPattern).optional(),
  /** 按匹配方式精确匹配 */
  matchMode: MatchModeEnum.optional(),
  /** 按是否自定义过滤 */
  isCustom: z
    .string()
    .optional()
    .transform((val) => val === 'true'),
  /** 按时效类型过滤 */
  validityType: z.string().optional(),
  /** 按店铺ID过滤 */
  shopId: z.string().uuid().optional(),
  /** 按商品ID过滤 */
  productId: z.string().uuid().optional(),
  /** 按时效配置ID过滤 */
  timeValidityId: z.string().uuid().optional(),
  /** 跳过条数（分页） */
  skip: z
    .string()
    .optional()
    .default('0')
    .transform((val) => parseInt(val, 10)),
  /** 获取条数（分页） */
  take: z
    .string()
    .optional()
    .default('10')
    .transform((val) => Math.min(parseInt(val, 10), 100)),
  /** 排序字段 */
  sortBy: z.enum(['createdAt', 'updatedAt', 'questionType', 'categoryCode', 'orderStatus']).optional().default('createdAt'),
  /** 排序方向 */
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
  /** 是否包含已删除 */
  includeDeleted: z
    .string()
    .optional()
    .default('false')
    .transform((val) => val === 'true'),
});

/**
 * ID参数验证器
 */
export const questionAndAnswerKnowledgeBaseParamIdSchema = z.object({
  /** 问答知识库ID */
  id: z.string().uuid({ message: '无效的ID格式' }),
});

/**
 * 分类编码参数验证器
 */
export const questionAndAnswerKnowledgeBaseCategoryCodeParamSchema = z.object({
  /** 分类编码 */
  categoryCode: z.string().min(1, { message: '分类编码不能为空' }),
});

/**
 * 批量删除验证器
 */
export const questionAndAnswerKnowledgeBaseBulkDeleteSchema = z.object({
  /** 要删除的ID数组 */
  ids: z.array(z.string().uuid({ message: '无效的ID格式' })).min(1, { message: '至少需要选择一个项目' }),
});

/**
 * 统计查询验证器
 */
export const questionAndAnswerKnowledgeBaseStatsQuerySchema = z.object({
  /** 开始日期 */
  startDate: z.string().datetime().optional(),
  /** 结束日期 */
  endDate: z.string().datetime().optional(),
  /** 分组方式 */
  groupBy: z.enum(['questionType', 'categoryCode', 'orderStatus', 'matchMode', 'isCustom', 'validityType']).optional().default('categoryCode'),
});

/**
 * 搜索查询验证器
 */
export const questionAndAnswerKnowledgeBaseSearchSchema = z.object({
  /** 搜索关键词 */
  query: z.string().min(1, { message: '搜索关键词不能为空' }).max(100, { message: '搜索关键词不能超过100个字符' }),
  /** 匹配方式 */
  matchMode: MatchModeEnum.optional().default('keyword'),
  /** 按时效类型过滤 */
  validityType: z.string().optional(),
  /** 是否只搜索当前有效的问答 */
  onlyValid: z
    .string()
    .optional()
    .default('true')
    .transform((val) => val === 'true'),
});
