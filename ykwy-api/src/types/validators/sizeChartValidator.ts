import { z } from 'zod';

// --- Reusable Base Schemas ---
export const paramIdSchema = z.object({
  id: z.string().uuid({ message: '无效的 ID 格式' }),
});

export const paramTypeIdSchema = z.object({
  typeId: z.string().uuid({ message: '无效的类型 ID 格式' }),
});

export const paramProductIdSchema = z.object({
  productId: z.string().uuid({ message: '无效的产品 ID 格式' }),
});

// --- 尺码表类型相关 Schemas ---

export const upsertSizeChartTypeSchema = z.object({
  id: z.string().uuid().optional(),
  name: z.string().min(1, { message: '名称为必填项' }),
  xAxis: z.string().min(1, { message: 'X轴名称为必填项' }),
  yAxis: z.string().optional(),
  desc: z.string().optional(),
});

export const sizeChartTypeQuerySchema = z.object({
  name: z.string().optional(),
  skip: z
    .string()
    .optional()
    .default('0')
    .transform((val) => parseInt(val, 10)),
  take: z
    .string()
    .optional()
    .default('10')
    .transform((val) => parseInt(val, 10)),
  includeDeleted: z
    .string()
    .optional()
    .default('false')
    .transform((val) => val === 'true'),
});

// --- 附表结构定义相关 Schemas ---

export const upsertSubTableDefSchema = z.object({
  id: z.string().uuid().optional(),
  typeId: z.string().uuid({ message: '类型ID为必填项' }),
  name: z.string().min(1, { message: '附表名称为必填项' }),
  key: z.string().min(1, { message: '附表键名为必填项' }),
  schema: z.record(z.any()), // JSON Schema
});

// --- 尺码表相关 Schemas ---

export const upsertSizeChartSchema = z.object({
  id: z.string().uuid().optional(),
  name: z.string().min(1, { message: '名称为必填项' }),
  typeId: z.string().uuid({ message: '类型ID为必填项' }),
  isComposite: z.boolean().default(false),
  thresholdRecommendation: z.string().min(1, { message: '临界值推荐为必填项' }),
  parentId: z.string().uuid().optional(),
  groupName: z.string().optional(),
  sortOrder: z.number().int().min(0).default(0),
});

export const sizeChartQuerySchema = z.object({
  name: z.string().optional(),
  typeId: z.string().uuid().optional(),
  isComposite: z
    .string()
    .optional()
    .transform((val) => val === 'true'),
  parentId: z.string().uuid().optional(),
  skip: z
    .string()
    .optional()
    .default('0')
    .transform((val) => parseInt(val, 10)),
  take: z
    .string()
    .optional()
    .default('10')
    .transform((val) => parseInt(val, 10)),
  includeDeleted: z
    .string()
    .optional()
    .default('false')
    .transform((val) => val === 'true'),
});

// --- 尺码表条目相关 Schemas ---

export const upsertSizeChartEntrySchema = z.object({
  id: z.string().uuid().optional(),
  chartId: z.string().uuid({ message: '尺码表ID为必填项' }),
  xValue: z.string().min(1, { message: 'X轴值为必填项' }),
  yValue: z.string().optional(),
  size: z.string().min(1, { message: '尺码值为必填项' }),
});

export const batchCreateEntriesSchema = z.object({
  entries: z.array(upsertSizeChartEntrySchema),
});

// --- 尺码表附表条目相关 Schemas ---

export const upsertSizeChartSubEntrySchema = z.object({
  id: z.string().uuid().optional(),
  chartId: z.string().uuid({ message: '尺码表ID为必填项' }),
  subTableKey: z.string().min(1, { message: '附表键名为必填项' }),
  data: z.record(z.any()), // JSON data
});

export const batchCreateSubEntriesSchema = z.object({
  subEntries: z.array(upsertSizeChartSubEntrySchema),
});

// --- 用于完整尺码表的条目 Schema（不需要 chartId）---
export const fullSizeChartEntrySchema = z.object({
  xValue: z.string().min(1, { message: 'X轴值为必填项' }),
  yValue: z.string().optional(),
  size: z.string().min(1, { message: '尺码值为必填项' }),
});

export const fullSizeChartSubEntrySchema = z.object({
  subTableKey: z.string().min(1, { message: '附表键名为必填项' }),
  data: z.record(z.any()), // JSON data
});

// --- 完整尺码表数据 Schema ---

export const fullSizeChartSchema = z.object({
  chart: upsertSizeChartSchema,
  entries: z.array(fullSizeChartEntrySchema).optional().default([]),
  subEntries: z.array(fullSizeChartSubEntrySchema).optional().default([]),
});

// --- 其他操作相关 Schemas ---

export const assignSizeChartsSchema = z.object({
  sizeChartIds: z.array(z.string().uuid()),
});

export const cloneSizeChartSchema = z.object({
  newName: z.string().min(1, { message: '新名称为必填项' }),
});

export const bulkDeleteSchema = z.object({
  ids: z.array(z.string().uuid()),
});
