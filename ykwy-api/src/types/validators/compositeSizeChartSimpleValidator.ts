import { z } from 'zod';

/**
 * CompositeSizeChartSimple 验证器
 */

export const createCompositeSizeChartSimpleSchema = z.object({
  name: z.string().min(1, '名称不能为空').max(100, '名称长度不能超过100个字符'),
  type: z.string().min(1, '类型不能为空').max(50, '类型长度不能超过50个字符'),
  sizeRange: z.string().min(1, '尺码范围不能为空').max(200, '尺码范围长度不能超过200个字符'),
  sizeValue: z.string().min(1, '尺码值不能为空').max(200, '尺码值长度不能超过200个字符'),
});

export const updateCompositeSizeChartSimpleSchema = z.object({
  name: z.string().min(1, '名称不能为空').max(100, '名称长度不能超过100个字符').optional(),
  type: z.string().min(1, '类型不能为空').max(50, '类型长度不能超过50个字符').optional(),
  sizeRange: z.string().min(1, '尺码范围不能为空').max(200, '尺码范围长度不能超过200个字符').optional(),
  sizeValue: z.string().min(1, '尺码值不能为空').max(200, '尺码值长度不能超过200个字符').optional(),
});

export const getCompositeSizeChartSimpleListSchema = z.object({
  page: z
    .string()
    .optional()
    .transform((val) => (val ? parseInt(val, 10) : 1)),
  limit: z
    .string()
    .optional()
    .transform((val) => (val ? parseInt(val, 10) : 10)),
  name: z.string().optional(),
  type: z.string().optional(),
  sizeRange: z.string().optional(),
  sizeValue: z.string().optional(),
});

export const getCompositeSizeChartSimpleByIdSchema = z.object({
  id: z.string().uuid('无效的ID格式'),
});

export const deleteCompositeSizeChartSimpleSchema = z.object({
  id: z.string().uuid('无效的ID格式'),
});

export const checkCompositeSizeChartNameUniqueSchema = z.object({
  name: z.string().min(1, '名称不能为空'),
  excludeId: z.string().uuid().optional(),
});

export type CreateCompositeSizeChartSimpleInput = z.infer<typeof createCompositeSizeChartSimpleSchema>;
export type UpdateCompositeSizeChartSimpleInput = z.infer<typeof updateCompositeSizeChartSimpleSchema>;
export type GetCompositeSizeChartSimpleListInput = z.infer<typeof getCompositeSizeChartSimpleListSchema>;
export type GetCompositeSizeChartSimpleByIdInput = z.infer<typeof getCompositeSizeChartSimpleByIdSchema>;
export type DeleteCompositeSizeChartSimpleInput = z.infer<typeof deleteCompositeSizeChartSimpleSchema>;
export type CheckCompositeSizeChartNameUniqueInput = z.infer<typeof checkCompositeSizeChartNameUniqueSchema>;
