/**
 * CompositeSizeChartSimple 数据传输对象类型定义
 */

export interface CompositeSizeChartSimpleDto {
  id: string;
  name: string;
  type: string;
  sizeRange: string;
  sizeValue: string;
  createdAt: Date;
  updatedAt: Date;
  isDeleted: number;
}

export interface CreateCompositeSizeChartSimpleDto {
  name: string;
  type: string;
  sizeRange: string;
  sizeValue: string;
}

export interface UpdateCompositeSizeChartSimpleDto {
  name?: string;
  type?: string;
  sizeRange?: string;
  sizeValue?: string;
}

export interface CompositeSizeChartSimpleQueryDto {
  page?: number;
  limit?: number;
  name?: string;
  type?: string;
  sizeRange?: string;
  sizeValue?: string;
}

export interface CompositeSizeChartSimpleListResponseDto {
  data: CompositeSizeChartSimpleDto[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
