import type { AnswerDto } from './answer';
import type { TimeValidityDto } from './timeValidity';

/**
 * 问答知识库基础 DTO
 */
export interface QuestionAndAnswerKnowledgeBaseDto {
  /** 问答知识库ID */
  id: string;
  /** 问题类型 */
  questionType: string;
  /** 订单状态，使用四位字符串表示 */
  orderStatus: string;
  /** 分类编码 */
  categoryCode: string;
  /** 常见问法样本（可以为空） */
  commonQuestionSamples: string[];
  /** 是否为自定义问答（true=用户添加，false=行业标准） */
  isCustom: boolean;
  /** 匹配方式：full=整句，keyword=关键词，regex=正则 */
  matchMode: string;
  /** 商品相关ID */
  productId?: string | null;
  /** 店铺ID（必填） */
  shopId?: string | null;
  /** 时效配置ID */
  timeValidityId: string;
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
  /** 是否已删除 */
  isDeleted: number;
}

/**
 * 问答知识库详细 DTO（包含关联信息）
 */
export interface QuestionAndAnswerKnowledgeBaseDetailDto extends QuestionAndAnswerKnowledgeBaseDto {
  /** 关联的知识库分类信息 */
  category: {
    id: string;
    name: string;
    code: string;
    level: number;
    description?: string | null;
    isActive: boolean;
    parent?: {
      id: string;
      name: string;
      code: string;
    } | null;
  };
  /** 多条回答 */
  answers: AnswerDto[];
  /** 时效配置信息 */
  timeValidity: TimeValidityDto;
  /** 店铺信息 */
  shop?: {
    id: string;
    name: string;
  } | null;
  /** 商品信息 */
  product?: {
    id: string;
    name: string;
  } | null;
}

/**
 * 问答知识库列表 DTO（分页结果）
 */
export interface QuestionAndAnswerKnowledgeBaseListDto {
  /** 问答知识库列表 */
  data: QuestionAndAnswerKnowledgeBaseDto[];
  /** 总数 */
  total: number;
  /** 当前页码 */
  page: number;
  /** 每页条数 */
  pageSize: number;
  /** 总页数 */
  totalPages: number;
}

/**
 * 问答知识库统计 DTO
 */
export interface QuestionAndAnswerKnowledgeBaseStatsDto {
  /** 总数 */
  total: number;
  /** 自定义问答数量 */
  customCount: number;
  /** 标准问答数量 */
  standardCount: number;
  /** 按时效类型统计 */
  byValidityType: Array<{
    validityType: string;
    count: number;
  }>;
  /** 按分类统计 */
  byCategory: Array<{
    categoryCode: string;
    categoryName: string;
    count: number;
  }>;
  /** 按匹配方式统计 */
  byMatchMode: Array<{
    matchMode: string;
    count: number;
  }>;
}

/**
 * 创建问答知识库请求 DTO
 */
export interface CreateQuestionAndAnswerKnowledgeBaseDto {
  /** 问题类型 */
  questionType: string;
  /** 订单状态，使用四位字符串表示 */
  orderStatus: string;
  /** 分类编码 */
  categoryCode: string;
  /** 常见问法样本（可以为空） */
  commonQuestionSamples?: string[];
  /** 是否为自定义问答（true=用户添加，false=行业标准） */
  isCustom?: boolean;
  /** 匹配方式：full=整句，keyword=关键词，regex=正则 */
  matchMode?: string;
  /** 商品相关ID */
  productId?: string;
  /** 店铺ID（必填） */
  shopId?: string;
  /** 时效配置ID */
  timeValidityId: string;
  /** 回答列表 */
  answers: Array<{
    content: string;
    replyOrder: number;
  }>;
}

/**
 * 更新问答知识库请求 DTO
 */
export interface UpdateQuestionAndAnswerKnowledgeBaseDto extends Partial<CreateQuestionAndAnswerKnowledgeBaseDto> {
  /** 问答知识库ID */
  id: string;
}

/**
 * 批量删除结果 DTO
 */
export interface BulkDeleteQuestionAndAnswerKnowledgeBaseResultDto {
  /** 成功删除的数量 */
  deletedCount: number;
  /** 删除的ID列表 */
  deletedIds: string[];
  /** 失败的ID列表 */
  failedIds: string[];
  /** 失败原因 */
  errors?: Array<{
    id: string;
    error: string;
  }>;
}
