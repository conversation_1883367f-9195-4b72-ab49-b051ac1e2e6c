/**
 * 导入导出任务 DTO - 基本信息
 */
export interface ImportExportTaskDto {
  /** 主键 */
  id: string;
  /** 任务名称 */
  taskName: string;
  /** 任务类型: import=导入, export=导出 */
  taskType: 'import' | 'export';
  /** 任务时间 */
  taskTime: Date;
  /** 文件名 */
  fileName: string;
  /** 任务状态: pending=进行中, success=成功, failed=失败 */
  status: 'pending' | 'success' | 'failed';
  /** 任务结果，JSON格式存储结果详情 */
  result?: string | TaskResultDto;
  /** 文件路径 */
  filePath?: string;
  /** 记录创建时间 */
  createdAt: Date;
  /** 记录最后更新时间 */
  updatedAt: Date;
  /** 逻辑删除标志 (0: 未删除, 1: 已删除) */
  isDeleted: number;
}

/**
 * 导入导出任务列表 DTO
 */
export interface ImportExportTaskListDto {
  /** 任务列表 */
  items: ImportExportTaskDto[];
  /** 总数 */
  total: number;
}

/**
 * 批量删除结果 DTO
 */
export interface BulkDeleteResultDto {
  /** 实际删除的数量 */
  count: number;
}

/**
 * 任务统计 DTO
 */
export interface TaskStatisticsDto {
  /** 总任务数 */
  totalTasks: number;
  /** 成功任务数 */
  successTasks: number;
  /** 失败任务数 */
  failedTasks: number;
  /** 进行中任务数 */
  pendingTasks: number;
  /** 导入任务数 */
  importTasks: number;
  /** 导出任务数 */
  exportTasks: number;
}

/**
 * 任务结果详情 DTO
 */
export interface TaskResultDto {
  /** 处理总数 */
  totalCount?: number;
  /** 成功数量 */
  successCount?: number;
  /** 失败数量 */
  failCount?: number;
  /** 新增数量 */
  newCount?: number;
  /** 更新数量 */
  updatedCount?: number;
  /** 错误信息列表 */
  errors?: string[];
  /** 其他详细信息 */
  details?: Record<string, string | number | boolean>;
}
