import type { ShippingRestrictedArea } from '@prisma/client';

/**
 * 发货受限地址 DTO - 基本信息
 */
export type ShippingRestrictedAreaDto = ShippingRestrictedArea;

/**
 * 发货受限地址列表 DTO
 */
export interface ShippingRestrictedAreaListDto {
  /** 发货受限地址列表 */
  items: ShippingRestrictedAreaDto[];
  /** 总数 */
  total: number;
}

/**
 * 批量删除结果 DTO
 */
export interface BulkDeleteResultDto {
  /** 实际删除的数量 */
  count: number;
}

/**
 * 批量创建结果 DTO
 */
export interface BulkCreateResultDto {
  /** 成功创建的数量 */
  successCount: number;
  /** 失败的数量 */
  failCount: number;
  /** 错误信息列表 */
  errors?: string[];
}

/**
 * API 通用响应格式
 */
export interface ApiResponse<T> {
  /** 是否成功 */
  success: boolean;
  /** 返回数据 */
  data?: T;
  /** 错误信息 */
  error?: string;
  /** 分页信息 */
  pagination?: {
    total: number;
    skip: number;
    take?: number;
  };
}
