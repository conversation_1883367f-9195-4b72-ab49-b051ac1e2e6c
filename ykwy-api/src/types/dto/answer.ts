/**
 * 回答 DTO
 */
export interface AnswerDto {
  /** 回答ID */
  id: string;
  /** 回答内容 */
  content: string;
  /** 第几次回复（1=首次，2=第二次…） */
  replyOrder: number;
  /** 所属问答ID */
  questionAndAnswerId: string;
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
}

/**
 * 创建回答请求 DTO
 */
export interface CreateAnswerDto {
  /** 回答内容 */
  content: string;
  /** 第几次回复（1=首次，2=第二次…） */
  replyOrder: number;
  /** 所属问答ID */
  questionAndAnswerId: string;
}

/**
 * 更新回答请求 DTO
 */
export interface UpdateAnswerDto {
  /** 回答ID */
  id: string;
  /** 回答内容 */
  content?: string;
  /** 第几次回复（1=首次，2=第二次…） */
  replyOrder?: number;
}
