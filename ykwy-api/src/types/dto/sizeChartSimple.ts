/**
 * SizeChartSimple 数据传输对象类型定义
 */

export interface SizeChartSimpleDto {
  id: string;
  name: string;
  sizeRange: string;
  sizeValue: string;
  createdAt: Date;
  updatedAt: Date;
  isDeleted: number;
}

export interface CreateSizeChartSimpleDto {
  name: string;
  sizeRange: string;
  sizeValue: string;
}

export interface UpdateSizeChartSimpleDto {
  name?: string;
  sizeRange?: string;
  sizeValue?: string;
}

export interface SizeChartSimpleQueryDto {
  page?: number;
  limit?: number;
  name?: string;
  sizeRange?: string;
  sizeValue?: string;
}

export interface SizeChartSimpleListResponseDto {
  data: SizeChartSimpleDto[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
