import { z } from 'zod';

/**
 * 发货受限地址输入参数校验 Schema
 */
export const shippingRestrictedAreaInputSchema = z.object({
  /** ID，更新时必传，创建时可选 */
  id: z.string().uuid().optional(),
  /** 省份/直辖市 (必须字段) */
  province: z.string().min(1, { message: '省份为必填项' }).max(50, { message: '省份长度不能超过50个字符' }),
  /** 城市 (必须字段) */
  city: z.string().min(1, { message: '城市为必填项' }).max(50, { message: '城市长度不能超过50个字符' }),
  /** 县/区 (必须字段) */
  district: z.string().min(1, { message: '县/区为必填项' }).max(50, { message: '县/区长度不能超过50个字符' }),
  /** 是否受限 (可选字段，默认false) */
  isActive: z.boolean().optional().default(false),
});

/**
 * 发货受限地址查询参数校验 Schema
 */
export const shippingRestrictedAreaQuerySchema = z.object({
  /** 按省份模糊搜索 */
  province: z.string().optional(),
  /** 按城市模糊搜索 */
  city: z.string().optional(),
  /** 按县/区模糊搜索 */
  district: z.string().optional(),
  /** 全文搜索（同时搜索省份/城市/县区） */
  searchText: z.string().optional(),
  /** 按状态过滤 */
  isActive: z
    .string()
    .optional()
    .transform((val) => {
      if (val === undefined || val === '') return undefined;
      return val === 'true';
    }),
  /** 跳过条数（分页） */
  skip: z
    .string()
    .optional()
    .default('0')
    .transform((val) => parseInt(val, 10)),
  /** 取条数（分页） */
  take: z
    .string()
    .optional()
    .default('10')
    .transform((val) => parseInt(val, 10)),
});

/**
 * 发货受限地址ID参数校验 Schema
 */
export const shippingRestrictedAreaParamIdSchema = z.object({
  /** 发货受限地址ID */
  id: z.string().uuid({ message: '无效的ID格式' }),
});

/**
 * 批量删除参数校验 Schema
 */
export const shippingRestrictedAreaBulkDeleteSchema = z.object({
  /** ID列表 */
  ids: z.array(z.string().uuid({ message: '无效的ID格式' })).min(1, { message: '至少选择一个要删除的项目' }),
});

// 导出类型
export type ShippingRestrictedAreaInput = z.infer<typeof shippingRestrictedAreaInputSchema>;
export type ShippingRestrictedAreaQuery = z.infer<typeof shippingRestrictedAreaQuerySchema>;
export type ShippingRestrictedAreaParamId = z.infer<typeof shippingRestrictedAreaParamIdSchema>;
export type ShippingRestrictedAreaBulkDelete = z.infer<typeof shippingRestrictedAreaBulkDeleteSchema>;
