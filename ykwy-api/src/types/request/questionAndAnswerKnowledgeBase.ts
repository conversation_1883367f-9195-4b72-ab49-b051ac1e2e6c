/**
 * 问答知识库查询请求对象
 */
export interface QuestionAndAnswerKnowledgeBaseQuery {
  /** 按问题类型模糊搜索 */
  questionType?: string;
  /** 按分类编码精确匹配 */
  categoryCode?: string;
  /** 按订单状态精确匹配 */
  orderStatus?: string;
  /** 按匹配方式精确匹配 */
  matchMode?: string;
  /** 按是否自定义过滤 */
  isCustom?: boolean;
  /** 按时效类型过滤 */
  validityType?: string;
  /** 按店铺ID过滤 */
  shopId?: string;
  /** 按商品ID过滤 */
  productId?: string;
  /** 按时效配置ID过滤 */
  timeValidityId?: string;
  /** 跳过条数（分页） */
  skip?: number;
  /** 获取条数（分页） */
  take?: number;
  /** 排序字段 */
  sortBy?: string;
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc';
  /** 是否包含已删除 */
  includeDeleted?: boolean;
}

/**
 * 搜索选项
 */
export interface QuestionAndAnswerKnowledgeBaseSearchOptions {
  /** 匹配方式 */
  matchMode?: string;
  /** 时效类型过滤 */
  validityType?: string;
  /** 是否只搜索当前有效的问答 */
  onlyValid?: boolean;
}

/**
 * 统计查询选项
 */
export interface QuestionAndAnswerKnowledgeBaseStatsOptions {
  /** 开始日期 */
  startDate?: string;
  /** 结束日期 */
  endDate?: string;
  /** 分组方式 */
  groupBy?: string;
}

// include 定义
export const QAIncludes = {
  detail: {
    category: {
      include: {
        parent: {
          select: { id: true, name: true, code: true },
        },
      },
    },
    answers: {
      orderBy: { replyOrder: 'asc' as const },
    },
    timeValidity: true,
    Shop: {
      select: { id: true, name: true },
    },
    product: {
      select: { id: true, name: true },
    },
  },
  list: {
    category: {
      select: {
        id: true,
        name: true,
        code: true,
        level: true,
        description: true,
        isActive: true,
        parent: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    },
    answers: {
      select: {
        id: true,
        content: true,
        replyOrder: true,
      },
      orderBy: {
        replyOrder: 'asc' as const,
      },
    },
    timeValidity: {
      select: {
        id: true,
        label: true,
        validityType: true,
        startDateTime: true,
        endDateTime: true,
        startTimeHHMMSS: true,
        endTimeHHMMSS: true,
        repeatWeekdays: true,
        customStartDate: true,
        customEndDate: true,
        customDailyStartTime: true,
        customDailyEndTime: true,
      },
    },
    Shop: {
      select: {
        id: true,
        name: true,
      },
    },
    product: {
      select: {
        id: true,
        name: true,
      },
    },
  },
  basic: {
    category: {
      select: {
        id: true,
        name: true,
        code: true,
        level: true,
        description: true,
        isActive: true,
        parent: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    },
    answers: {
      select: {
        id: true,
        content: true,
        replyOrder: true,
      },
      orderBy: {
        replyOrder: 'asc' as const,
      },
    },
    timeValidity: {
      select: {
        id: true,
        label: true,
        validityType: true,
        startDateTime: true,
        endDateTime: true,
        startTimeHHMMSS: true,
        endTimeHHMMSS: true,
        repeatWeekdays: true,
        customStartDate: true,
        customEndDate: true,
        customDailyStartTime: true,
        customDailyEndTime: true,
      },
    },
    Shop: {
      select: {
        id: true,
        name: true,
      },
    },
    product: {
      select: {
        id: true,
        name: true,
      },
    },
  },
  search: {
    answers: {
      select: {
        content: true,
      },
      orderBy: {
        replyOrder: 'asc' as const,
      },
    },
  },
} as const;
