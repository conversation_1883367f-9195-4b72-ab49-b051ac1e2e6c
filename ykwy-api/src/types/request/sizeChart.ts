import { z } from 'zod';

/**
 * 尺码表类型输入验证 Schema
 */
export const sizeChartTypeInputSchema = z.object({
  id: z.string().uuid().optional(),
  name: z.string().min(1, { message: 'Name is required' }),
  xAxis: z.string().min(1, { message: 'X-axis name is required' }),
  yAxis: z.string().optional(),
  desc: z.string().optional(),
});

/**
 * 附表结构定义输入验证 Schema
 */
export const subTableDefInputSchema = z.object({
  id: z.string().uuid().optional(),
  typeId: z.string().uuid({ message: 'Type ID is required' }),
  name: z.string().min(1, { message: 'Sub table name is required' }),
  key: z.string().min(1, { message: 'Sub table key is required' }),
  schema: z.record(z.any()), // JSON Schema
});

/**
 * 尺码表输入验证 Schema
 */
export const sizeChartInputSchema = z.object({
  id: z.string().uuid().optional(),
  name: z.string().min(1, { message: 'Name is required' }),
  typeId: z.string().uuid({ message: 'Type ID is required' }),
  isComposite: z.boolean().default(false),
  thresholdRecommendation: z.string().min(1, { message: 'Threshold recommendation is required' }),
  parentId: z.string().uuid().optional(),
  groupName: z.string().optional(),
  sortOrder: z.number().int().min(0).default(0),
});

/**
 * 尺码表条目输入验证 Schema
 */
export const sizeChartEntryInputSchema = z.object({
  id: z.string().uuid().optional(),
  chartId: z.string().uuid({ message: 'Chart ID is required' }),
  xValue: z.string().min(1, { message: 'X value is required' }),
  yValue: z.string().optional(),
  size: z.string().min(1, { message: 'Size is required' }),
});

/**
 * 尺码表附表条目输入验证 Schema
 */
export const sizeChartSubEntryInputSchema = z.object({
  id: z.string().uuid().optional(),
  chartId: z.string().uuid({ message: 'Chart ID is required' }),
  subTableKey: z.string().min(1, { message: 'Sub table key is required' }),
  data: z.record(z.any()), // JSON data
});

/**
 * 用于完整尺码表的条目 Schema（不需要 chartId）
 */
export const fullSizeChartEntryInputSchema = z.object({
  xValue: z.string().min(1, { message: 'X value is required' }),
  yValue: z.string().optional(),
  size: z.string().min(1, { message: 'Size is required' }),
});

/**
 * 用于完整尺码表的附表条目 Schema（不需要 chartId）
 */
export const fullSizeChartSubEntryInputSchema = z.object({
  subTableKey: z.string().min(1, { message: 'Sub table key is required' }),
  data: z.record(z.any()), // JSON data
});

/**
 * 完整尺码表数据输入验证 Schema（包含条目和附表数据）
 */
export const fullSizeChartInputSchema = z.object({
  chart: sizeChartInputSchema,
  entries: z.array(fullSizeChartEntryInputSchema).optional().default([]),
  subEntries: z.array(fullSizeChartSubEntryInputSchema).optional().default([]),
});

/**
 * 尺码表类型查询参数验证 Schema
 */
export const sizeChartTypeQuerySchema = z.object({
  name: z.string().optional(),
  skip: z
    .string()
    .transform((val) => parseInt(val, 10))
    .optional(),
  take: z
    .string()
    .transform((val) => parseInt(val, 10))
    .optional(),
  includeDeleted: z
    .string()
    .transform((val) => val === 'true')
    .optional(),
});

/**
 * 尺码表查询参数验证 Schema
 */
export const sizeChartQuerySchema = z.object({
  name: z.string().optional(),
  typeId: z.string().uuid().optional(),
  isComposite: z
    .string()
    .transform((val) => val === 'true')
    .optional(),
  parentId: z.string().uuid().optional(),
  skip: z
    .string()
    .transform((val) => parseInt(val, 10))
    .optional(),
  take: z
    .string()
    .transform((val) => parseInt(val, 10))
    .optional(),
  includeDeleted: z
    .string()
    .transform((val) => val === 'true')
    .optional(),
});

/**
 * ID 参数验证 Schema
 */
export const sizeChartIdSchema = z.object({
  id: z.string().uuid().min(1, 'Size chart ID is required'),
});

export const sizeChartTypeIdSchema = z.object({
  id: z.string().uuid().min(1, 'Size chart type ID is required'),
});

/**
 * 产品 ID 参数验证 Schema
 */
export const productIdSchema = z.object({
  productId: z.string().uuid().min(1, 'Product ID is required'),
});

/**
 * 分配尺码表输入验证 Schema
 */
export const assignSizeChartsSchema = z.object({
  sizeChartIds: z.array(z.string().uuid()),
});

/**
 * 克隆尺码表输入验证 Schema
 */
export const cloneSizeChartSchema = z.object({
  newName: z.string().min(1, 'New name is required'),
});

/**
 * 批量删除输入验证 Schema
 */
export const bulkDeleteSchema = z.object({
  ids: z.array(z.string().uuid()),
});

export const paramIdSchema = z.object({
  id: z.string().uuid({ message: 'Invalid UUID' }),
});

// ============ 类型定义 ============

/**
 * 尺码表类型输入类型
 */
export type SizeChartTypeInput = z.infer<typeof sizeChartTypeInputSchema>;

/**
 * 附表结构定义输入类型
 */
export type SubTableDefInput = z.infer<typeof subTableDefInputSchema>;

/**
 * 尺码表输入类型
 */
export type SizeChartInput = z.infer<typeof sizeChartInputSchema>;

/**
 * 尺码表条目输入类型
 */
export type SizeChartEntryInput = z.infer<typeof sizeChartEntryInputSchema>;

/**
 * 尺码表附表条目输入类型
 */
export type SizeChartSubEntryInput = z.infer<typeof sizeChartSubEntryInputSchema>;

/**
 * 完整尺码表数据输入类型
 */
export type FullSizeChartInput = z.infer<typeof fullSizeChartInputSchema>;

/**
 * 尺码表类型查询参数类型
 */
export type SizeChartTypeQuery = z.infer<typeof sizeChartTypeQuerySchema>;

/**
 * 尺码表查询参数类型
 */
export type SizeChartQuery = z.infer<typeof sizeChartQuerySchema>;

/**
 * 尺码表 ID 参数类型
 */
export type SizeChartIdParam = z.infer<typeof sizeChartIdSchema>;

export type SizeChartTypeIdParam = z.infer<typeof sizeChartTypeIdSchema>;

/**
 * 产品 ID 参数类型
 */
export type ProductIdParam = z.infer<typeof productIdSchema>;

/**
 * 分配尺码表输入类型
 */
export type AssignSizeChartsInput = z.infer<typeof assignSizeChartsSchema>;

/**
 * 克隆尺码表输入类型
 */
export type CloneSizeChartInput = z.infer<typeof cloneSizeChartSchema>;

/**
 * 批量删除输入类型
 */
export type BulkDeleteInput = z.infer<typeof bulkDeleteSchema>;
