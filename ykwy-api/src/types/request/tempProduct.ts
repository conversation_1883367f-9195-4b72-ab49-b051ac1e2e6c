import { z } from 'zod';

/**
 * 临时商品输入参数校验 Schema
 */
export const tempProductInputSchema = z.object({
  /** 临时商品ID，更新时必传，创建时可选 */
  id: z.string().uuid().optional(),
  /** 商品名称 (必须字段) */
  name: z.string().min(1, { message: '商品名称为必填项' }),
  /** 商品链接或id (必须字段) */
  linkOrId: z.string().min(1, { message: '商品链接或ID为必填项' }),
  /** 商品ID (必须字段) */
  productId: z.string().min(1, { message: '商品ID为必填项' }),
  /** 商品状态 (必须字段) */
  status: z.string().min(1, { message: '商品状态为必填项' }),
  /** 货号/款号 (可选字段) */
  styleNumber: z.string().optional(),
  /** 商品图片链接 (可选字段) */
  imageUrl: z.string().url({ message: '图片链接格式不正确' }).optional(),
  /** 描述 (可选字段，JSON格式) */
  description: z.any().optional(),
  /** 店铺ID (可选字段) */
  shopId: z.string().optional(),
});

/**
 * 临时商品查询参数校验 Schema
 */
export const tempProductQuerySchema = z.object({
  /** 按商品名称模糊搜索 */
  name: z.string().optional(),
  /** 按状态过滤 */
  status: z.string().optional(),
  /** 按商品ID过滤 */
  productId: z.string().optional(),
  /** 跳过条数（分页） */
  skip: z
    .string()
    .optional()
    .default('0')
    .transform((val) => parseInt(val, 10)),
  /** 获取条数（分页） */
  take: z
    .string()
    .optional()
    .default('10')
    .transform((val) => parseInt(val, 10)),
  /** 是否包含已删除 */
  includeDeleted: z
    .string()
    .optional()
    .default('false')
    .transform((val) => val === 'true'),
  /** 只查询没有shopId的数据 */
  onlyWithoutShopId: z.boolean().optional(),
});

/**
 * 临时商品ID参数校验 Schema
 */
export const tempProductParamIdSchema = z.object({
  /** 临时商品ID */
  id: z.string().uuid({ message: '无效的ID格式' }),
});

/**
 * 批量删除参数校验 Schema
 */
export const tempProductBulkDeleteSchema = z.object({
  /** 临时商品ID数组 */
  ids: z.array(z.string().uuid()),
});

/**
 * 导出临时商品参数校验 Schema
 */
export const tempProductExportSchema = z.object({
  /** 导出格式 */
  format: z.enum(['excel', 'csv'], { message: '导出格式必须是excel或csv' }),
  /** 要导出的字段列表 */
  fields: z.array(z.enum(['name', 'linkOrId', 'productId', 'status', 'styleNumber', 'imageUrl', 'createdAt', 'updatedAt']), { message: '无效的字段名' }).min(1, { message: '至少选择一个字段' }),
  /** 查询过滤条件（可选） */
  filters: z
    .object({
      /** 按商品名称模糊搜索 */
      name: z.string().optional(),
      /** 按状态过滤 */
      status: z.string().optional(),
      /** 按商品ID过滤 */
      productId: z.string().optional(),
      /** 是否包含已删除 */
      includeDeleted: z.boolean().optional().default(false),
    })
    .optional(),
});

/**
 * 临时商品输入类型
 */
export type TempProductInput = z.infer<typeof tempProductInputSchema>;
/**
 * 临时商品查询参数类型
 */
export type TempProductQuery = z.infer<typeof tempProductQuerySchema>;
/**
 * 临时商品ID参数类型
 */
export type TempProductIdParam = z.infer<typeof tempProductParamIdSchema>;
/**
 * 批量删除参数类型
 */
export type TempProductBulkDelete = z.infer<typeof tempProductBulkDeleteSchema>;
/**
 * 导出参数类型
 */
export type TempProductExport = z.infer<typeof tempProductExportSchema>;
