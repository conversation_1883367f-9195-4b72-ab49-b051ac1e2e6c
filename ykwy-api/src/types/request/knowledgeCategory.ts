import { z } from 'zod';

/**
 * 知识库分类输入参数校验 Schema
 * 用于创建/更新知识库分类时的参数校验
 */
export const knowledgeCategoryInputSchema = z.object({
  /** 分类ID，更新时必传，创建时可选 */
  id: z.string().uuid().optional(),
  /** 分类名称 (必须字段) */
  name: z.string().min(1, { message: '分类名称为必填项' }),
  /** 分类编码 (必须字段) */
  code: z.string().min(1, { message: '分类编码为必填项' }),
  /** 分类级别 (必须字段) */
  level: z.number().int().min(1, { message: '分类级别必须大于0' }),
  /** 父分类ID (可选字段) */
  parentId: z.string().uuid().nullable().optional(),
  /** 排序顺序 (可选字段) */
  sortOrder: z.number().int().min(0).optional().default(0),
  /** 是否启用 (可选字段) */
  isActive: z.boolean().optional().default(true),
  /** 分类描述 (可选字段) */
  description: z.string().optional(),
});

/**
 * 知识库分类查询参数校验 Schema
 * 用于分页、筛选知识库分类列表
 */
export const knowledgeCategoryQuerySchema = z.object({
  /** 按分类名称模糊搜索 */
  name: z.string().optional(),
  /** 按分类编码精确匹配 */
  code: z.string().optional(),
  /** 按分类级别筛选 */
  level: z
    .string()
    .optional()
    .transform((val) => (val ? parseInt(val, 10) : undefined)),
  /** 按父分类ID筛选 */
  parentId: z.string().optional(),
  /** 是否只返回启用的分类 */
  activeOnly: z
    .string()
    .optional()
    .default('true')
    .transform((val) => val === 'true'),
  /** 跳过条数（分页） */
  skip: z
    .string()
    .optional()
    .default('0')
    .transform((val) => parseInt(val, 10)),
  /** 获取条数（分页） */
  take: z
    .string()
    .optional()
    .default('100')
    .transform((val) => Math.min(parseInt(val, 10), 100)),
  /** 排序字段 */
  sortBy: z.enum(['sortOrder', 'createdAt', 'name', 'code']).optional().default('sortOrder'),
  /** 排序方向 */
  sortOrder: z.enum(['asc', 'desc']).optional().default('asc'),
  /** 是否包含已删除 */
  includeDeleted: z
    .string()
    .optional()
    .default('false')
    .transform((val) => val === 'true'),
});

/**
 * 知识库分类ID参数校验 Schema
 * 用于需要传递分类ID的接口
 */
export const knowledgeCategoryParamIdSchema = z.object({
  /** 分类ID */
  id: z.string().uuid({ message: '无效的分类ID格式' }),
});

/**
 * 知识库分类编码参数校验 Schema
 * 用于需要传递分类编码的接口
 */
export const knowledgeCategoryParamCodeSchema = z.object({
  /** 分类编码 */
  code: z.string().min(1, { message: '分类编码不能为空' }),
});

/**
 * 知识库分类编码列表查询参数校验 Schema
 * 用于根据编码列表批量查询分类
 */
export const knowledgeCategoryCodesQuerySchema = z.object({
  /** 编码列表，用逗号分隔 */
  codes: z.string().min(1, { message: '编码列表不能为空' }),
});

/**
 * 批量删除参数校验 Schema
 * 用于批量删除知识库分类
 */
export const knowledgeCategoryBulkDeleteSchema = z.object({
  /** 分类ID数组 */
  ids: z.array(z.string().uuid()),
});

// ================== 向后兼容的别名导出 ==================
export const categoryIdSchema = knowledgeCategoryParamIdSchema;
export const categoryCodeSchema = knowledgeCategoryParamCodeSchema;

/**
 * 知识库分类输入类型
 * 对应 knowledgeCategoryInputSchema
 */
export type KnowledgeCategoryInput = z.infer<typeof knowledgeCategoryInputSchema>;
/**
 * 知识库分类查询参数类型
 * 对应 knowledgeCategoryQuerySchema
 */
export type KnowledgeCategoryQuery = z.infer<typeof knowledgeCategoryQuerySchema>;
/**
 * 知识库分类ID参数类型
 * 对应 knowledgeCategoryParamIdSchema
 */
export type KnowledgeCategoryIdParam = z.infer<typeof knowledgeCategoryParamIdSchema>;
/**
 * 知识库分类编码参数类型
 * 对应 knowledgeCategoryParamCodeSchema
 */
export type KnowledgeCategoryCodeParam = z.infer<typeof knowledgeCategoryParamCodeSchema>;
/**
 * 知识库分类编码列表查询类型
 * 对应 knowledgeCategoryCodesQuerySchema
 */
export type KnowledgeCategoryCodesQuery = z.infer<typeof knowledgeCategoryCodesQuerySchema>;
/**
 * 批量删除参数类型
 * 对应 knowledgeCategoryBulkDeleteSchema
 */
export type KnowledgeCategoryBulkDelete = z.infer<typeof knowledgeCategoryBulkDeleteSchema>;

// ================== 向后兼容的类型别名 ==================
export type CategoryIdParam = KnowledgeCategoryIdParam;
export type CategoryCodeParam = KnowledgeCategoryCodeParam;
