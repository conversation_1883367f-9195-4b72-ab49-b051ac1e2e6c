/**
 * CompositeSizeChartSimple 请求类型定义
 */

export interface CreateCompositeSizeChartSimpleRequest {
  name: string;
  type: string;
  sizeRange: string;
  sizeValue: string;
}

export interface UpdateCompositeSizeChartSimpleRequest {
  name?: string;
  type?: string;
  sizeRange?: string;
  sizeValue?: string;
}

export interface GetCompositeSizeChartSimpleListRequest {
  page?: string;
  limit?: string;
  name?: string;
  type?: string;
  sizeRange?: string;
  sizeValue?: string;
}

export interface GetCompositeSizeChartSimpleByIdRequest {
  id: string;
}

export interface DeleteCompositeSizeChartSimpleRequest {
  id: string;
}
