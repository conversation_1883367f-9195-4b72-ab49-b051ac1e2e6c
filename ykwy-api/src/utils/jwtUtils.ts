import Bun from 'bun';
import { sign, verify } from 'hono/jwt';

import { ErrorCode } from '../constants/errorCodes';
import { AppError } from '../errors/custom.error';
const JWT_SECRET = Bun.env['JWT_SECRET'];
const JWT_ALGORITHM = 'HS256';

if (!JWT_SECRET) {
  throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, {
    message: 'JWT_SECRET环境变量未配置',
  });
}

/**
 * 生成访问令牌 (短期)
 * @param userId - 用户ID
 * @returns 生成的访问令牌
 */
export async function generateAccessToken(userId: string): Promise<string> {
  try {
    const expiresInSeconds = Number(
      process.env['JWT_ACCESS_EXPIRES_IN_SECONDS'] || 1800, // 0.5小时
    );
    const exp = Math.floor(Date.now() / 1000) + expiresInSeconds;
    return await sign({ userId, type: 'access', exp }, JWT_SECRET as string);
  } catch {
    throw new AppError(ErrorCode.AUTH_JWT_GENERATE_FAILED);
  }
}

/**
 * 生成刷新令牌 (长期)
 * @param userId - 用户ID
 * @returns 生成的刷新令牌
 */
export async function generateRefreshToken(userId: string): Promise<string> {
  try {
    const expiresInSeconds = Number(
      process.env['JWT_REFRESH_EXPIRES_IN_SECONDS'] || 604800, // 7天
    );
    const exp = Math.floor(Date.now() / 1000) + expiresInSeconds;
    return await sign({ userId, type: 'refresh', exp }, JWT_SECRET as string);
  } catch {
    throw new AppError(ErrorCode.AUTH_JWT_GENERATE_FAILED);
  }
}

/**
 * 验证JWT令牌
 * @param token - JWT令牌
 * @returns 解码后的用户ID对象
 */
export async function verifyToken(token: string): Promise<{
  userId: string;
}> {
  try {
    const decoded = await verify(token, JWT_SECRET as string, JWT_ALGORITHM);
    if (typeof decoded !== 'object' || !decoded['userId']) {
      throw new AppError(ErrorCode.AUTH_JWT_INVALID);
    }
    return decoded as { userId: string };
  } catch (err) {
    if (err instanceof Error) {
      if (err.message.includes('unsupported algorithm')) {
        throw new AppError(ErrorCode.AUTH_JWT_INVALID);
      }
      if (err.message.includes('invalid token')) {
        throw new AppError(ErrorCode.AUTH_JWT_EXPIRED);
      }
    }
    throw new AppError(ErrorCode.AUTH_JWT_VERIFY_FAILED);
  }
}

/**
 * 从请求头中提取JWT令牌
 * @param authHeader - 请求头中的Authorization字段
 * @returns 提取的令牌或null
 */
export function extractTokenFromHeader(authHeader: string | undefined | null): string | null {
  if (!authHeader) return null;
  const match = authHeader.match(/^Bearer\s+(\S+)$/);
  return match && match[1] ? match[1] : null;
}
