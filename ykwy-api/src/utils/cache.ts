import Redis from 'ioredis';

// 删除用 scan 查询出来的 string keys，性能较高
export async function removeScanKeys(redis: Redis, pattern: string, count?: number) {
  // const [, keys] = await this.redis.scan(0, 'MATCH', 'bot*', 'COUNT', 100);
  const [, keys] = await redis.scan(0, 'MATCH', `${pattern}*`, 'COUNT', count || 100);
  const promises = keys.map((key: string) => redis.del(key));
  await Promise.all(promises);
}
