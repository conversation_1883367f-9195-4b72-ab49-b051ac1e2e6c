import { Hono } from 'hono';

import { ImportExportTaskController } from '../controller/importExportTask';

const router = new Hono();
const importExportTask = new ImportExportTaskController();

// 获取任务列表（支持分页、过滤）
// GET /api/v1/import-export-tasks
router.get('/import-export-tasks', importExportTask.findMany);

// 获取单个任务详情
// GET /api/v1/import-export-task/:id
router.get('/import-export-task/:id', importExportTask.findById);

// 获取任务统计信息
// GET /api/v1/import-export-tasks/statistics
router.get('/import-export-tasks/statistics', importExportTask.getStatistics);

// 获取最近的任务列表
// GET /api/v1/import-export-tasks/recent
router.get('/import-export-tasks/recent', importExportTask.getRecentTasks);

export default router;
