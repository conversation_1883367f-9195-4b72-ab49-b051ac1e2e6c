import { Hono } from 'hono';

import { QuestionAndAnswerController } from '../controller/questionAndAnswer';
import { downloadTemplate, importQAKnowledge } from '../controller/questionAndAnswerFile';

const router = new Hono();
const questionAndAnswer = new QuestionAndAnswerController();

// 文件导入与模板下载
router.get('/questionAndAnswerFile/template', downloadTemplate);
router.post('/questionAndAnswerFile/import', importQAKnowledge); // 超时时间由全局中间件管理

// 创建/更新问答
router.post('/questionAndAnswer', questionAndAnswer.upsert);

// 获取问答列表
router.get('/questionAndAnswers', questionAndAnswer.findMany);

// 获取单个问答详情
router.get('/questionAndAnswer/:id', questionAndAnswer.findById);

// 根据问题类型查找问答
router.get('/questionAndAnswers/type/:questionType', questionAndAnswer.findByQuestionType);

// 根据订单状态查找问答
router.get('/questionAndAnswers/status/:orderStatus', questionAndAnswer.findByOrderStatus);

// 获取所有唯一的问题类型
router.get('/questionAndAnswers/types', questionAndAnswer.getUniqueQuestionTypes);

// 获取统计数据
router.get('/questionAndAnswers/stats', questionAndAnswer.getStats);

// 更新订单状态
router.patch('/questionAndAnswer/:id/status', questionAndAnswer.updateOrderStatus);

// 软删除单个问答
router.delete('/questionAndAnswer/:id', questionAndAnswer.delete);

// 批量软删除
router.post('/questionAndAnswers/bulk-delete', questionAndAnswer.bulkDelete);

// 同步问答知识库到Ragflow
router.post('/questionAndAnswers/sync-ragflow', questionAndAnswer.syncRagflow);
export default router;
