import { Hono } from 'hono';
import { poweredBy } from 'hono/powered-by';
import { secureHeaders } from 'hono/secure-headers';

import { counterLogger, payloadLogger } from '../middlewares/logger.ts';
import { requestLogger } from '../middlewares/requestLogger';
import importExportTaskRouter from '../router/importExportTask.ts';
import knowledgeCategoryRouter from '../router/knowledgeCategory.ts';
import productRouter from '../router/product.ts';
import questionAndAnswerRouter from '../router/questionAndAnswer.ts';
import questionAndAnswerKnowledgeBaseRouter from '../router/questionAndAnswerKnowledgeBase.ts';
import shippingRestrictedAreaRouter from '../router/shippingRestrictedArea.ts';
import sizeChartRouter from '../router/sizeChart.ts';
import tempProductRouter from '../router/tempProduct.ts';
import timeValidityRouter from '../router/timeValidity.ts';
import userRouter from '../router/user.ts';
import v2Router from '../router/v2.ts';
import { compositeSizeChartSimpleRouter } from './compositeSizeChartSimple.ts';
import { frontendLogRouter } from './frontendLog.ts';
import { sizeChartSimpleRouter } from './sizeChartSimple.ts';

const app = new Hono();
app.use(secureHeaders());
app.use(poweredBy());

// 根据 .env 里的配置的 method 与 path 进行匹配，并打印请求体
app.use('*', payloadLogger);
app.use('*', counterLogger);
app.use('*', requestLogger);

app.route('/v1', userRouter);
app.route('/v1', sizeChartRouter);
app.route('/v1/size-chart-simple', sizeChartSimpleRouter);
app.route('/v1/composite-size-chart-simple', compositeSizeChartSimpleRouter);
app.route('/v1', productRouter);
app.route('/v1', tempProductRouter);
app.route('/v2', v2Router); // 统一的v2接口路由，支持多种数据模型
app.route('/v1', importExportTaskRouter);
app.route('/v1', questionAndAnswerRouter);
app.route('/v1', questionAndAnswerKnowledgeBaseRouter);
app.route('/v1', timeValidityRouter);
app.route('/v1', shippingRestrictedAreaRouter);
app.route('/v1', knowledgeCategoryRouter);
app.route('/v1', frontendLogRouter);

export default app;
