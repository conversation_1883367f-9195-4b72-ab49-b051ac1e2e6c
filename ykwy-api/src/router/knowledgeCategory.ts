import { Hono } from 'hono';

import {
  getActiveKnowledgeCategories,
  getKnowledgeCategories,
  getKnowledgeCategoriesByCodes,
  getKnowledgeCategoryByCode,
  getKnowledgeCategoryById,
  getKnowledgeCategoryMap,
  getKnowledgeCategoryTree,
} from '../controller/knowledgeCategory';

const knowledgeCategoryRouter = new Hono();

// 获取知识库分类列表
knowledgeCategoryRouter.get('/knowledgeCategories', getKnowledgeCategories);

// 获取所有启用的分类（用于下拉选择）
knowledgeCategoryRouter.get('/knowledgeCategories/active', getActiveKnowledgeCategories);

// 获取分类树形结构
knowledgeCategoryRouter.get('/knowledgeCategories/tree', getKnowledgeCategoryTree);

// 获取编码到名称的映射
knowledgeCategoryRouter.get('/knowledgeCategories/map', getKnowledgeCategoryMap);

// 根据编码列表批量获取分类信息
knowledgeCategoryRouter.get('/knowledgeCategories/codes', getKnowledgeCategoriesByCodes);

// 根据ID获取分类详情
knowledgeCategoryRouter.get('/knowledgeCategory/:id', getKnowledgeCategoryById);

// 根据编码获取分类详情
knowledgeCategoryRouter.get('/knowledgeCategory/code/:code', getKnowledgeCategoryByCode);

export default knowledgeCategoryRouter;
