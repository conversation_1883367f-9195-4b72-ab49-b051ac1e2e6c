import { Hono } from 'hono';

import { SizeChartController } from '../controller/sizeChart.ts';

const router = new Hono();
const sizeChartController = new SizeChartController();

/**
 * 尺码表相关 API 路由（基础CRUD）
 * /api/v1
 */

// ============ 尺码表类型相关路由 ============

// 创建/更新尺码表类型
router.post('/size-chart-types', sizeChartController.upsertSizeChartType);

// 获取尺码表类型列表
router.get('/size-chart-types', sizeChartController.findSizeChartTypes);

// 获取单个尺码表类型详情
router.get('/size-chart-types/:id', sizeChartController.findSizeChartTypeById);

// 删除尺码表类型
router.delete('/size-chart-types/:id', sizeChartController.deleteSizeChartType);

// ============ 尺码表相关路由 ============

// 创建/更新尺码表
router.post('/size-charts', sizeChartController.upsertSizeChart);

// 获取尺码表列表（支持分页和过滤）
router.get('/size-charts', sizeChartController.findSizeCharts);

// 获取单个尺码表详情
router.get('/size-charts/:id', sizeChartController.findSizeChartById);

// 删除尺码表
router.delete('/size-charts/:id', sizeChartController.deleteSizeChart);

// ============ 尺码表条目相关路由 ============

// 创建/更新尺码表条目
router.post('/size-chart-entries', sizeChartController.upsertSizeChartEntry);

// 获取尺码表的所有条目
router.get('/size-charts/:id/entries', sizeChartController.findSizeChartEntries);

// 批量创建尺码表条目
router.post('/size-charts/:id/entries/batch', sizeChartController.batchCreateEntries);

// 删除尺码表条目
router.delete('/size-chart-entries/:id', sizeChartController.deleteSizeChartEntry);

// 清空尺码表的所有条目
router.delete('/size-charts/:id/entries', sizeChartController.clearSizeChartEntries);

export default router;
