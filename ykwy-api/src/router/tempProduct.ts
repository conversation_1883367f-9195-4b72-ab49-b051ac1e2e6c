import { Hono } from 'hono';

import { TempProductController } from '../controller/tempProduct';
import { TempProductByProductIdController } from '../controller/tempProductByProductId';
import { TempProductFileController } from '../controller/tempProductFile';

const router = new Hono();
const tempProduct = new TempProductController();
const tempProductFile = new TempProductFileController();
const tempProductByProductId = new TempProductByProductIdController();

// 创建/更新临时商品
// POST /api/v1/temp-product
router.post('/temp-product', tempProduct.upsert);

// 获取临时商品列表（支持分页、过滤）
// GET /api/v1/temp-products
router.get('/temp-products', tempProduct.findMany);

// 获取单个临时商品详情
// GET /api/v1/temp-product/:id
router.get('/temp-product/:id', tempProduct.findById);

// 根据productId获取单个临时商品详情
// GET /api/v1/temp-product/by-product-id/:productId
router.get('/temp-product/by-product-id/:productId', tempProductByProductId.findByProductId);

// 软删除单个临时商品
// DELETE /api/v1/temp-product/:id
router.delete('/temp-product/:id', tempProduct.delete);

// 恢复已删除临时商品
// POST /api/v1/temp-product/:id/restore
router.post('/temp-product/:id/restore', tempProduct.restore);

// 批量软删除
// POST /api/v1/temp-products/bulk-delete
router.post('/temp-products/bulk-delete', tempProduct.bulkDelete);

// 批量创建临时商品
// POST /api/v1/temp-products/bulk-create
router.post('/temp-products/bulk-create', tempProduct.bulkCreate);

// 清空所有临时商品
// DELETE /api/v1/temp-products/truncate
router.delete('/temp-products/truncate', tempProduct.truncate);

// 文件操作路由
// 批量导入临时商品（Excel文件）
// POST /api/v1/temp-products/batch-import
router.post('/temp-products/batch-import', tempProductFile.batchImport); // 超时时间由全局中间件管理 // 文件导入需要更长超时

// 下载导入模版
// GET /api/v1/temp-products/import-template
router.get('/temp-products/import-template', tempProductFile.downloadTemplate);

// 导出临时商品数据
// POST /api/v1/temp-products/export
router.post('/temp-products/export', tempProductFile.exportTempProducts);

// 下载导出文件
// GET /api/v1/temp-products/download/:filename
router.get('/temp-products/download/:filename', tempProductFile.downloadExportFile);

// 手动清理过期导出文件
// POST /api/v1/temp-products/cleanup-expired
router.post('/temp-products/cleanup-expired', tempProductFile.cleanupExpiredFiles);
// 同步商品数据到Ragflow知识库
// POST /api/v1/temp-products/sync-ragflow
router.post('/temp-products/sync-ragflow', tempProduct.syncToRagflow);

export default router;
