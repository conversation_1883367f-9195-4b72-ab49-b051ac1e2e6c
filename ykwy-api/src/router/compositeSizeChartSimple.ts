import { Hono } from 'hono';

import { CompositeSizeChartSimpleController } from '../controller/compositeSizeChartSimple.js';

const compositeSizeChartSimpleRouter = new Hono();

// 检查名称是否已存在
compositeSizeChartSimpleRouter.get('/check-name', CompositeSizeChartSimpleController.checkNameUnique);

// 创建复合简单尺码表
compositeSizeChartSimpleRouter.post('/', CompositeSizeChartSimpleController.createCompositeSizeChartSimple);

// 获取复合简单尺码表列表
compositeSizeChartSimpleRouter.get('/', CompositeSizeChartSimpleController.getCompositeSizeChartSimpleList);

// 根据ID获取复合简单尺码表
compositeSizeChartSimpleRouter.get('/:id', CompositeSizeChartSimpleController.getCompositeSizeChartSimpleById);

// 更新复合简单尺码表
compositeSizeChartSimpleRouter.put('/:id', CompositeSizeChartSimpleController.updateCompositeSizeChartSimple);

// 删除复合简单尺码表
compositeSizeChartSimpleRouter.delete('/:id', CompositeSizeChartSimpleController.deleteCompositeSizeChartSimple);

export { compositeSizeChartSimpleRouter };
