import { Hono } from 'hono';

import { SizeChartSimpleController } from '../controller/sizeChartSimple.js';

const sizeChartSimpleRouter = new Hono();

// 检查名称是否已存在
sizeChartSimpleRouter.get('/check-name', SizeChartSimpleController.checkNameUnique);

// 创建简单尺码表
sizeChartSimpleRouter.post('/', SizeChartSimpleController.createSizeChartSimple);

// 获取简单尺码表列表
sizeChartSimpleRouter.get('/', SizeChartSimpleController.getSizeChartSimpleList);

// 根据ID获取简单尺码表
sizeChartSimpleRouter.get('/:id', SizeChartSimpleController.getSizeChartSimpleById);

// 更新简单尺码表
sizeChartSimpleRouter.put('/:id', SizeChartSimpleController.updateSizeChartSimple);

// 删除简单尺码表
sizeChartSimpleRouter.delete('/:id', SizeChartSimpleController.deleteSizeChartSimple);

export { sizeChartSimpleRouter };
