import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * 知识库分类系统使用示例
 *
 * 分类编码格式说明：
 * - 大分类：1, 2, 3, ..., 13
 * - 子分类：1.1, 1.2, 2.1, 2.2 等
 * - 子子分类：1.1.1, 1.1.2, 1.2.1 等
 */

// 1. 获取所有大分类
export async function getMainCategories() {
  return await prisma.knowledgeCategory.findMany({
    where: {
      level: 1,
      isActive: true,
      isDeleted: 0,
    },
    orderBy: {
      sortOrder: 'asc',
    },
  });
}

// 2. 根据分类编码获取分类信息
export async function getCategoryByCode(code: string) {
  return await prisma.knowledgeCategory.findUnique({
    where: {
      code: code,
    },
    include: {
      parent: true,
      children: {
        where: {
          isActive: true,
          isDeleted: 0,
        },
        orderBy: {
          sortOrder: 'asc',
        },
      },
    },
  });
}

// 3. 获取分类的完整层级结构
export async function getCategoryHierarchy() {
  const mainCategories = await prisma.knowledgeCategory.findMany({
    where: {
      level: 1,
      isActive: true,
      isDeleted: 0,
    },
    include: {
      children: {
        where: {
          isActive: true,
          isDeleted: 0,
        },
        include: {
          children: {
            where: {
              isActive: true,
              isDeleted: 0,
            },
            orderBy: {
              sortOrder: 'asc',
            },
          },
        },
        orderBy: {
          sortOrder: 'asc',
        },
      },
    },
    orderBy: {
      sortOrder: 'asc',
    },
  });

  return mainCategories;
}

// 4. 创建子分类的示例函数
export async function createSubCategory(parentCode: string, name: string, description?: string) {
  // 获取父分类信息
  const parentCategory = await prisma.knowledgeCategory.findUnique({
    where: { code: parentCode },
  });

  if (!parentCategory) {
    throw new Error(`父分类 ${parentCode} 不存在`);
  }

  // 生成下一个子分类的编码
  const childrenCount = await prisma.knowledgeCategory.count({
    where: {
      parentId: parentCategory.id,
      isDeleted: 0,
    },
  });

  const nextChildCode = `${parentCode}.${childrenCount + 1}`;

  // 创建子分类
  return await prisma.knowledgeCategory.create({
    data: {
      name,
      code: nextChildCode,
      level: parentCategory.level + 1,
      description,
      parentId: parentCategory.id,
      sortOrder: childrenCount + 1,
    },
  });
}

// 5. 根据分类查找问答记录
export async function getQuestionsByCategory(categoryCode: string) {
  return await prisma.questionAndAnswer.findMany({
    where: {
      categoryCode: categoryCode,
      isDeleted: 0,
    },
    include: {
      category: true,
    },
    orderBy: {
      createdAt: 'desc',
    },
  });
}

// 6. 创建问答记录的示例函数
export async function createQuestionAnswer(data: {
  questionType: string;
  answers: string[];
  categoryCode: string;
  orderStatus: string;
  commonQuestionSamples: string[];
  shopName?: string;
  shopId?: string;
  productName?: string;
  productUrl?: string;
}) {
  // 验证分类编码是否存在
  const category = await prisma.knowledgeCategory.findUnique({
    where: { code: data.categoryCode },
  });

  if (!category) {
    throw new Error(`分类编码 ${data.categoryCode} 不存在`);
  }

  return await prisma.questionAndAnswer.create({
    data,
  });
}

// 7. 使用示例
export async function demonstrateUsage() {
  console.log('=== 知识库分类系统使用演示 ===\n');

  // 显示所有大分类
  console.log('1. 获取所有大分类：');
  const mainCategories = await getMainCategories();
  mainCategories.forEach((cat) => {
    console.log(`   ${cat.code}: ${cat.name} - ${cat.description}`);
  });

  console.log('\n2. 创建子分类示例：');
  try {
    // 为"商品问题"（编码：5）创建子分类
    const subCategory = await createSubCategory('5', '尺码咨询', '关于商品尺码的咨询问题');
    console.log(`   创建子分类成功：${subCategory.code} - ${subCategory.name}`);

    // 为刚创建的子分类创建子子分类
    const subSubCategory = await createSubCategory(subCategory.code, '尺码表查询', '查询具体商品的尺码表');
    console.log(`   创建子子分类成功：${subSubCategory.code} - ${subSubCategory.name}`);
  } catch (error) {
    console.log(`   子分类可能已存在或创建失败：${error}`);
  }

  console.log('\n3. 创建问答记录示例：');
  try {
    const questionAnswer = await createQuestionAnswer({
      questionType: '商品咨询',
      answers: ['请问您需要咨询什么商品信息呢？', '我来为您查询商品详情'],
      categoryCode: '5', // 商品问题
      orderStatus: '0000',
      commonQuestionSamples: ['商品怎么样', '这个商品好不好', '商品质量如何'],
    });
    console.log(`   创建问答记录成功：${questionAnswer.id}`);
  } catch (error) {
    console.log(`   问答记录创建失败：${error}`);
  }

  console.log('\n4. 获取完整的分类层级结构：');
  const hierarchy = await getCategoryHierarchy();
  hierarchy.forEach((mainCat) => {
    console.log(`   ${mainCat.code}: ${mainCat.name}`);
    mainCat.children.forEach((subCat) => {
      console.log(`     ${subCat.code}: ${subCat.name}`);
      subCat.children.forEach((subSubCat) => {
        console.log(`       ${subSubCat.code}: ${subSubCat.name}`);
      });
    });
  });

  console.log('\n=== 演示完成 ===');
}

// 如果直接运行此文件，则执行演示
if (require.main === module) {
  demonstrateUsage()
    .catch((e) => {
      console.error('演示过程中发生错误:', e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

// /**
//  * 获取统计信息
//  */
// async getStats(options?: QuestionAndAnswerKnowledgeBaseStatsOptions): Promise<QuestionAndAnswerKnowledgeBaseStatsDto> {
//   try {
//     const where: Prisma.QuestionAndAnswerKnowledgeBaseWhereInput = { isDeleted: 0 };
//
// if (options?.startDate || options?.endDate) {
//   where.createdAt = {};
//   if (options.startDate) {
//     where.createdAt.gte = new Date(options.startDate);
//   }
//   if (options.endDate) {
//     where.createdAt.lte = new Date(options.endDate);
//   }
// }
//
// const [total, customCount, permanentCount, byCategoryData, byMatchModeData] = await Promise.all([
//   prisma.questionAndAnswerKnowledgeBase.count({ where }),
//   prisma.questionAndAnswerKnowledgeBase.count({
//     where: { ...where, isCustom: true },
//   }),
//   prisma.questionAndAnswerKnowledgeBase.count({
//     where: { ...where, isPermanent: true },
//   }),
//   prisma.questionAndAnswerKnowledgeBase.groupBy({
//     by: ['categoryCode'],
//     where,
//     _count: true,
//   }),
//   prisma.questionAndAnswerKnowledgeBase.groupBy({
//     by: ['matchMode'],
//     where,
//     _count: true,
//   }),
// ]);
//
// // 获取分类名称
// const categoryNames = await prisma.knowledgeCategory.findMany({
//   where: {
//     code: {
//       in: byCategoryData.map((item) => item.categoryCode),
//     },
//   },
//   select: {
//     code: true,
//     name: true,
//   },
// });
//
// const categoryNameMap = new Map(categoryNames.map((cat) => [cat.code, cat.name]));
//
// return {
//   total,
//   customCount,
//   standardCount: total - customCount,
//   permanentCount,
//   temporaryCount: total - permanentCount,
//   byCategory: byCategoryData.map((item) => ({
//     categoryCode: item.categoryCode,
//     categoryName: categoryNameMap.get(item.categoryCode) || item.categoryCode,
//     count: item._count,
//   })),
//   byMatchMode: byMatchModeData.map((item) => ({
//     matchMode: item.matchMode,
//     count: item._count,
//   })),
// };
// } catch (error) {
//   throw new AppError(ErrorCode.G001_UNEXPECTED_ERROR, {
//     message: '获取统计信息失败',
//     originalError: error,
//   });
// }
// }
