import Bun from 'bun';
import type { Context, Next } from 'hono';

import redis from '../client/redis.ts';

export default async (c: Context, next: Next) => {
  await payloadLogger(c, async () => {
    await counterLogger(c, next);
  });
};
export const payloadLogger = async (c: Context, next: Next) => {
  const paths = Bun.env['PRINT_LOG_PATH']?.split('|') || [];
  for (const path of paths) {
    const [left, right] = path.split(':');
    const method = c.req.method;
    if (left === method && right === c.req.path) {
      let payload = {};
      if (method === 'GET') {
        payload = c.req.query();
      } else if (method === 'POST' || method === 'PATCH' || method === 'PUT') {
        payload = await c.req.json();
      }
      console.log('<-- payload', JSON.stringify(payload));
    }
  }
  return await next();
};

export const counterLogger = async (_c: Context, next: Next) => {
  await redis.pipeline().incr('visit_counter').exec();
  return await next();
};
