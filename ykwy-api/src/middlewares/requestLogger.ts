import type { MiddlewareHandler } from 'hono';

import sendLog<PERSON><PERSON><PERSON><PERSON> from '../utils/logger';

const serverName = 'ykwy-api';

/**
 * 请求日志中间件
 * 只记录错误和警告日志，成功的请求不发送到Loki
 * - 异常/错误: 立即发送错误日志
 * - 4xx状态码: 发送警告日志
 * - 5xx状态码: 发送错误日志
 * - 2xx/3xx状态码: 不发送日志（减少Loki负载）
 */
export const requestLogger: MiddlewareHandler = async (c, next) => {
  const { method, url } = c.req;
  const start = Date.now();
  let error: unknown = null;
  const requestBody: unknown = null;
  let queryParams: unknown = null;

  try {
    queryParams = c.req.query();
    await next();
  } catch (err) {
    error = err;
    throw err;
  } finally {
    const duration = Date.now() - start;
    const status = c.res.status;

    if (error && typeof error === 'object') {
      const err = error as { name?: string; message?: string; stack?: string; code?: string; context?: unknown };
      // 结构化详细日志
      const logDetail = {
        request: `${method} ${url}`,
        status,
        duration: `${duration}ms`,
        error: {
          name: err.name,
          message: err.message,
          stack: err.stack,
          code: err.code,
          context: err.context,
        },
        requestBody,
        queryParams,
        timestamp: new Date().toISOString(),
      };
      const prefix = `${method} ${url} ${status} | `;
      await sendLogToLoki(
        {
          service: serverName,
          level: status >= 500 ? 'error' : 'warn',
          module: 'api',
        },
        prefix + JSON.stringify(logDetail),
      );
    }
    // 成功的请求（2xx和3xx）不发送日志
  }
};
