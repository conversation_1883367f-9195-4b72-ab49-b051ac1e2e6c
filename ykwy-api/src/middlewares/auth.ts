import { <PERSON><PERSON><PERSON> } from 'buffer';
import type { Context, Next } from 'hono';

import { ErrorCode } from '../constants/errorCodes';
import { AppError } from '../errors/custom.error';
import { UserService } from '../services/userService';
import { extractTokenFromHeader, verifyToken } from '../utils/jwtUtils';

const userService = new UserService();

/**
 * 支持JWT和Basic Auth的统一认证中间件
 */
export const authMiddleware = async (c: Context, next: () => Promise<void>) => {
  const authHeader = c.req.header('Authorization');
  if (!authHeader) {
    throw new AppError(ErrorCode.AUTH_NO_CREDENTIALS);
  }

  if (authHeader.startsWith('Bearer ')) {
    // JWT认证
    const token = extractTokenFromHeader(authHeader);
    if (!token) {
      throw new AppError(ErrorCode.AUTH_JWT_FORMAT_ERROR);
    }

    const decoded = await verifyToken(token);
    if (!decoded.userId) {
      throw new AppError(ErrorCode.AUTH_JWT_INVALID);
    }

    const user = await userService.getUserById(decoded.userId);
    if (!user) {
      throw new AppError(ErrorCode.AUTH_USER_NOT_FOUND);
    }

    c.set('user', {
      id: user.id,
      email: user.email,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    });
    await next();
  } else if (authHeader.startsWith('Basic ')) {
    // Basic认证
    const base64Credentials = authHeader.replace('Basic ', '');
    const credentials = Buffer.from(base64Credentials, 'base64').toString('utf-8');
    const [email, password] = credentials.split(':');
    if (!email) {
      throw new AppError(ErrorCode.AUTH_USER_NOT_FOUND);
    }
    if (!password) {
      throw new AppError(ErrorCode.AUTH_PASSWORD_INCORRECT);
    }
    const user = await userService.getUserByEmail(email);
    if (!user) {
      throw new AppError(ErrorCode.AUTH_USER_NOT_FOUND);
    }
    const passwordMatch = await userService.verifyPassword(password, user.password);
    if (!passwordMatch) {
      throw new AppError(ErrorCode.AUTH_PASSWORD_INCORRECT);
    }
    // 关键：设置用户信息，供后续 handler 使用
    c.set('user', {
      id: user.id,
      email: user.email,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    });
    await next();
  } else {
    throw new AppError(ErrorCode.AUTH_UNSUPPORTED_STRATEGY);
  }
};

/**
 * 简化的认证中间件调用方式
 */
export const auth = () => authMiddleware;

/**
 * 仅JWT认证中间件
 */
export const jwtAuth = async (c: Context, next: Next) => {
  const authHeader = c.req.header('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new AppError(ErrorCode.AUTH_NO_CREDENTIALS);
  }
  return authMiddleware(c, next);
};

/**
 * 仅Basic认证中间件
 */
export const basicAuth = async (c: Context, next: Next) => {
  const authHeader = c.req.header('Authorization');
  if (!authHeader || !authHeader.startsWith('Basic ')) {
    throw new AppError(ErrorCode.AUTH_NO_CREDENTIALS);
  }
  return authMiddleware(c, next);
};
