import Bun from 'bun';
import type { Context, Next } from 'hono';

export const checkAccess = async (c: Context, next: Next) => {
  const accessToken = c.req.header('Access-Token');

  // 检查accessToken是否存在且不为空
  if (!accessToken || accessToken.trim() === '') {
    return c.json({ msg: 'Unauthorized' }, 403);
  }

  const accessKeys = (Bun.env['OUTER_ACCESS_KEY'] || '').split('|').filter(Boolean);

  // 检查是否配置了有效的访问密钥
  if (accessKeys.length === 0) {
    return c.json({ msg: 'Unauthorized' }, 403);
  }

  for (const key of accessKeys) {
    // 支持两种格式：
    // 1. 简单格式：token1|token2|token3
    // 2. 带标识格式：微信客户端:token1|后台管理系统:token2
    const value = key.includes(':') ? key.split(':')[1] : key;

    // 确保解析出的value不为空
    if (value && value.trim() !== '' && value === accessToken) {
      return await next();
    }
  }
  return c.json({ msg: 'Unauthorized' }, 403);
};
