{"name": "ykwy-api", "version": "0.3.12", "description": "易康无忧客服管理系统 API", "scripts": {"build": "bun build src/index.ts --minify --outdir dist --target bun", "dev": "bun run src/index.ts", "dev:hot": "cross-env PORT=3009 bun --hot run src/index.ts", "lint": "eslint \"{src,test}/**/*.ts\" --fix --cache --cache-location node_modules/.cache/eslint/.eslint-cache", "postinstall": "husky", "tsc": "tsc --noEmit", "test": "bun test --preload ./test/setup.ts"}, "prisma": {"seed": "bun run prisma/seed.ts"}, "dependencies": {"@prisma/client": "^6.11.1", "hono": "^4.8.4", "ioredis": "^5.6.1", "node-cron": "^4.2.0", "papaparse": "^5.5.3", "xlsx": "^0.18.5", "zod": "^3.25.74"}, "devDependencies": {"@eslint/js": "^9.30.1", "@stylistic/eslint-plugin": "^5.1.0", "@types/node-cron": "^3.0.11", "@types/papaparse": "^5.3.16", "@types/bun": "latest", "cross-env": "^7.0.3", "eslint": "^9.30.1", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.3.0", "husky": "^9.1.7", "prisma": "^6.11.1", "typescript-eslint": "^8.35.1"}, "peerDependencies": {"typescript": "^5.8.3"}, "private": true}