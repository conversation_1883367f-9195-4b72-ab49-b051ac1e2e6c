# 新尺码表数据模型设计文档

## 概述

本文档描述了基于 Prisma Schema 的新尺码表数据模型设计，该模型支持更加灵活和复杂的尺码表管理需求。

## 数据模型架构

### 1. 尺码表类型 (SizeChartType)

定义尺码表的基本类型和结构。

```typescript
interface SizeChartType {
  id: string;           // UUID 主键
  name: string;         // 类型名称，如"女士内衣尺码"
  xAxis: string;        // X轴名称，如"胸围"
  yAxis?: string;       // Y轴名称，如"腰围"（可空表示单轴）
  desc?: string;        // 类型描述
  createdAt: DateTime;
  updatedAt: DateTime;
  isDeleted: number;    // 软删除标志
}
```

### 2. 附表结构定义 (SubTableDef)

定义尺码表的附属表格结构，用于存储额外信息。

```typescript
interface SubTableDef {
  id: string;           // UUID 主键
  typeId: string;       // 关联的尺码表类型ID
  name: string;         // 附表名称，如"罩杯对照"
  key: string;          // 附表键名，用于数据存储
  schema: Json;         // 附表结构 JSON Schema
  createdAt: DateTime;
  updatedAt: DateTime;
  isDeleted: number;
}
```

### 3. 尺码表 (SizeChart)

核心尺码表实体。

```typescript
interface SizeChart {
  id: string;                      // UUID 主键
  name: string;                    // 尺码表名称
  typeId: string;                  // 关联的类型ID
  isComposite: boolean;            // 是否为复合表
  thresholdRecommendation: string; // 临界值推荐：推大、推小
  parentId?: string;               // 父表ID，用于复合表分组
  groupName?: string;              // 分组名称，如"儿童"、"成人"
  sortOrder: number;               // 排序
  createdAt: DateTime;
  updatedAt: DateTime;
  isDeleted: number;
}
```

### 4. 尺码表条目 (SizeChartEntry)

存储尺码表的主要数据。

```typescript
interface SizeChartEntry {
  id: string;           // UUID 主键
  chartId: string;      // 关联的尺码表ID
  xValue: string;       // X轴值
  yValue?: string;      // Y轴值，可空用于单轴
  size: string;         // 对应的尺码值
  createdAt: DateTime;
  updatedAt: DateTime;
}
```

### 5. 尺码表附表条目 (SizeChartSubEntry)

存储尺码表的附属信息。

```typescript
interface SizeChartSubEntry {
  id: string;           // UUID 主键
  chartId: string;      // 关联的尺码表ID
  subTableKey: string;  // 对应SubTableDef的key
  data: Json;           // 具体的附表数据
  createdAt: DateTime;
  updatedAt: DateTime;
}
```

## API 端点

### 尺码表类型管理

- `POST /api/v1/size-chart-types` - 创建/更新尺码表类型
- `GET /api/v1/size-chart-types` - 获取尺码表类型列表
- `GET /api/v1/size-chart-types/:id` - 获取单个尺码表类型详情
- `DELETE /api/v1/size-chart-types/:id` - 删除尺码表类型

### 附表结构定义管理

- `POST /api/v1/sub-table-defs` - 创建/更新附表结构定义
- `GET /api/v1/size-chart-types/:typeId/sub-table-defs` - 获取指定类型的附表定义
- `DELETE /api/v1/sub-table-defs/:id` - 删除附表结构定义

### 尺码表管理

- `POST /api/v1/size-charts` - 创建/更新尺码表
- `POST /api/v1/size-charts/full` - 创建完整尺码表（包含条目和附表数据）
- `GET /api/v1/size-charts` - 获取尺码表列表
- `GET /api/v1/size-charts/:id` - 获取单个尺码表详情
- `DELETE /api/v1/size-charts/:id` - 删除尺码表
- `POST /api/v1/size-charts/:id/restore` - 恢复已删除的尺码表
- `POST /api/v1/size-charts/bulk-delete` - 批量删除尺码表
- `POST /api/v1/size-charts/:id/clone` - 复制尺码表

### 尺码表条目管理

- `POST /api/v1/size-chart-entries` - 创建/更新尺码表条目
- `POST /api/v1/size-charts/:id/entries/batch` - 批量创建尺码表条目
- `DELETE /api/v1/size-chart-entries/:id` - 删除尺码表条目

### 尺码表附表条目管理

- `POST /api/v1/size-chart-sub-entries` - 创建/更新尺码表附表条目
- `POST /api/v1/size-charts/:id/sub-entries/batch` - 批量创建尺码表附表条目
- `DELETE /api/v1/size-chart-sub-entries/:id` - 删除尺码表附表条目

### 产品关联管理

- `GET /api/v1/products/:productId/size-charts` - 获取产品关联的尺码表
- `POST /api/v1/products/:productId/size-charts` - 为产品分配尺码表

## 使用示例

### 1. 创建女士内衣尺码表类型

```json
POST /api/v1/size-chart-types
{
  "name": "女士内衣尺码",
  "xAxis": "胸围",
  "yAxis": "腰围",
  "desc": "女士内衣尺码表类型，支持胸围和腰围双轴"
}
```

### 2. 创建附表结构定义

```json
POST /api/v1/sub-table-defs
{
  "typeId": "类型ID",
  "name": "罩杯对照表",
  "key": "cup_mapping",
  "schema": {
    "type": "object",
    "properties": {
      "cup": {"type": "string"},
      "description": {"type": "string"}
    }
  }
}
```

### 3. 创建完整尺码表

```json
POST /api/v1/size-charts/full
{
  "chart": {
    "name": "女士内衣A款尺码表",
    "typeId": "类型ID",
    "isComposite": false,
    "thresholdRecommendation": "推大",
    "sortOrder": 1
  },
  "entries": [
    {
      "xValue": "80",
      "yValue": "65",
      "size": "32A"
    },
    {
      "xValue": "85",
      "yValue": "70",
      "size": "34A"
    }
  ],
  "subEntries": [
    {
      "subTableKey": "cup_mapping",
      "data": {
        "A": "小罩杯",
        "B": "中等罩杯",
        "C": "大罩杯"
      }
    }
  ]
}
```

## 数据模型优势

### 1. 灵活性
- 支持单轴和双轴尺码表
- 可扩展的附表结构
- 支持复合尺码表和分组

### 2. 可维护性
- 清晰的类型定义
- 标准化的JSON Schema验证
- 完整的软删除支持

### 3. 性能
- 适当的索引设计
- 批量操作支持
- 关联查询优化

### 4. 扩展性
- 模块化设计
- 支持未来功能扩展
- 兼容性保障

## 迁移注意事项

1. 数据库迁移需要谨慎执行
2. 旧数据需要进行格式转换
3. API向后兼容性需要考虑
4. 充分的测试覆盖 