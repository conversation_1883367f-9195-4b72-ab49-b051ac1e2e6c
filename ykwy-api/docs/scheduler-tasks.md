# 定时任务系统

## 概述

本系统集成了自动化定时任务，用于维护和清理系统资源。

## 功能特性

### 1. 过期导出文件清理

- **执行时间**: 每天 00:00:00 (中国时间)
- **清理规则**: 删除30天前的导出任务对应的文件
- **安全机制**: 
  - 仅清理temp_files目录内的文件
  - 验证文件路径防止目录遍历攻击
  - 只清理状态为"success"的导出任务
  - 更新任务记录标记文件已清理

### 2. 任务状态记录

- 自动记录清理过程和结果
- 统计成功/失败数量
- 错误详情记录

## API接口

### 手动清理过期文件

```
POST /api/v1/temp-products/cleanup-expired
```

**说明**: 手动触发清理过期导出文件，用于测试或紧急清理

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "message": "过期文件清理完成",
    "cleanupTime": "2025-07-02T10:30:00.000Z"
  }
}
```

## 配置说明

### 清理策略配置

定时任务的配置位于 `src/services/schedulerService.ts`:

```typescript
// 修改清理周期（默认30天）
const thirtyDaysAgo = new Date();
thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30); // 修改这里的天数

// 修改执行时间（默认每天00:00:00）
cron.schedule('0 0 0 * * *', async () => { ... }); // 修改cron表达式
```

### Cron表达式说明

格式: `秒 分 时 日 月 周`

示例:
- `0 0 0 * * *` - 每天00:00:00
- `0 0 2 * * *` - 每天02:00:00  
- `0 0 0 * * 0` - 每周日00:00:00
- `0 0 0 1 * *` - 每月1号00:00:00

## 日志监控

定时任务执行时会输出详细日志:

```
🕐 定时任务服务启动
📅 已启动清理过期导出文件定时任务 (每天00:00:00执行)
📁 开始清理过期导出文件...
🔍 查询2025-06-02T10:30:00.000Z之前的导出任务...
📋 找到 3 个过期导出任务需要清理
🗑️  已删除文件: /path/to/temp_files/导出商品列表-2025-06-01T10-30-00.xlsx
📊 清理完成统计:
   ✅ 成功删除: 3 个文件
   ❌ 删除失败: 0 个文件
✅ 过期导出文件清理完成
```

## 安全考虑

1. **路径验证**: 严格验证文件路径，确保只能删除temp_files目录内的文件
2. **权限控制**: 仅删除已确认的导出任务文件
3. **状态更新**: 删除文件后更新数据库记录，保持数据一致性
4. **错误处理**: 完善的错误处理和日志记录

## 故障排除

### 常见问题

1. **定时任务未启动**
   - 检查服务器日志是否有 "定时任务服务启动" 消息
   - 确认node-cron依赖已正确安装

2. **文件清理失败**
   - 检查temp_files目录权限
   - 查看详细错误日志
   - 确认文件路径格式正确

3. **时区问题**
   - 定时任务使用 `Asia/Shanghai` 时区
   - 确认服务器时间配置正确

### 手动测试

可使用API接口手动触发清理进行测试:

```bash
curl -X POST http://localhost:3009/api/v1/temp-products/cleanup-expired
```

## 扩展功能

系统设计支持添加更多定时任务:

1. 在 `SchedulerService` 类中添加新的定时任务方法
2. 在 `startAllTasks()` 方法中启动新任务
3. 为新任务添加对应的手动触发API

示例扩展:
```typescript
// 添加新的定时任务
private startCleanupTempDataTask() {
  cron.schedule('0 0 3 * * *', async () => {
    // 清理临时数据逻辑
  });
}
``` 