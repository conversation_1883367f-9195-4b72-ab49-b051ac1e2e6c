### 创建精准意图
POST http://localhost:3009/api/v1/precise-intent
Content-Type: application/json

{
  "name": "测试精准意图",
  "type": "本句核心词",
  "keywords": "[\"关键词1\",\"关键词2\"]",
  "contextRange": 2,
  "industryQuestionId": "{{industryQuestionId}}"
}

### 获取精准意图列表
GET http://localhost:3009/api/v1/precise-intents?skip=0&take=10

### 获取单个精准意图详情
GET http://localhost:3009/api/v1/precise-intent/{{preciseIntentId}}

### 更新精准意图
POST http://localhost:3009/api/v1/precise-intent
Content-Type: application/json

{
  "id": "{{preciseIntentId}}",
  "name": "测试精准意图-更新",
  "type": "上文核心词",
  "keywords": "[\"新关键词\"]",
  "contextRange": 3,
  "industryQuestionId": "{{industryQuestionId}}"
}

### 软删除精准意图
DELETE http://localhost:3009/api/v1/precise-intent/{{preciseIntentId}}

### 恢复已删除精准意图
POST http://localhost:3009/api/v1/precise-intent/{{preciseIntentId}}/restore

### 批量软删除
POST http://localhost:3009/api/v1/precise-intents/bulk-delete
Content-Type: application/json

{
  "ids": ["{{preciseIntentId}}", "{{preciseIntentId2}}"]
} 