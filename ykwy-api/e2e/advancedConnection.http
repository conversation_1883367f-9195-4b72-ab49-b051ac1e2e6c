### 创建高级连接
POST http://localhost:3009/api/v1/advanced-connection
Content-Type: application/json

{
  "answerId": "{{answerId}}",
  "contextRange": 2,
  "conditionType": "精准意图",
  "conditionValue": "意图A"
}

### 获取高级连接列表
GET http://localhost:3009/api/v1/advanced-connections?skip=0&take=10&answerId={{answerId}}

### 获取单个高级连接详情
GET http://localhost:3009/api/v1/advanced-connection/{{advancedConnectionId}}

### 更新高级连接
POST http://localhost:3009/api/v1/advanced-connection
Content-Type: application/json

{
  "id": "{{advancedConnectionId}}",
  "answerId": "{{answerId}}",
  "contextRange": 3,
  "conditionType": "核心词-买家",
  "conditionValue": "买家A"
}

### 软删除高级连接
DELETE http://localhost:3009/api/v1/advanced-connection/{{advancedConnectionId}}

### 恢复已删除高级连接
POST http://localhost:3009/api/v1/advanced-connection/{{advancedConnectionId}}/restore

### 批量软删除
POST http://localhost:3009/api/v1/advanced-connections/bulk-delete
Content-Type: application/json

{
  "ids": ["{{advancedConnectionId}}", "{{advancedConnectionId2}}"]
} 