### 临时商品API测试
### 注意：所有查询操作都支持Redis缓存，缓存键格式如下：
### - 详情缓存：temp-product:detail:{id}
### - 列表缓存：temp-product:list:{page}-{limit}-{productTitle}-{brandName}-{productType}-{priceRange}-{isDeleted}
### 写操作会自动清理相关缓存

### 1. 创建临时商品
POST {{host}}/v1/temp-product
Content-Type: application/json

{
  "productTitle": "测试商品",
  "brandName": "测试品牌",
  "productType": "T恤",
  "priceRange": "100-200",
  "sizes": ["S", "M", "L"],
  "colors": ["红色", "蓝色"],
  "tags": ["夏季", "纯棉"]
}

### 2. 获取临时商品详情 (支持缓存)
GET {{host}}/v1/temp-product/{{temp_product_id}}

### 3. 获取临时商品列表 (支持缓存和分页)
GET {{host}}/v1/temp-product?page=1&limit=10

### 4. 获取临时商品列表 - 带筛选条件 (支持缓存)
GET {{host}}/v1/temp-product?page=1&limit=10&productTitle=测试&brandName=品牌

### 5. 更新临时商品
PUT {{host}}/v1/temp-product
Content-Type: application/json

{
  "id": "{{temp_product_id}}",
  "productTitle": "更新的商品标题",
  "brandName": "更新的品牌",
  "productType": "衬衫",
  "priceRange": "200-300",
  "sizes": ["M", "L", "XL"],
  "colors": ["白色", "黑色"],
  "tags": ["商务", "正装"]
}

### 6. 软删除临时商品
DELETE {{host}}/v1/temp-product/{{temp_product_id}}

### 7. 恢复已删除的临时商品
POST {{host}}/v1/temp-product/{{temp_product_id}}/restore

### 8. 批量删除临时商品
POST {{host}}/v1/temp-product/bulk-delete
Content-Type: application/json

{
  "ids": ["{{temp_product_id1}}", "{{temp_product_id2}}"]
}

### 9. 批量创建临时商品
POST {{host}}/v1/temp-product/bulk-create
Content-Type: application/json

{
  "dataList": [
    {
      "productTitle": "批量商品1",
      "brandName": "批量品牌",
      "productType": "连衣裙",
      "priceRange": "300-500"
    },
    {
      "productTitle": "批量商品2",
      "brandName": "批量品牌",
      "productType": "半身裙",
      "priceRange": "200-400"
    }
  ]
}

### 10. 批量导入临时商品 (Excel/CSV文件)
POST {{host}}/v1/temp-product/batch-import
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="temp_products.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./test-files/temp_products.xlsx
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 11. 下载导入模版 (Excel格式)
GET {{host}}/v1/temp-product/template?format=excel

### 12. 下载导入模版 (CSV格式)
GET {{host}}/v1/temp-product/template?format=csv

### 13. 清空所有临时商品
DELETE {{host}}/v1/temp-product/truncate

### 14. 通过productId获取临时商品详情 (accesstoken认证) - 成功场景
GET http://localhost:3009/api/v2/temp-product/product/914630029253
Access-Token:PoNyP4f0zYDWuXg8NytR

### 15. 通过productId获取临时商品详情 (accesstoken认证) - 缺少token
GET http://localhost:3009/api/v2/temp-product/product/914630029253

### 16. 通过productId获取临时商品详情 (accesstoken认证) - 错误token
GET http://localhost:3009/api/v2/temp-product/product/914630029253
Access-Token: invalid_token

### 17. 通过productId获取临时商品详情 (accesstoken认证) - 不存在的productId
GET http://localhost:3009/api/v2/temp-product/product/NONEXISTENT123
Access-Token: PoNyP4f0zYDWuXg8NytR

### 18. 验证v2路径上其他接口不可访问 (安全测试)
GET http://localhost:3009/api/v2/temp-products
Access-Token: PoNyP4f0zYDWuXg8NytR

### 19. 验证v1接口仍需要JWT认证 (安全测试)
GET http://localhost:3009/api/v1/temp-products

### 20. 安全测试：空Access-Token
GET http://localhost:3009/api/v2/temp-product/product/914630029253
Access-Token: 

### 21. 安全测试：只包含空格的Access-Token
GET http://localhost:3009/api/v2/temp-product/product/914630029253
Access-Token:    

### 缓存测试说明：
### - 第一次调用查询API时会查询数据库并缓存结果
### - 再次调用相同查询时会直接返回缓存数据（响应更快）
### - 执行写操作（创建、更新、删除）后相关缓存会被清理
### - 缓存过期时间由环境变量 REDIS_CACHE_EXPIRE 控制（默认3600秒）
### - 列表查询的缓存键包含所有查询参数，确保不同查询条件有独立缓存
### 
### API架构说明：
### v1接口 (/api/v1/*):
### - 使用JWT认证，需要在请求头中提供Bearer token
### - 包含所有CRUD操作：增删改查、批量操作、文件导入导出等
### - 验证失败返回401状态码
### - 面向内部管理系统，提供完整的数据操作权限
### 
### v2接口 (/api/v2/*):
### - 统一管理所有v2版本接口，支持多种数据模型
### - 使用AccessToken认证，需要在请求头中提供Access-Token
### - 主要提供查询类接口，为外部系统提供有限的只读访问权限
### - 不使用缓存，每次都直接查询数据库确保数据实时性
### - Access-Token必须与环境变量OUTER_ACCESS_KEY匹配
### - 验证失败返回403状态码和JSON错误信息
### - 路由文件：src/router/v2.ts 集中管理所有v2接口
### - 安全特性：
###   * 拒绝空或只包含空格的Access-Token
###   * 过滤OUTER_ACCESS_KEY中的空值部分，防止配置错误导致的安全漏洞
###   * 支持简单格式(token1|token2)和带标识格式(app1:token1|app2:token2)
### 
### 当前v2接口列表：
### - GET /temp-product/product/:productId - 通过productId获取临时商品详情