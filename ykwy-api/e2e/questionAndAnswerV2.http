### 问答知识库 V2 API 测试
### 使用 AccessToken 认证的搜索接口

### 1. 根据查询字符串搜索问答知识库 - 匹配问题类型
GET http://localhost:3009/api/v2/question-answer/search?query=差价补多少
Access-Token: PoNyP4f0zYDWuXg8NytR

### 2. 根据查询字符串搜索问答知识库 - 匹配常见问法样本
GET http://localhost:3009/api/v2/question-answer/search?query=什么快递
Access-Token: PoNyP4f0zYDWuXg8NytR

### 3. 查询不存在的内容 - 返回空数组
GET {{host}}/api/v2/question-answer/search?query=不存在的问题
Access-Token: {{accessToken}}

### 3.1. 模糊匹配测试 - 部分匹配问题类型
GET {{host}}/api/v2/question-answer/search?query=快递
Access-Token: {{accessToken}}

### 3.2. 不区分大小写测试
GET {{host}}/api/v2/question-answer/search?query=KUAIDI
Access-Token: {{accessToken}}

### 4. 查询参数为空 - 返回错误
GET {{host}}/api/v2/question-answer/search?query=
Access-Token: {{accessToken}}

### 4.1. 查询参数过长 - 返回错误
GET {{host}}/api/v2/question-answer/search?query=这是一个非常长的查询字符串用于测试最大长度限制这是一个非常长的查询字符串用于测试最大长度限制这是一个非常长的查询字符串用于测试最大长度限制这是一个非常长的查询字符串用于测试最大长度限制这是一个非常长的查询字符串用于测试最大长度限制这是一个非常长的查询字符串用于测试最大长度限制
Access-Token: {{accessToken}}

### 5. 缺少查询参数 - 返回错误
GET {{host}}/api/v2/question-answer/search
Access-Token: {{accessToken}}

### 6. 安全测试：无AccessToken - 返回403
GET {{host}}/api/v2/question-answer/search?query=发货

### 7. 安全测试：错误的AccessToken - 返回403
GET {{host}}/api/v2/question-answer/search?query=发货
Access-Token: invalid-token

### API说明：
### 这个接口用于根据查询字符串搜索问答知识库
### - 使用AccessToken认证，需要在请求头中提供有效的Access-Token
### - 接收一个string类型的query查询参数（通过URL参数传递）
### - 同时匹配问题类型(questionType)和常见问法样本(commonQuestionSamples)
### - 在匹配常见问法时，与数组中的每个元素单独匹配
### - 只要问题类型或常见问法中有一个匹配，就返回对应的回答数组
### - 如果没有匹配则返回空数组
### - 支持不区分大小写的匹配
### - 自动去重返回的回答 