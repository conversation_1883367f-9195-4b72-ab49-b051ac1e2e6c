import { PrismaClient } from '@prisma/client';
import { seedSizeChartsSimple } from './seed-size-charts-simple.js';
import { seedKnowledgeCategories } from './seed-knowledge-categories.js';

const prisma = new PrismaClient();

// 为种子数据定义一个清晰的类型
type ProductSeedData = {
  title: string;
  sourceUrl: string;
  status: boolean; // 改为boolean类型
  category?: string;
  model?: string;
  tags?: string[];
  // 可以添加一些平台相关字段作为示例
  platformFunction?: string;
  platformMaterial?: string;
  platformStyle?: string;
  platformSeason?: string;
  platformGender?: string;
  sizeChartNames: string[];
  identifiers: {
    identifierType: string;
    value: string;
  }[];
};

/**
 * 封装创建尺码表的逻辑
 * @param prisma PrismaClient 实例
 */
async function createSizeCharts(prisma: PrismaClient) {
  console.log('Creating size chart types...');
  
  // 先创建SizeChartType
  const womenType = await prisma.sizeChartType.create({
    data: {
      name: '女装尺码',
      xAxis: '胸围',
      yAxis: '腰围',
      desc: '女士服装尺码表',
    },
  });

  const menType = await prisma.sizeChartType.create({
    data: {
      name: '男装尺码',
      xAxis: '胸围',
      yAxis: '腰围', 
      desc: '男士服装尺码表',
    },
  });

  console.log('Creating size charts...');
  const sizeChartWomenLoose = await prisma.sizeChart.create({
    data: {
      name: '女士-宽松',
      typeId: womenType.id,
      isComposite: false,
      thresholdRecommendation: '推大',
      sortOrder: 1,
    },
  });

  const sizeChartMenRegular = await prisma.sizeChart.create({
    data: {
      name: '男士-标准',
      typeId: menType.id,
      isComposite: false,
      thresholdRecommendation: '推小',
      sortOrder: 2,
    },
  });

  console.log('Size charts created successfully.');
  return { sizeChartWomenLoose, sizeChartMenRegular };
}

/**
 * 封装创建商品的逻辑
 * @param prisma PrismaClient 实例
 * @param sizeCharts 包含已创建尺码表的对象
 */
async function createProducts(
    prisma: PrismaClient,
    sizeCharts: {
      sizeChartWomenLoose: { id: string };
      sizeChartMenRegular: { id: string };
  },
) {
  console.log('Creating products...');
  const productsData: ProductSeedData[] = [
    {
      title: '时尚休闲T恤',
      sourceUrl: 'https://detail.1688.com/offer/123456789.html',
      status: true, // 已上架
      category: '服装',
      model: 'T001',
      tags: ['夏季', '短袖', '棉质'],
      platformFunction: '舒适透气',
      platformMaterial: '100%纯棉',
      platformStyle: '休闲款',
      platformSeason: '夏季',
      platformGender: '男女通用',
      sizeChartNames: ['女士-宽松'],
      identifiers: [
        {
          identifierType: 'SUPPLIER_SKU',
          value: 'SUP-T-2023-001',
        },
        {
          identifierType: 'INTERNAL_CODE',
          value: 'TS-2023-001',
        },
      ],
    },
    {
      title: '男士牛仔裤',
      sourceUrl: 'https://detail.1688.com/offer/987654321.html',
      status: false, // 未上架
      category: '服装',
      model: 'J001',
      tags: ['秋冬', '长裤', '牛仔'],
      platformFunction: '耐用修身',
      platformMaterial: '牛仔布',
      platformStyle: '修身款',
      platformSeason: '秋冬',
      platformGender: '男士',
      sizeChartNames: ['男士-标准'],
      identifiers: [
        {
          identifierType: 'SUPPLIER_SKU',
          value: 'SUP-J-2023-042',
        },
      ],
    },
    {
      title: '运动休闲鞋',
      sourceUrl: 'https://detail.1688.com/offer/456789123.html',
      status: true, // 已上架
      category: '鞋靴',
      model: 'S001',
      tags: ['运动', '透气', '轻便'],
      platformFunction: '运动休闲',
      platformMaterial: '透气网面',
      platformStyle: '运动款',
      platformSeason: '全季',
      platformGender: '男女通用',
      sizeChartNames: [],
      identifiers: [
        {
          identifierType: 'SUPPLIER_SKU',
          value: 'SUP-S-2023-078',
        },
        {
          identifierType: 'BARCODE',
          value: '8901234567890',
        },
      ],
    },
  ];

  for (const product of productsData) {
    const { sizeChartNames, identifiers, ...productCoreData } = product;

    await prisma.product.create({
      data: {
        ...productCoreData,
        sizeChartBindings: {
          create: sizeChartNames.map((name) => ({
            sizeChartId: name === '女士-宽松' ? sizeCharts.sizeChartWomenLoose.id : sizeCharts.sizeChartMenRegular.id,
          })),
        },
        identifiers: {
          create: identifiers,
        },
      },
    });
  }

  console.log('Products created successfully.');
}

async function main() {
  console.log('🚀 Starting seed process...');

  console.log('🧹 Clearing old data...');
  await prisma.productSizeChartBinding.deleteMany();
  await prisma.productIdentifier.deleteMany();
  await prisma.product.deleteMany();
  await prisma.sizeChartEntry.deleteMany();
  await prisma.sizeChartSubEntry.deleteMany();
  await prisma.sizeChart.deleteMany();
  await prisma.subTableDef.deleteMany();
  await prisma.sizeChartType.deleteMany();

  const createdSizeCharts = await createSizeCharts(prisma);
  await createProducts(prisma, createdSizeCharts);

  // 调用简单尺码表seed函数
  console.log('🌱 Seeding simple size charts...');
  await seedSizeChartsSimple(prisma, false);

  // 调用知识库分类seed函数
  console.log('🌱 Seeding knowledge categories...');
  await seedKnowledgeCategories(prisma);

  console.log('✅ Seeding finished.');
  console.log(`- Created ${await prisma.product.count()} products`);
  console.log(`- Created ${await prisma.productIdentifier.count()} product identifiers`);
  console.log(`- Created ${await prisma.sizeChartType.count()} size chart types`);
  console.log(`- Created ${await prisma.sizeChart.count()} size charts`);
  console.log(`- Created ${await prisma.productSizeChartBinding.count()} product-size chart bindings`);
  console.log(`- Created ${await prisma.sizeChartSimple.count()} simple size charts`);
  console.log(`- Created ${await prisma.compositeSizeChartSimple.count()} composite simple size charts`);
  console.log(`- Created ${await prisma.knowledgeCategory.count()} knowledge categories`);
  console.log(`- Created ${await prisma.questionAndAnswer.count()} question and answers`);
}

main()
    .catch((e) => {
      console.error('❌ An error occurred during seeding:', e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
