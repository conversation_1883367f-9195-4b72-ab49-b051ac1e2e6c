import { PrismaClient } from '@prisma/client';

/**
 * 简单尺码表种子数据
 */
const sizeChartSimpleData = [
  {
    name: '女士 宽松',
    sizeRange: '成人体重(84.0斤～149.9斤),成人身高(150.0厘米～179.9厘米)',
    sizeValue: 'L, M, S, XL, XS'
  },
  {
    name: '男士 宽松',
    sizeRange: '成人体重(98.0斤～186.0斤),成人身高(160.0厘米～189.9厘米)',
    sizeValue: 'L, M, S, XL, XXL'
  },
  {
    name: '男士 修身/合身版',
    sizeRange: '成人体重(98.0斤～186.0斤),成人身高(160.0厘米～189.9厘米)',
    sizeValue: 'L, M, S, XL, XXL'
  },
  {
    name: '女士 修身/合身版',
    sizeRange: '成人体重(84.0斤～149.9斤),成人身高(150.0厘米～179.9厘米)',
    sizeValue: 'L, M, S, XL, XS'
  }
];

/**
 * 复合简单尺码表种子数据
 */
const compositeSizeChartSimpleData = [
  {
    name: '男女情侣装/合身版',
    type: '情侣身高体重尺码表',
    sizeRange: '男款: 成人体重(98.0斤～186.0斤),成人身高(160.0厘米～189.9厘米) 女款: 成人体重(84.0斤～149.9斤),成人身高(150.0厘米～179.9厘米)',
    sizeValue: '男款: L,M,S,XL,XXL 女款: L,M,S,XL,XS'
  },
  {
    name: '情侣宽松版',
    type: '情侣身高体重尺码表',
    sizeRange: '男款: 成人体重(80.0斤～159.9斤),成人身高(155.0厘米～184.9厘米) 女款: 成人体重(84.0斤～149.9斤),成人身高(150.0厘米～179.9厘米)',
    sizeValue: '男款: L,M,S,XL,XXL 女款: L,M,S,XL,XS'
  }
];

async function seedSizeChartsSimple(prismaInstance?: PrismaClient, shouldClearData = false) {
  const prisma = prismaInstance || new PrismaClient();
  console.log('🌱 开始播种简单尺码表数据...');

  try {
    // 只有在shouldClearData为true时才清理现有数据
    if (shouldClearData) {
      await prisma.compositeSizeChartSimple.deleteMany();
      await prisma.sizeChartSimple.deleteMany();
      console.log('✅ 清理现有数据完成');
    }

    // 插入简单尺码表数据
    console.log('📝 插入简单尺码表数据...');
    for (const data of sizeChartSimpleData) {
      await prisma.sizeChartSimple.create({
        data,
      });
    }
    console.log(`✅ 成功插入 ${sizeChartSimpleData.length} 条简单尺码表记录`);

    // 插入复合简单尺码表数据
    console.log('📝 插入复合简单尺码表数据...');
    for (const data of compositeSizeChartSimpleData) {
      await prisma.compositeSizeChartSimple.create({
        data,
      });
    }
    console.log(`✅ 成功插入 ${compositeSizeChartSimpleData.length} 条复合简单尺码表记录`);

    console.log('🎉 简单尺码表种子数据播种完成！');
  } catch (error) {
    console.error('❌ 播种简单尺码表数据失败:', error);
    throw error;
  } finally {
    if (!prismaInstance) {
      await prisma.$disconnect();
    }
  }
}

// 如果直接运行此文件，则执行播种
if (import.meta.main) {
  seedSizeChartsSimple(undefined, true).catch((error) => {
    console.error(error);
    process.exit(1);
  });
}

export { seedSizeChartsSimple }; 