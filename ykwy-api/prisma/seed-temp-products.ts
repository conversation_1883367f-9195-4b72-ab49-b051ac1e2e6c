import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * 封装创建临时商品数据的逻辑
 * @param prisma PrismaClient 实例
 */
async function createTempProducts(prisma: PrismaClient) {
  console.log('开始创建临时商品数据...');

  // 前10条CSV数据（来自完整结果_123542.csv）
  const tempProductsData = [
    {
      name: '轻功短袖POLO衫男款女款夏季透气排汗短袖',
      linkOrId: 'https://item.taobao.com/item.htm?id=932971667258',
      productId: '932971667258',
      status: '已上架',
      styleNumber: 'M250011',
      imageUrl: 'https://img.alicdn.com/imgextra/i2/2209305722753/O1CN01HN7bQk1WCvQhmKWjP_!!2209305722753.jpg_.webp',
    },
    {
      name: 'QINKUNG轻功跑步文化T运动户外透气短袖圆领防晒跑步短袖',
      linkOrId: 'https://item.taobao.com/item.htm?id=918472921649',
      productId: '918472921649',
      status: '已上架',
      styleNumber: 'M220020',
      imageUrl: 'https://img.alicdn.com/imgextra/i2/2209305722753/O1CN01vnCvWC1WCvQ8GWU1I_!!2209305722753.jpg_.webp',
    },
    {
      name: 'QINKUNG轻功速干慢跑T夏季半袖圆领跑步休闲短袖T恤',
      linkOrId: 'https://item.taobao.com/item.htm?id=918471189226',
      productId: '918471189226',
      status: '已上架',
      styleNumber: 'M230024',
      imageUrl: 'https://img.alicdn.com/imgextra/i3/2209305722753/O1CN01aVRZRA1WCvQ8peWq3_!!2209305722753.jpg_.webp',
    },
    {
      name: 'QINKUNG轻功速干慢跑T夏季半袖圆领跑步休闲短袖T恤',
      linkOrId: 'https://item.taobao.com/item.htm?id=917948098996',
      productId: '917948098996',
      status: '已上架',
      styleNumber: 'M230024',
      imageUrl: 'https://img.alicdn.com/imgextra/i3/2209305722753/O1CN01aVRZRA1WCvQ8peWq3_!!2209305722753.jpg_.webp',
    },
    {
      name: 'QINKUNG轻功跑步休闲小立领夏季透气舒适跑步T恤短袖',
      linkOrId: 'https://item.taobao.com/item.htm?id=918914240972',
      productId: '918914240972',
      status: '已上架',
      styleNumber: 'M230025',
      imageUrl: 'https://img.alicdn.com/imgextra/i4/2209305722753/O1CN01ijYw4w1WCvQAhiZ35_!!2209305722753.jpg_.webp',
    },
    {
      name: 'QINKUNG轻功 长款羽绒服 男款女款 （宽松版型）',
      linkOrId: 'https://item.taobao.com/item.htm?id=914495698689',
      productId: '914495698689',
      status: '已上架',
      styleNumber: 'M240022',
      imageUrl: 'https://img.alicdn.com/imgextra/i4/2209305722753/O1CN01Wlr2Wq1WCvQ38KGdL_!!2209305722753.jpg_.webp',
    },
    {
      name: 'QINKUNG轻功 加厚热力怪连帽跑步棉服 男款女款',
      linkOrId: 'https://item.taobao.com/item.htm?id=914164487680',
      productId: '914164487680',
      status: '已上架',
      styleNumber: 'M240041',
      imageUrl: 'https://img.alicdn.com/imgextra/i4/2209305722753/O1CN01SS8QRi1WCvQ2zcADA_!!2209305722753.jpg_.webp',
    },
    {
      name: '【新品】QINKUNG轻功 热力怪修身短款跑步棉服 女款（修身版型）',
      linkOrId: 'https://item.taobao.com/item.htm?id=914494834078',
      productId: '914494834078',
      status: '已上架',
      styleNumber: 'F240006',
      imageUrl: 'https://img.alicdn.com/imgextra/i2/2209305722753/O1CN01J47saU1WCvPwuN4Cy_!!2209305722753.jpg_.webp',
    },
    {
      name: 'QINKUNG轻功 热力怪立领跑步棉服 男款女款',
      linkOrId: 'https://item.taobao.com/item.htm?id=914493982456',
      productId: '914493982456',
      status: '已上架',
      styleNumber: 'M240012',
      imageUrl: 'https://img.alicdn.com/imgextra/i2/2209305722753/O1CN01o8BeIi1WCvQ1rIuzV_!!2209305722753.jpg_.webp',
    },
    {
      name: 'QINKUNG轻功 热力方格圆领跑步长袖 男款女款（合身版型）',
      linkOrId: 'https://item.taobao.com/item.htm?id=914493282392',
      productId: '914493282392',
      status: '已上架',
      styleNumber: 'M240046',
      imageUrl: 'https://img.alicdn.com/imgextra/i1/2209305722753/O1CN01iLAi0U1WCvQ54y4lu_!!2209305722753.jpg_.webp',
    },
  ];

  // 批量创建临时商品
  for (const tempProduct of tempProductsData) {
    await prisma.tempProduct.create({
      data: tempProduct,
    });
  }

  console.log('临时商品数据创建完成');
}

async function main() {
  console.log('🚀 开始导入临时商品数据...');

  console.log('🧹 清理旧的临时商品数据...');
  await prisma.tempProduct.deleteMany();

  await createTempProducts(prisma);

  console.log('✅ 临时商品数据导入完成.');
  console.log(`- 创建了 ${await prisma.tempProduct.count()} 个临时商品`);
}

main()
  .catch((e) => {
    console.error('❌ 导入临时商品数据时发生错误:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 