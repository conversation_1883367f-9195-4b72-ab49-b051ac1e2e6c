# 易客维助手应用

一个集成千牛智能客服的多端应用系统。

## 📁 项目结构

```
ykwy-assistant-app/
├── ykwy-assistant-api/          # 后端API服务
│   ├── src/                     # 源代码
│   ├── prisma/                  # 数据库模型
│   ├── docs/                    # API文档
│   │   └── qianniu-api-openapi.json # OpenAPI规范文件
│   ├── test/                    # HTTP客户端测试
│   │   ├── e2e.rest            # 系统API测试
│   │   └── qianniu-api-e2e.rest # 千牛API测试
│   └── package.json
├── ykwy-assistant-web/          # 前端Web应用
├── taobao-product-imformation/  # 淘宝商品信息服务
├── qianniu-sdk-test-example/    # 千牛SDK测试示例
├── QIANNIU_API_INTEGRATION.md   # 千牛API集成文档
└── APIFOX_IMPORT_GUIDE.md       # Apifox导入指南
```

## 🚀 快速开始

### 后端API服务
```bash
cd ykwy-assistant-api
npm install
npm run dev
```

### 前端Web应用
```bash
cd ykwy-assistant-web
npm install
npm run dev
```

## 📚 文档

- **[千牛API集成文档](QIANNIU_API_INTEGRATION.md)** - 详细的千牛API集成说明
- **[Apifox导入指南](APIFOX_IMPORT_GUIDE.md)** - 如何将API导入Apifox进行测试
- **[OpenAPI规范](ykwy-assistant-api/docs/qianniu-api-openapi.json)** - 标准的API接口定义

## 🧪 测试

### HTTP客户端测试
使用VS Code的REST Client扩展或其他HTTP客户端工具：

1. **系统API测试**：`ykwy-assistant-api/test/e2e.rest`
2. **千牛API测试**：`ykwy-assistant-api/test/qianniu-api-e2e.rest`

### Apifox测试
1. 导入 `ykwy-assistant-api/docs/qianniu-api-openapi.json` 文件
2. 配置环境变量
3. 开始测试API接口

## 🔧 技术栈

### 后端
- **运行时**：Bun
- **框架**：Hono
- **数据库**：PostgreSQL + Prisma
- **WebSocket**：原生WebSocket
- **千牛集成**：千牛SDK + WebSocket通信

### 前端
- **框架**：React + TypeScript
- **构建工具**：Vite
- **UI组件**：Ant Design
- **状态管理**：Zustand

## 🌟 核心功能

### 千牛API集成
- ✅ 订单管理（邀请下单、查询订单、物流查询）
- ✅ 商品管理（商品卡片、商品记录、店铺搜索）
- ✅ 用户管理（客户信息、买家搜索）
- ✅ 优惠券管理（查询、发送优惠券）
- ✅ 客服操作（挂起、转接、获取客服）
- ✅ 客户ID接口（自动获取encryptId）
- ✅ 通用接口（支持所有千牛MTOP接口）

### 系统功能
- ✅ 多组织架构
- ✅ 用户权限管理
- ✅ 平台集成管理
- ✅ 客户关系管理
- ✅ 对话消息系统
- ✅ 实时WebSocket通信

## 📝 开发规范

### 代码仓库原则
- **业务代码优先** - 主要包含核心业务逻辑
- **简单测试** - 只保留HTTP客户端测试和Apifox文档
- **文档集中** - 所有文档统一放在根目录
- **结构清晰** - 避免复杂的测试脚本和工具

### 测试策略
- **手动测试** - 使用REST文件进行接口测试
- **集成测试** - 通过Apifox进行完整的API测试
- **真实环境** - 在实际的千牛环境中验证功能

## 🔗 相关链接

- [千牛开放平台](https://open.taobao.com/doc.htm?docId=106593&docType=1)
- [Apifox官网](https://www.apifox.cn/)
- [Hono框架](https://hono.dev/)
- [Prisma ORM](https://www.prisma.io/)

## 📄 许可证

本项目采用 MIT 许可证。
