using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using TouchSocket.Core;
using TouchSocket.Sockets;
using EngineIOSharp.Common.Enum;
using SocketIOSharp.Client;
using SocketIOSharp.Common;
using SocketIOSharp.Server;
using SocketIOSharp.Server.Client;
using System.Runtime.ConstrainedExecution;
using System.Collections;
using System.ComponentModel.Design;

namespace QNTEST
{
    internal class Program
    {
        private static Queue<ManualResetEventSlim> _requestWaitHandles = new Queue< ManualResetEventSlim>();
        private static Queue<string> _responses = new Queue<string>();
        static void Main(string[] args)
        {


            //Process.Start(new ProcessStartInfo
            //{
            //    FileName = "QNQtHelp.exe",
            //    Arguments = "25432",
            //});

            var service = new TcpService();
            service.Connecting = (client, e) =>
            {

            };//有客户端正在连接
            service.Connected = (client, e) =>
            {
                //var requestResetEvent = new ManualResetEventSlim(false);
                //_requestWaitHandles.Enqueue(requestResetEvent);
                var payload = JsonConvert.SerializeObject(new { method = "getusername" });
                client.Send(payload + "\r\n\r\n");

                //requestResetEvent.Wait();
                //var rt = _responses.Dequeue();

                client.OnHandleReceivedData = (a,b) =>
                {
                    return true;
                };
            };//有客户端连接
            service.Disconnected = (client, e) =>
            {

            };//有客户端断开连接
            service.Received = (client, byteBlock, requestInfo) =>
            {
                //从客户端收到信息
                string response = byteBlock.ToString();
                Console.WriteLine($"已从{client.ID}接收到信息：{response}");

                SendText(client);


                    //SendFile(client);

                    //SendVideo(client);
                    //SetWarn(client);

                    //CloseTalker(client);
            };

            service.Setup(new TouchSocketConfig()//载入配置
                .SetListenIPHosts(new IPHost[] { new IPHost("127.0.0.1:9996") }))//同时监听两个地址
                .Start();//启动
            Console.ReadLine();
        }

        static void SetLight(SocketClient client)
        {

            var payload = JsonConvert.SerializeObject(
                   new
                   {
                       method = "highlight",
                       id = "2807792688.1-1597305140.1#11001@cntaobao",
                       flash = "close"
                   }
                   );
            client.Send(payload + "\r\n\r\n");
        }

        static async void SendText(SocketClient client)
        {
            var requestResetEvent = new ManualResetEventSlim(false);
            _requestWaitHandles.Enqueue(requestResetEvent);
            var payload = JsonConvert.SerializeObject(
                   new
                   {
                       method = "sendtext",
                       id = "2807792688.1-1597305140.1#11001@cntaobao",
                       text = @"亲，您好！请问有什么可以帮您的吗？",
                       keep = "on",
                   }
                   );
            client.Send(payload + "\r\n\r\n");

            await Task.Run(() => {

            requestResetEvent.Wait();

            var result = _responses.Dequeue();
            });

        }

        static void SendImage(SocketClient client)
        {
            var payload = JsonConvert.SerializeObject(
                   new
                   {
                       method = "sendimage",
                       id = "2807792688.1-1597305140.1#11001@cntaobao",
                       image = @"C:\\Users\\<USER>\\Desktop\\test11.jpg",
                       keep = "off",
                   }
                   );
            client.Send(payload + "\r\n\r\n");
        }

        static void SendVideo(SocketClient client)
        {
            //普通发视频
            var payload = JsonConvert.SerializeObject(
                   new
                   {
                       method = "sendvideo",
                       id = "2807792688.1-1597305140.1#11001@cntaobao",
                       video = @"C:\1.MP4",
                       keep = "off",
                   }
                   );
            client.Send(payload + "\r\n\r\n");
        }
        static void CloseTalker(SocketClient client)
        {
            var payload = JsonConvert.SerializeObject(
                   new
                   {
                       method = "closetalker",
                       id = "2807792688.1-1597305140.1#11001@cntaobao",
                   }
                   );
            client.Send(payload + "\r\n\r\n");
        }

    }

}
