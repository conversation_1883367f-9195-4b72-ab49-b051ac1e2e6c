# 易康无忧客服助手

易康无忧客服助手 - 基于 React + Vite + TypeScript 构建的现代化 Web 应用。

## 功能特性

- ⚡ Vite 快速构建工具
- ⚛️ React 18 + TypeScript
- 🎨 Tailwind CSS + shadcn/ui 组件库
- 🔄 TanStack Query 数据管理
- 🛣️ React Router 路由管理
- 🎯 ESLint + Prettier 代码规范
- 📱 响应式设计

## 快速开始

### 环境要求

- Bun >= 1.1.0
- Node.js >= 18

### 安装依赖

```bash
bun install
```

### 环境配置

复制环境变量文件并配置：

```bash
cp .env.example .env
```

### 启动开发服务器

```bash
bun run dev
```

应用将在 http://localhost:3000 启动。

## 可用脚本

- `bun run dev` - 启动开发服务器
- `bun run build` - 构建生产版本
- `bun run preview` - 预览生产构建
- `bun run lint` - 运行 ESLint 检查
- `bun run lint:fix` - 自动修复 ESLint 问题
- `bun run typecheck` - TypeScript 类型检查

## 项目结构

```
src/
├── components/       # React 组件
│   ├── main/        # 主要功能组件
│   ├── qianniu/     # 千牛相关组件
│   └── ui/          # 基础 UI 组件
├── lib/             # 工具库
├── services/        # API 服务
│   ├── api/         # API 客户端
│   ├── hooks/       # 自定义 Hooks
│   └── types/       # 类型定义
├── styles/          # 样式文件
└── main.tsx         # 应用入口


├── eslint-config/   # ESLint 配置
├── shared/          # 共享工具
├── typescript-config/ # TypeScript 配置
└── ui/              # UI 组件库
```

## 主要功能

### 对话管理
- 实时消息收发
- 对话列表管理
- 消息历史查看
- 搜索和过滤

### AI 助手
- 销售智能体推荐
- 自动回复配置
- 智能对话生成
- 回复建议生成

### 千牛集成
- 千牛客户端连接
- 消息同步
- 状态监控
- 平台桥接

### 用户管理
- 身份认证
- 权限控制
- 团队管理
- 组织架构

## 技术栈

- **框架**: React 18 + TypeScript
- **构建工具**: Vite
- **样式**: Tailwind CSS
- **组件库**: shadcn/ui
- **状态管理**: TanStack Query
- **路由**: React Router
- **HTTP 客户端**: 自定义 API 客户端
- **代码规范**: ESLint + Prettier

## 环境变量

主要环境变量配置：

```env
# API 服务地址
VITE_API_URL="http://localhost:3002"

# 应用配置
VITE_APP_NAME="千牛助手"
VITE_APP_VERSION="1.0.0"
```

## 构建部署

### 生产构建

```bash
bun run build
```

构建产物将输出到 `dist/` 目录。

### 静态部署

构建完成后，可以将 `dist/` 目录部署到任何静态文件服务器：

- Nginx
- Apache
- Vercel
- Netlify
- GitHub Pages

### Docker 部署

```dockerfile
docker build -f Dockerfile -t ykwy-assistant-web --no-cache --progress=plain .

docker run --rm --name ykwy-assistant-web -p 8080:80 ykwy-assistant-web
```

## 开发指南

### 组件开发

使用 shadcn/ui 组件库进行开发：

```bash
# 添加新组件
npx shadcn-ui@latest add button
```

### API 集成

使用统一的 API 客户端：

```typescript
import { apiClient } from '@/services/api/client'

// GET 请求
const conversations = await apiClient.get('/conversations')

// POST 请求
const newMessage = await apiClient.post('/messages', { content: 'Hello' })
```

### 状态管理

使用 TanStack Query 进行数据管理：

```typescript
import { useQuery } from '@tanstack/react-query'
import { conversationService } from '@/services/api/conversation'

function ConversationList() {
  const { data, isLoading } = useQuery({
    queryKey: ['conversations'],
    queryFn: conversationService.getAll
  })

  // ...
}
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证。
