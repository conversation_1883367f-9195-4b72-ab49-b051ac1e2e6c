import { useEffect, useState } from 'react';

/**
 * 防抖Hook，延迟更新值，用于处理频繁变化的输入
 * @param value 需要防抖的值
 * @param delay 延迟时间(毫秒)，默认300毫秒
 * @returns 防抖后的值
 */
export function useDebounce<T>(value: T, delay: number = 300): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    // 设置定时器，延迟更新值
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    // 在下一次effect运行之前清除定时器
    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);

  return debouncedValue;
}
