import { useQueryClient } from '@tanstack/react-query';
import { useCallback, useEffect, useRef, useState } from 'react';
import { toast } from 'sonner';

import { matches, Pattern } from '@/lib/query-utils';
import { type NewCustomerMessage, type NewNotification, useAppStore } from '@/lib/store';

interface UseWebSocketOptions {
  userId?: string;
  organizationId?: string;
  enabled?: boolean;
}

type InvalidateMsg = {
  type: 'invalidate';
  payload: { pattern: Pattern; exact?: boolean };
};

type PatchMsg = {
  type: 'patch';
  payload: { queryKey: unknown[]; data: unknown };
};

type AutoReplyDisabledMsg = {
  type: 'auto-reply-disabled';
  payload: {
    conversationId: string;
    customerName: string;
    timestamp: string;
  };
};

type AutoReplyEnabledMsg = {
  type: 'auto-reply-enabled';
  payload: {
    conversationId: string;
    customerName: string;
    timestamp: string;
  };
};

type AutoReplyStatusChangedMsg = {
  type: 'auto-reply-enabled' | 'auto-reply-disabled';
  payload: {
    conversationId: string;
    customerName: string;
    timestamp: string;
    enabled?: boolean;
  };
};

type CustomerMessageMsg = {
  type: 'customer-message';
  payload: {
    messageId: string;
    conversationId: string;
    customerName: string;
    timestamp: string;
    isAutoReplied: boolean;
  };
};

type WebSocketMessage = InvalidateMsg | PatchMsg | AutoReplyDisabledMsg | AutoReplyEnabledMsg | AutoReplyStatusChangedMsg | CustomerMessageMsg;

export type WSStatus = 'idle' | 'connecting' | 'open' | 'closed' | 'error';

// 简单合并工具，这里假设数据结构是扁平
function merge(prev: unknown, patch: unknown): unknown {
  if (Array.isArray(prev) && Array.isArray(patch)) return [...prev, ...patch];
  if (typeof prev === 'object' && typeof patch === 'object') return { ...prev, ...patch };
  return patch;
}

declare global {
  interface Window {
    webkitAudioContext: typeof AudioContext;
  }
}

// 创建通知音效
function playNotificationSound() {
  // 使用 Web Audio API 创建简短的提示音
  try {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.type = 'sine';
    oscillator.frequency.setValueAtTime(800, audioContext.currentTime); // 频率
    oscillator.frequency.exponentialRampToValueAtTime(500, audioContext.currentTime + 0.2); // 频率变化

    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime); // 音量
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3); // 音量衰减

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.start();
    oscillator.stop(audioContext.currentTime + 0.3); // 持续0.3秒

    return true;
  } catch (error) {
    console.error('创建通知音效失败:', error);
    return false;
  }
}

export function useWebSocket({ userId, organizationId, enabled = true }: UseWebSocketOptions) {
  const queryClient = useQueryClient();
  const ws = useRef<WebSocket | null>(null);
  const [status, setStatus] = useState<WSStatus>('idle');

  // 获取添加通知的方法
  const addAutoReplyNotification = useAppStore((state) => state.addAutoReplyNotification);

  // 获取添加客户消息的方法
  const addCustomerMessage = useAppStore((state) => state.addCustomerMessage);

  const connect = useCallback(() => {
    if (!enabled || !userId || !organizationId || ws.current) return;

    setStatus('connecting');
    const raw = import.meta.env.VITE_API_URL || 'localhost:3002';
    const wsBase = raw.startsWith('http')
      ? raw.replace(/^http/, 'ws') // 把 http/https 替换为 ws/wss
      : `ws://${raw}`;
    ws.current = new WebSocket(`${wsBase}/ws?userId=${userId}&organizationId=${organizationId}`);

    ws.current.onopen = () => {
      console.log('[WS] connected');
      setStatus('open');
    };

    ws.current.onmessage = (event) => {
      console.log('[WS] raw →', event.data);
      try {
        const msg: WebSocketMessage = JSON.parse(event.data);
        console.log('[WS] parsed →', msg);

        if (msg.type === 'invalidate') {
          const { pattern, exact = true } = msg.payload;
          console.log('pattern', pattern.toString());
          console.log('exact', exact);
          console.log('[WS] invalidate pattern', pattern, 'exact', exact);
          queryClient.invalidateQueries({
            predicate: (q) => matches(q.queryKey as readonly unknown[], pattern, { exact }),
          });
        } else if (msg.type === 'patch') {
          queryClient.setQueryData(msg.payload.queryKey, (prev: unknown) => merge(prev, msg.payload.data));
        } else if (msg.type === 'auto-reply-disabled') {
          // 处理自动回复关闭通知
          const { customerName, conversationId, timestamp } = msg.payload;
          console.log('[WS] auto-reply-disabled', customerName, conversationId);

          // 播放提示音
          playNotificationSound();

          // 添加到状态存储
          const notification: NewNotification = {
            conversationId,
            customerName,
            timestamp,
          };
          addAutoReplyNotification(notification);

          // 显示通知
          toast.warning(`客户 ${customerName} 的自动回复已关闭，请及时处理`, {
            id: `auto-reply-disabled-${conversationId}`,
            duration: 10000, // 10秒
          });

          // 同时刷新会话列表
          queryClient.invalidateQueries({
            queryKey: ['app', 'conversations'],
          });
        } else if (msg.type === 'auto-reply-enabled') {
          // 处理自动回复启用通知
          const { customerName, conversationId } = msg.payload;
          console.log('[WS] auto-reply-enabled', customerName, conversationId);

          // 播放提示音（可选）
          playNotificationSound();

          // 显示通知
          toast.success(`客户 ${customerName} 的自动回复已开启，系统将自动处理消息`, {
            id: `auto-reply-enabled-${conversationId}`,
            duration: 5000, // 5秒
          });

          // 同时刷新会话列表
          queryClient.invalidateQueries({
            queryKey: ['app', 'conversations'],
          });
        } else if (msg.type === 'customer-message') {
          // 处理客户新消息
          const { messageId, conversationId, customerName, timestamp, isAutoReplied } = msg.payload;
          console.log('[WS] customer-message', customerName, conversationId, messageId);

          // 播放提示音（如果没有自动回复）
          if (!isAutoReplied) {
            playNotificationSound();
          }

          // 添加到状态存储
          const message: NewCustomerMessage = {
            messageId,
            conversationId,
            customerName,
            timestamp,
            isAutoReplied,
          };
          addCustomerMessage(message);

          // 如果没有自动回复，显示通知
          if (!isAutoReplied) {
            toast.info(`收到客户 ${customerName} 的新消息，请及时处理`, {
              id: `customer-message-${messageId}`,
              duration: 10000, // 10秒
            });
          }

          // 同时刷新会话列表
          queryClient.invalidateQueries({
            queryKey: ['app', 'conversations'],
          });
        }
      } catch (err) {
        console.error('[WS] parse error', err);
      }
    };

    ws.current.onclose = (event) => {
      const closeInfo = {
        timestamp: new Date().toISOString(),
        code: event.code,
        reason: event.reason || '无原因说明',
        wasClean: event.wasClean,
        url: wsBase,
      };

      console.warn('[WS] 🔌 连接已关闭:', closeInfo);

      // 根据关闭代码提供更详细的信息
      if (event.code === 1000) {
        console.log('[WS] ℹ️ 正常关闭');
      } else if (event.code === 1001) {
        console.log('[WS] ℹ️ 端点离开 (页面刷新或导航)');
      } else if (event.code === 1006) {
        console.error('[WS] ❌ 异常关闭 (可能是网络问题或服务器错误)');
      } else if (event.code >= 4000) {
        console.error('[WS] ❌ 应用程序错误:', event.reason);
      } else {
        console.warn('[WS] ⚠️ 其他关闭原因');
      }

      setStatus('closed');
      ws.current = null;

      console.log('[WS] ⏰ 5秒后尝试重连...');
      setTimeout(connect, 5_000);
    };

    ws.current.onerror = (event) => {
      const errorInfo = {
        timestamp: new Date().toISOString(),
        readyState: ws.current?.readyState,
        readyStateText:
          ws.current?.readyState === 0 ? 'CONNECTING' : ws.current?.readyState === 1 ? 'OPEN' : ws.current?.readyState === 2 ? 'CLOSING' : ws.current?.readyState === 3 ? 'CLOSED' : 'UNKNOWN',
        url: wsBase,
        event,
      };

      console.error('[WS] ❌ WebSocket 错误:', errorInfo);

      // 🔧 移除错误监控上报，只在控制台记录
      console.error(`WebSocket connection error: ${errorInfo.readyStateText}`, {
        type: 'websocket_error',
        readyState: errorInfo.readyState,
        url: wsBase.replace(/token=[^&]+/, 'token=***'), // 隐藏token
        userId,
        organizationId,
        timestamp: errorInfo.timestamp,
      });

      // 提供可能的解决建议
      console.error('[WS] 🔧 可能的解决方案:');
      console.error('  1. 检查网络连接');
      console.error('  2. 确认服务器是否运行在', raw);
      console.error('  3. 检查防火墙设置');
      console.error('  4. 验证用户认证状态');
      console.error('  5. 检查服务器WebSocket配置');

      setStatus('error');
      ws.current?.close();
    };
  }, [enabled, userId, organizationId, queryClient]);

  useEffect(() => {
    console.log('[WS] 🚀 useEffect: 初始化WebSocket连接', {
      enabled,
      hasUserId: !!userId,
      hasOrganizationId: !!organizationId,
      currentStatus: status,
    });

    // 只有在满足连接条件时才尝试连接
    if (enabled && userId && organizationId && !ws.current) {
      connect();
    }

    return () => {
      console.log('[WS] 🧹 useEffect: 清理WebSocket连接');
      if (ws.current) {
        console.log('[WS] 🔌 主动关闭WebSocket连接');
        ws.current.close(1000, '组件卸载');
        ws.current = null;
      }
      setStatus('closed');
    };
  }, [enabled, userId, organizationId]); // 直接依赖这些值，而不是 connect 函数

  return status;
}
