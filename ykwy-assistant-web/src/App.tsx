import { Navigate, Route, Routes } from 'react-router-dom';

import AuthGuard from './components/auth/AuthGuard';
import AuthLayout from './components/auth/Layout';
import ProtectedRoute from './components/auth/ProtectedRoute';
import Layout from './components/main/Layout';
import LoginPage from './pages/auth/LoginPage';
import RegisterPage from './pages/auth/RegisterPage';
import { ConnectionManagePage } from './pages/main/ConnectionManagePage';
import ConversationManagePage from './pages/main/ConversationManagePage';
import SettingsPage from './pages/main/SettingsPage';

function App() {
  return (
    <Routes>
      <Route
        path="/auth"
        element={
          <AuthGuard>
            <AuthLayout />
          </AuthGuard>
        }
      >
        <Route path="login" element={<LoginPage />} />
        <Route path="register" element={<RegisterPage />} />
        <Route path="*" element={<Navigate to="/auth/login" replace />} />
      </Route>
      <Route
        path="/"
        element={
          <ProtectedRoute>
            <Layout />
          </ProtectedRoute>
        }
      >
        <Route index element={<ConversationManagePage />} />
        <Route path="conversations" element={<ConversationManagePage />} />
        <Route path="connections" element={<ConnectionManagePage />} />

        <Route path="settings" element={<SettingsPage />} />
        <Route path="*" element={<Navigate to="/" replace />} />
      </Route>
    </Routes>
  );
}

export default App;
