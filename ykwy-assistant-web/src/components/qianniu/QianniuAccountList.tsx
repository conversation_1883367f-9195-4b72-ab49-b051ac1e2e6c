import { Edit, Plus, Store, Trash2, User, Wifi, WifiOff } from 'lucide-react';
import { useState } from 'react';

import { useDeleteQianniuAccount, useQianniuAccounts } from '../../services/hooks';
import type { QianniuAccount, QianniuClient } from '../../services/types';
import { QianniuAccountForm } from './QianniuAccountForm';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

interface QianniuAccountListProps {
  client: QianniuClient;
  onClose: () => void;
}

export function QianniuAccountList({ client, onClose }: QianniuAccountListProps) {
  const [editingAccount, setEditingAccount] = useState<QianniuAccount | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  // 获取客户端账号列表
  const { data: accountsResponse, isLoading } = useQianniuAccounts(client.id);
  const accounts = accountsResponse?.data || [];

  // 删除账号
  const deleteAccountMutation = useDeleteQianniuAccount();

  const handleDeleteAccount = async (account: QianniuAccount) => {
    if (confirm(`确定要删除账号 "${account.accountName}" 吗？此操作不可撤销。`)) {
      try {
        await deleteAccountMutation.mutateAsync(account.id);
      } catch (error) {
        console.error('删除账号失败:', error);
      }
    }
  };

  const handleEditAccount = (account: QianniuAccount) => {
    setEditingAccount(account);
    setIsEditDialogOpen(true);
  };

  const handleCreateSuccess = () => {
    setIsCreateDialogOpen(false);
  };

  const handleEditSuccess = () => {
    setIsEditDialogOpen(false);
    setEditingAccount(null);
  };

  const getPlatformBadgeColor = (platformType: string) => {
    switch (platformType) {
      case 'TAOBAO':
        return 'bg-orange-100 text-orange-800';
      case 'TMALL':
        return 'bg-red-100 text-red-800';
      case 'JD':
        return 'bg-red-100 text-red-800';
      case 'PINDUODUO':
        return 'bg-orange-100 text-orange-800';
      case 'WECHAT':
        return 'bg-green-100 text-green-800';
      case 'DOUYIN':
        return 'bg-black text-white';
      case 'XIAOHONGSHU':
        return 'bg-pink-100 text-pink-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPlatformName = (platformType: string) => {
    switch (platformType) {
      case 'TAOBAO':
        return '淘宝';
      case 'TMALL':
        return '天猫';
      case 'JD':
        return '京东';
      case 'PINDUODUO':
        return '拼多多';
      case 'WECHAT':
        return '微信';
      case 'DOUYIN':
        return '抖音';
      case 'XIAOHONGSHU':
        return '小红书';
      default:
        return '其他';
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Store className="w-5 h-5" />
            {client.name} - 账号管理
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* 添加账号按钮 */}
          <div className="flex justify-between items-center">
            <p className="text-sm text-muted-foreground">管理该客户端下的千牛账号，每个账号对应一个品牌和平台</p>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  添加账号
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>添加千牛账号</DialogTitle>
                </DialogHeader>
                <QianniuAccountForm clientId={client.id} onSuccess={handleCreateSuccess} onCancel={() => setIsCreateDialogOpen(false)} />
              </DialogContent>
            </Dialog>
          </div>

          {/* 账号列表 */}
          {isLoading ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">加载中...</p>
            </div>
          ) : accounts.length === 0 ? (
            <div className="text-center py-8">
              <Store className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground">暂无账号</p>
              <p className="text-sm text-muted-foreground mt-2">点击上方"添加账号"按钮创建第一个账号</p>
            </div>
          ) : (
            <div className="grid gap-4">
              {accounts.map((account) => (
                <Card key={account.id}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                          <User className="w-4 h-4 text-muted-foreground" />
                          <span className="font-medium">{account.accountName}</span>
                        </div>

                        {account.shopName && (
                          <div className="flex items-center gap-2">
                            <Store className="w-4 h-4 text-muted-foreground" />
                            <span className="text-sm text-muted-foreground">{account.shopName}</span>
                          </div>
                        )}

                        <Badge className={getPlatformBadgeColor(account.platformType)}>{getPlatformName(account.platformType)}</Badge>

                        <div className="flex items-center gap-1">
                          {account.isLoggedIn ? <Wifi className="w-4 h-4 text-green-500" /> : <WifiOff className="w-4 h-4 text-gray-400" />}
                          <span className={`text-xs ${account.isLoggedIn ? 'text-green-600' : 'text-gray-500'}`}>{account.isLoggedIn ? '已登录' : '未登录'}</span>
                        </div>

                        {account.brand && <Badge variant="outline">{account.brand.name}</Badge>}
                      </div>

                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm" onClick={() => handleEditAccount(account)}>
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => handleDeleteAccount(account)} disabled={deleteAccountMutation.isPending}>
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>

                    {account.lastLoginAt && <div className="mt-2 text-xs text-muted-foreground">最后登录: {new Date(account.lastLoginAt).toLocaleString()}</div>}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>

        {/* 编辑账号对话框 */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>编辑千牛账号</DialogTitle>
            </DialogHeader>
            {editingAccount && <QianniuAccountForm clientId={client.id} account={editingAccount} onSuccess={handleEditSuccess} onCancel={() => setIsEditDialogOpen(false)} />}
          </DialogContent>
        </Dialog>
      </DialogContent>
    </Dialog>
  );
}
