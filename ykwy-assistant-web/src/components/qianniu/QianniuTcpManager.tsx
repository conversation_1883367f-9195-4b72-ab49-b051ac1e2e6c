import { Alert<PERSON>ircle, CheckCircle, Link, RefreshCw, Send, Unlink } from 'lucide-react';
import { useState } from 'react';

import { useDeleteTcpConnection, useRegisterTcpConnection, useTcpConnections, useTestSendMessage } from '../../services/hooks/useQianniuTcp';
import type { RegisterTcpConnectionRequest, TestSendRequest } from '../../services/types';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

export function QianniuTcpManager() {
  const [registerForm, setRegisterForm] = useState<RegisterTcpConnectionRequest>({
    connectionId: '',
    tcpClientId: '',
  });
  const [testForm, setTestForm] = useState<TestSendRequest>({
    connectionId: '',
    targetId: '',
    message: '测试消息',
  });

  // 使用service hooks
  const { data: connectionsInfo, isLoading, refetch } = useTcpConnections();
  const registerMutation = useRegisterTcpConnection();
  const deleteMutation = useDeleteTcpConnection();
  const testSendMutation = useTestSendMessage();

  // 注册成功后重置表单
  const handleRegister = async () => {
    await registerMutation.mutateAsync(registerForm);
    setRegisterForm({ connectionId: '', tcpClientId: '' });
  };

  if (isLoading) {
    return <div className="flex items-center justify-center p-8">加载中...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">千牛TCP连接管理</h2>
          <p className="text-muted-foreground">管理千牛TCP连接与WebSocket连接的映射关系</p>
        </div>
        <Button onClick={() => refetch()} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          刷新
        </Button>
      </div>

      {/* 连接状态概览 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">TCP连接数</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{connectionsInfo?.totalConnections || 0}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">已映射连接</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Object.keys(connectionsInfo?.connectionMappings || {}).length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">未映射连接</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{connectionsInfo?.unmappedTcpConnections?.length || 0}</div>
          </CardContent>
        </Card>
      </div>

      {/* 在线客户端列表 */}
      <Card>
        <CardHeader>
          <CardTitle>在线客户端</CardTitle>
          <CardDescription>当前在线的千牛客户端及其TCP连接状态</CardDescription>
        </CardHeader>
        <CardContent>
          {connectionsInfo?.tcpConnections?.length === 0 ? (
            <p className="text-muted-foreground">暂无TCP连接</p>
          ) : (
            <div className="space-y-3">
              {connectionsInfo?.tcpConnections?.map((tcpClientId) => {
                const mappedConnectionId = Object.entries(connectionsInfo?.connectionMappings || {}).find(([, clientId]) => clientId === tcpClientId)?.[0];
                const isMapped = !!mappedConnectionId;

                return (
                  <div key={tcpClientId} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">TCP连接 {tcpClientId}</span>
                        <Badge variant={isMapped ? 'default' : 'destructive'}>{isMapped ? '已映射' : '未映射'}</Badge>
                      </div>
                      <div className="text-sm text-muted-foreground mt-1">{isMapped && <span>映射到: {mappedConnectionId}</span>}</div>
                    </div>
                    <div className="flex gap-2">
                      {isMapped ? (
                        <Button variant="outline" size="sm" onClick={() => deleteMutation.mutate(mappedConnectionId)} disabled={deleteMutation.isPending}>
                          <Unlink className="h-4 w-4 mr-1" />
                          取消映射
                        </Button>
                      ) : (
                        <Button variant="outline" size="sm" onClick={() => setRegisterForm({ connectionId: '', tcpClientId })}>
                          <Link className="h-4 w-4 mr-1" />
                          建立映射
                        </Button>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 手动注册连接映射 */}
      <Card>
        <CardHeader>
          <CardTitle>手动注册连接映射</CardTitle>
          <CardDescription>为WebSocket连接ID手动指定对应的TCP连接</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="connectionId">连接ID (WebSocket)</Label>
              <Input id="connectionId" value={registerForm.connectionId} onChange={(e) => setRegisterForm((prev) => ({ ...prev, connectionId: e.target.value }))} placeholder="输入WebSocket连接ID" />
            </div>
            <div>
              <Label htmlFor="clientId">客户端ID (可选)</Label>
              <Input id="clientId" value={registerForm.tcpClientId || ''} onChange={(e) => setRegisterForm((prev) => ({ ...prev, tcpClientId: e.target.value }))} placeholder="留空自动选择" />
            </div>
          </div>
          <Button onClick={handleRegister} disabled={!registerForm.connectionId || registerMutation.isPending}>
            <Link className="h-4 w-4 mr-2" />
            注册映射
          </Button>
          {registerMutation.error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{registerMutation.error.message}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* 测试消息发送 */}
      <Card>
        <CardHeader>
          <CardTitle>测试消息发送</CardTitle>
          <CardDescription>测试通过TCP连接发送消息</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="testConnectionId">连接ID</Label>
              <Input id="testConnectionId" value={testForm.connectionId} onChange={(e) => setTestForm((prev) => ({ ...prev, connectionId: e.target.value }))} placeholder="输入连接ID" />
            </div>
            <div>
              <Label htmlFor="targetId">目标ID</Label>
              <Input id="targetId" value={testForm.targetId} onChange={(e) => setTestForm((prev) => ({ ...prev, targetId: e.target.value }))} placeholder="输入目标客户ID" />
            </div>
          </div>
          <div>
            <Label htmlFor="message">消息内容</Label>
            <Textarea id="message" value={testForm.message} onChange={(e) => setTestForm((prev) => ({ ...prev, message: e.target.value }))} placeholder="输入测试消息" rows={3} />
          </div>
          <Button onClick={() => testSendMutation.mutate(testForm)} disabled={!testForm.connectionId || !testForm.targetId || !testForm.message || testSendMutation.isPending}>
            <Send className="h-4 w-4 mr-2" />
            发送测试消息
          </Button>
          {testSendMutation.data && (
            <Alert variant={testSendMutation.data.data.success ? 'default' : 'destructive'}>
              {testSendMutation.data.data.success ? <CheckCircle className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
              <AlertDescription>{testSendMutation.data.message}</AlertDescription>
            </Alert>
          )}
          {testSendMutation.error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{testSendMutation.error.message}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* 未映射的TCP连接 */}
      {(connectionsInfo?.unmappedTcpConnections?.length || 0) > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>未映射的TCP连接</CardTitle>
            <CardDescription>这些TCP连接尚未与WebSocket连接建立映射</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {connectionsInfo?.unmappedTcpConnections?.map((tcpClientId) => (
                <div key={tcpClientId} className="flex items-center justify-between p-2 border rounded">
                  <span className="font-mono text-sm">{tcpClientId}</span>
                  <Badge variant="outline">未映射</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
