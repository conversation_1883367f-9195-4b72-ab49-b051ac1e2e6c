import { Activity, AlertCircle, CheckCircle, Clock, MessageSquare, Monitor, RefreshCw, Users, WifiOff } from 'lucide-react';
import { useState } from 'react';

import { useActiveConnections, useConnectionStats, useMessageStats } from '../../services/hooks/useQianniuMonitor';
import type { QianniuConnection } from '../../services/types';
import { QianniuTcpManager } from './QianniuTcpManager';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export function QianniuMonitorDashboard() {
  const [refreshInterval] = useState(30000); // 30秒刷新

  // 使用service hooks获取数据
  const { data: connectionStats, refetch: refetchStats } = useConnectionStats(refreshInterval);
  const { data: activeConnections, refetch: refetchConnections } = useActiveConnections(refreshInterval);
  const { data: messageStats, refetch: refetchMessages } = useMessageStats(refreshInterval);

  const handleRefresh = () => {
    refetchStats();
    refetchConnections();
    refetchMessages();
  };

  const getConnectionStatus = (connection: QianniuConnection) => {
    if (!connection.lastActivity) return 'unknown';

    const lastActivity = new Date(connection.lastActivity);
    const now = new Date();
    const timeDiff = now.getTime() - lastActivity.getTime();

    if (timeDiff < 60000) return 'healthy'; // 1分钟内
    if (timeDiff < 300000) return 'warning'; // 5分钟内
    return 'error'; // 超过5分钟
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'warning':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <WifiOff className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'healthy':
        return <Badge className="bg-green-500">正常</Badge>;
      case 'warning':
        return <Badge className="bg-yellow-500">警告</Badge>;
      case 'error':
        return <Badge className="bg-red-500">异常</Badge>;
      default:
        return <Badge variant="secondary">未知</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* 头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">千牛监控面板</h2>
          <p className="text-muted-foreground">实时监控千牛客户端连接状态和消息流</p>
        </div>
        <Button onClick={handleRefresh} variant="outline">
          <RefreshCw className="w-4 h-4 mr-2" />
          刷新
        </Button>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总连接数</CardTitle>
            <Monitor className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{connectionStats?.realTime?.qianniuConnections || 0}</div>
            <p className="text-xs text-muted-foreground">千牛客户端连接</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活跃团队</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Object.keys(connectionStats?.websocket?.connectionsByTeam || {}).length}</div>
            <p className="text-xs text-muted-foreground">有连接的团队数</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">今日消息</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{messageStats?.overview?.today || 0}</div>
            <p className="text-xs text-muted-foreground">过去24小时</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">系统状态</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-500">正常</div>
            <p className="text-xs text-muted-foreground">所有服务运行正常</p>
          </CardContent>
        </Card>
      </div>

      {/* 详细信息 */}
      <Tabs defaultValue="connections" className="space-y-4">
        <TabsList>
          <TabsTrigger value="connections">活跃连接</TabsTrigger>
          <TabsTrigger value="tcp">TCP管理</TabsTrigger>
          <TabsTrigger value="teams">团队统计</TabsTrigger>
          <TabsTrigger value="brands">品牌统计</TabsTrigger>
          <TabsTrigger value="messages">消息统计</TabsTrigger>
        </TabsList>

        <TabsContent value="connections" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>活跃连接列表</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {activeConnections?.map((connection) => {
                  const status = getConnectionStatus(connection);
                  return (
                    <div key={connection.connectionId} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        {getStatusIcon(status)}
                        <div>
                          <div className="font-medium">客户端 {connection.clientId}</div>
                          <div className="text-sm text-muted-foreground">连接时间: {new Date(connection.connectedAt).toLocaleString()}</div>
                          <div className="text-sm text-muted-foreground">最后活动: {new Date(connection.lastActivity).toLocaleString()}</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="text-right">
                          <div className="text-sm font-medium">{connection.client?.name || '未知客户端'}</div>
                          <div className="text-sm text-muted-foreground">团队: {connection.client?.team?.name || connection.teamId}</div>
                        </div>
                        {getStatusBadge(status)}
                      </div>
                    </div>
                  );
                })}

                {(!activeConnections || activeConnections.length === 0) && <div className="text-center py-8 text-muted-foreground">暂无活跃连接</div>}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tcp" className="space-y-4">
          <QianniuTcpManager />
        </TabsContent>

        <TabsContent value="teams" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>团队连接统计</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {Object.entries(connectionStats?.websocket?.connectionsByTeam || {}).map(([teamId, count]) => (
                  <div key={teamId} className="flex items-center justify-between">
                    <span>团队 {teamId}</span>
                    <Badge variant="outline">{count as number} 个连接</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="brands" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>组织客户端统计</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {Object.entries(connectionStats?.database?.byOrganization || {}).map(([orgId, stats]) => (
                  <div key={orgId} className="flex items-center justify-between">
                    <span>组织 {orgId.slice(0, 8)}...</span>
                    <Badge variant="outline">{stats.total} 个客户端</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="messages" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>消息流统计</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <h4 className="font-medium mb-2">时间统计</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>今日</span>
                      <span>{messageStats?.overview?.today || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>本周</span>
                      <span>{messageStats?.overview?.week || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>本月</span>
                      <span>{messageStats?.overview?.month || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>总计</span>
                      <span>{messageStats?.overview?.total || 0}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">发送者类型分布</h4>
                  <div className="space-y-2">
                    {Object.entries(messageStats?.bySenderType || {}).map(([senderType, count]) => (
                      <div key={senderType} className="flex justify-between">
                        <span>{senderType === 'CUSTOMER' ? '客户' : '客服'}</span>
                        <span>{count as number}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">消息类型分布</h4>
                  <div className="space-y-2">
                    {Object.entries(messageStats?.byMessageType || {}).map(([messageType, count]) => (
                      <div key={messageType} className="flex justify-between">
                        <span>{messageType}</span>
                        <span>{count as number}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
