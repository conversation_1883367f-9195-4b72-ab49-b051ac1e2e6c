import { CheckCircle } from 'lucide-react';

interface SuccessAlertProps {
  title: string;
  message: string;
  action?: {
    text: string;
    onClick: () => void;
  };
}

export default function SuccessAlert({ title, message, action }: SuccessAlertProps) {
  return (
    <div className="rounded-lg bg-green-50 p-4 border border-green-200">
      <div className="flex">
        <div className="flex-shrink-0">
          <CheckCircle className="h-5 w-5 text-green-400" />
        </div>
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium text-green-800">{title}</h3>
          <div className="mt-1 text-sm text-green-700">{message}</div>
          {action && (
            <div className="mt-3">
              <button
                type="button"
                onClick={action.onClick}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                {action.text}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
