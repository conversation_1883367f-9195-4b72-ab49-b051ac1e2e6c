import { MessageCircle, Settings, Wifi } from 'lucide-react';
import { Link, useLocation } from 'react-router-dom';

import { useAppStore } from '../../lib/store';

import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';

const menuItems = [
  {
    path: '/conversations',
    label: '对话管理',
    icon: MessageCircle,
    active: ['/', '/conversations'],
  },
  {
    path: '/connections',
    label: '连接管理',
    icon: Wifi,
  },

  {
    path: '/settings',
    label: '系统设置',
    icon: Settings,
  },
];

interface SidebarProps {
  isSmallScreen?: boolean;
}

export default function Sidebar({ isSmallScreen = false }: SidebarProps) {
  const location = useLocation();
  const sidebarCollapsed = useAppStore((state) => state.sidebarCollapsed);
  const isMobileView = useAppStore((state) => state.isMobileView);
  const mobileSidebarOpen = useAppStore((state) => state.mobileSidebarOpen);
  const closeMobileSidebar = useAppStore((state) => state.closeMobileSidebar);

  // 获取未读会话总数
  const totalUnreadConversations = useAppStore((state) => state.getTotalUnreadConversations());

  // 根据屏幕尺寸和侧边栏状态决定侧边栏的样式
  const sidebarClasses = cn(
    'fixed top-16 h-[calc(100vh-4rem)] bg-white border-r border-gray-200 overflow-y-auto transition-all duration-300 ease-in-out z-50',
    // 大屏幕桌面视图
    !isMobileView && !isSmallScreen && (sidebarCollapsed ? 'w-16' : 'w-64'),
    // 小屏幕PC视图 - 永久折叠状态
    isSmallScreen && 'w-16',
    // 移动视图
    isMobileView && (mobileSidebarOpen ? 'left-0 w-64' : '-left-64 w-64'),
  );

  // 处理在移动设备上点击菜单项时关闭侧边栏
  const handleMenuItemClick = () => {
    if (isMobileView) {
      closeMobileSidebar();
    }
  };

  // 决定是否显示工具提示（在侧边栏折叠或小屏幕PC模式下）
  const shouldShowTooltip = (!isMobileView && sidebarCollapsed) || isSmallScreen;

  return (
    <aside className={sidebarClasses}>
      <nav className="p-4">
        <ul className="space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = item.active ? item.active.includes(location.pathname) : location.pathname === item.path;

            // 对于对话管理菜单项，显示未读数量徽章
            const showBadge = item.path === '/conversations' && totalUnreadConversations > 0 && !isActive;

            const menuItem = (
              <Link
                to={item.path}
                onClick={handleMenuItemClick}
                className={cn(
                  'flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors',
                  isActive ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900',
                  shouldShowTooltip && 'justify-center',
                )}
              >
                <div className="relative px-2">
                  <Icon className="w-5 h-5" />
                  {showBadge && (
                    <span className="absolute -top-1 -right-1 inline-flex items-center justify-center w-4 h-4 text-xs font-bold text-white bg-red-500 rounded-full">
                      {totalUnreadConversations > 9 ? '9+' : totalUnreadConversations}
                    </span>
                  )}
                </div>
                <span className={cn('transition-all duration-300 ease-in-out whitespace-nowrap overflow-hidden', shouldShowTooltip ? 'w-0 opacity-0' : 'w-auto opacity-100')}>{item.label}</span>
              </Link>
            );

            return (
              <li key={item.path}>
                {shouldShowTooltip ? (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>{menuItem}</TooltipTrigger>
                      <TooltipContent side="right" align="center">
                        {item.label}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                ) : (
                  menuItem
                )}
              </li>
            );
          })}
        </ul>
      </nav>
    </aside>
  );
}
