import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { useDebounce } from '../../../hooks/useDebounce';
import { useOrganization } from '../../../hooks/useOrganization';
import { useConversations } from '../../../services';
import ConversationItem from './ConversationItem';

interface ConversationListProps {
  onSelectConversation: (_id: string) => void;
  selectedConversationId?: string;
}

export default function ConversationList({ onSelectConversation, selectedConversationId }: ConversationListProps) {
  const [filters, setFilters] = useState({});
  const [page, setPage] = useState(1);
  const [searchText, setSearchText] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);
  const [isSearchFocused, setIsSearchFocused] = useState(false);

  // 使用防抖钩子处理搜索文本
  const debouncedSearchText = useDebounce(searchText, 500);

  const handleFilterChange = useCallback((newFilters: Record<string, unknown>) => {
    setFilters(newFilters);
    setPage(1);
  }, []);

  // 当防抖后的搜索文本变化时，才更新过滤器
  useEffect(() => {
    if (debouncedSearchText !== undefined) {
      handleFilterChange({ search: debouncedSearchText });
    }
  }, [debouncedSearchText, handleFilterChange]);

  // 构建查询参数
  const queryFilters = useMemo(
    () => ({
      ...filters,
    }),
    [filters],
  );

  const { hasOrganization } = useOrganization();
  const { data, isLoading, error } = useConversations(queryFilters, page);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchText(e.target.value);
  };

  // 处理搜索框焦点
  const handleSearchFocus = () => {
    setIsSearchFocused(true);
    const input = inputRef.current;
    if (input) {
      const len = input.value.length;
      setTimeout(() => {
        input.setSelectionRange(len, len);
      }, 0);
    }
  };

  const handleSearchBlur = () => {
    setIsSearchFocused(false);
  };

  // 防止因为搜索结果变化导致的焦点丢失
  useEffect(() => {
    if (isSearchFocused && inputRef.current && document.activeElement !== inputRef.current) {
      // 使用 requestAnimationFrame 确保在下一帧重新获得焦点
      requestAnimationFrame(() => {
        if (inputRef.current && isSearchFocused) {
          inputRef.current.focus();
          // 恢复光标位置
          const len = inputRef.current.value.length;
          inputRef.current.setSelectionRange(len, len);
        }
      });
    }
  }, [data, isSearchFocused]);

  // 防止在加载状态和错误状态时丢失焦点
  useEffect(() => {
    if (isSearchFocused && inputRef.current) {
      const preserveFocus = () => {
        if (inputRef.current && isSearchFocused && document.activeElement !== inputRef.current) {
          inputRef.current.focus();
        }
      };

      // 在状态变化时保持焦点
      const timeoutId = setTimeout(preserveFocus, 0);
      return () => clearTimeout(timeoutId);
    }
  }, [isLoading, error, isSearchFocused]);

  const handleNextPage = useCallback(() => {
    if (data && page < data.totalPages) {
      setPage(page + 1);
    }
  }, [data, page]);

  const handlePrevPage = useCallback(() => {
    if (page > 1) {
      setPage(page - 1);
    }
  }, [page]);

  // 如果用户没有加入任何组织，显示提示信息
  if (!hasOrganization) {
    return (
      <div className="p-4 text-center text-amber-600">
        <p>您当前未加入任何组织</p>
        <p className="text-sm mt-2">请联系管理员邀请您加入组织</p>
      </div>
    );
  }

  // 如果对话数据还在加载中，显示加载状态
  if (isLoading) {
    return <div className="p-4 text-center">加载对话列表...</div>;
  }

  if (error) {
    return <div className="p-4 text-center text-red-500">加载失败</div>;
  }

  return (
    <div className="h-full flex flex-col">
      <div className="p-3 border-b">
        <input
          ref={inputRef}
          type="text"
          placeholder="搜索对话..."
          className="w-full px-3 py-2 border rounded-md"
          value={searchText}
          onChange={handleSearchChange}
          onFocus={handleSearchFocus}
          onBlur={handleSearchBlur}
        />
      </div>

      <div className="flex-1 overflow-y-auto">
        {data?.data.length === 0 ? (
          <div className="p-4 text-center text-gray-500">暂无对话</div>
        ) : (
          data?.data.map((conversation) => (
            <ConversationItem key={conversation.id} conversation={conversation} isSelected={selectedConversationId === conversation.id} onClick={() => onSelectConversation(conversation.id)} />
          ))
        )}
      </div>

      {data && data.totalPages > 1 && (
        <div className="p-3 border-t flex justify-between items-center">
          <button onClick={handlePrevPage} disabled={page === 1} className="px-3 py-1 bg-gray-200 rounded disabled:opacity-50">
            上一页
          </button>
          <span>
            {page} / {data.totalPages}
          </span>
          <button onClick={handleNextPage} disabled={page >= data.totalPages} className="px-3 py-1 bg-gray-200 rounded disabled:opacity-50">
            下一页
          </button>
        </div>
      )}
    </div>
  );
}
