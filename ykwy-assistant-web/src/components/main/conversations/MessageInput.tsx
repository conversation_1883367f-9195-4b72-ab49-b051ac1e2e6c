import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

import { LoadingSpinner } from '../../common/LoadingStates';

interface MessageInputProps {
  onSendMessage: (content: string) => void;
  disabled?: boolean;
  loading?: boolean;
  placeholder?: string;
  onInputChange?: (content: string) => void;
}

export interface MessageInputRef {
  setMessage: (content: string) => void;
  focus: () => void;
  blur: () => void;
}

const MessageInput = forwardRef<MessageInputRef, MessageInputProps>(({ onSendMessage, disabled = false, loading = false, placeholder = '输入消息...', onInputChange }, ref) => {
  const [message, setMessage] = useState('');
  const textareaRef = React.useRef<HTMLTextAreaElement>(null);
  const [isSmallScreen, setIsSmallScreen] = useState(false);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      // 768px 以上 1024px 以下为小屏幕PC
      setIsSmallScreen(window.innerWidth >= 768 && window.innerWidth < 1024);
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 暴露textarea引用和setMessage方法给父组件
  useImperativeHandle(ref, () => ({
    setMessage: (content: string) => {
      setMessage(content);
      // 通知父组件输入内容变化
      onInputChange?.(content);
      // 同时更新textarea的高度
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
        textareaRef.current.style.height = Math.min(textareaRef.current.scrollHeight, 120) + 'px';
      }
    },
    focus: () => textareaRef.current?.focus(),
    blur: () => textareaRef.current?.blur(),
  }));

  const isDisabled = disabled || loading;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!message.trim() || isDisabled) return;

    onSendMessage(message);
    setMessage('');

    // 重置textarea高度
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // 检查是否正在使用输入法组合文字（如中文输入法）
    // 如果正在组合，不应该触发提交操作
    if (e.key === 'Enter' && !e.shiftKey && !e.nativeEvent.isComposing) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setMessage(newValue);

    // 通知父组件输入内容变化
    onInputChange?.(newValue);

    // 自动调整高度
    const textarea = e.target;
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, isSmallScreen ? 80 : 120) + 'px';
  };

  const getPlaceholder = () => {
    if (loading) return '发送中...';
    if (disabled) return '对话已锁定，无法发送消息';
    return placeholder;
  };

  return (
    <form onSubmit={handleSubmit} className="flex flex-col space-y-3">
      <div className="relative">
        <textarea
          ref={textareaRef}
          value={message}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder={getPlaceholder()}
          disabled={isDisabled}
          className={`w-full ${isSmallScreen ? 'p-2 pr-10' : 'p-3 pr-12'} border rounded-lg resize-none transition-all duration-200 min-h-[44px] ${isSmallScreen ? 'max-h-[80px]' : 'max-h-[120px]'} ${
            isDisabled ? 'bg-gray-50 text-gray-500 cursor-not-allowed' : 'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
          }`}
          rows={1}
        />

        {/* 发送按钮 */}
        <button
          type="submit"
          disabled={!message.trim() || isDisabled}
          className={`absolute ${isSmallScreen ? 'right-1 bottom-1 p-1.5' : 'right-2 bottom-2 p-2'} rounded-md transition-all duration-200 ${
            !message.trim() || isDisabled ? 'text-gray-400 cursor-not-allowed' : 'text-blue-500 hover:bg-blue-50 hover:text-blue-600'
          }`}
        >
          {loading ? (
            <LoadingSpinner size="sm" />
          ) : (
            <svg className={`${isSmallScreen ? 'w-4 h-4' : 'w-5 h-5'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
          )}
        </button>
      </div>

      {/* 快捷提示 */}
      <div className="flex items-center justify-between text-xs text-gray-500">
        <span>{isSmallScreen ? 'Enter 发送' : '按 Enter 发送，Shift + Enter 换行'}</span>
        <span className={message.length > 500 ? 'text-red-500' : ''}>{message.length}/1000</span>
      </div>
    </form>
  );
});

MessageInput.displayName = 'MessageInput';

export default MessageInput;
