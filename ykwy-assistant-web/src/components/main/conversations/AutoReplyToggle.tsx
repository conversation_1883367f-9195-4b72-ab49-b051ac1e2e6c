import { <PERSON><PERSON>, BotOff } from 'lucide-react';

import { useConversationAutoReplyStatus, useSetConversationAutoReply } from '../../../services/hooks/useConversationAutoReply';

import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface AutoReplyToggleProps {
  conversationId: string;
  variant?: 'switch' | 'button'; // 显示样式：开关或按钮
  size?: 'sm' | 'lg' | 'default' | 'icon';
  showLabel?: boolean;
}

export default function AutoReplyToggle({ conversationId, variant = 'switch', size = 'default', showLabel = true }: AutoReplyToggleProps) {
  const { data: autoReplyStatus, isLoading } = useConversationAutoReplyStatus(conversationId);
  const setAutoReplyMutation = useSetConversationAutoReply();

  // 只有在真正加载完成后才使用数据，否则不显示默认值
  const isEnabled = isLoading ? false : (autoReplyStatus?.data?.autoReplyEnabled ?? true);
  const isUpdating = setAutoReplyMutation.isPending;

  console.log(`[AutoReplyToggle] Status:`, {
    conversationId,
    autoReplyStatus,
    isEnabled,
    isUpdating,
    isLoading,
  });

  const handleToggle = async () => {
    if (isUpdating) return;

    console.log(`[AutoReplyToggle] Toggling from ${isEnabled} to ${!isEnabled}`);

    try {
      const result = await setAutoReplyMutation.mutateAsync({
        conversationId,
        enabled: !isEnabled,
      });
      console.log(`[AutoReplyToggle] Toggle result:`, result);
    } catch (error) {
      console.error('Failed to toggle auto reply:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center gap-2">
        <div className="w-4 h-4 bg-gray-200 rounded animate-pulse" />
        {showLabel && <div className="w-16 h-4 bg-gray-200 rounded animate-pulse" />}
      </div>
    );
  }

  if (variant === 'button') {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant={isEnabled ? 'default' : 'outline'}
              size={size}
              onClick={handleToggle}
              disabled={isUpdating}
              className={`flex items-center gap-2 ${isEnabled ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-600'}`}
            >
              {isEnabled ? <Bot className="w-4 h-4" /> : <BotOff className="w-4 h-4" />}
              {showLabel && <span className="text-sm">{isEnabled ? 'AI自动回复' : '手动回复'}</span>}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>{isEnabled ? '点击关闭AI自动回复，改为手动回复' : '点击开启AI自动回复'}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return (
    <div className="flex items-center gap-2">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center gap-2">
              {isEnabled ? <Bot className="w-4 h-4 text-green-600" /> : <BotOff className="w-4 h-4 text-gray-400" />}
              <Switch checked={isEnabled} onCheckedChange={handleToggle} disabled={isUpdating} className="data-[state=checked]:bg-green-600" />
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>{isEnabled ? 'AI自动回复已开启，点击关闭' : 'AI自动回复已关闭，点击开启'}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      {showLabel && <span className={`text-sm ${isEnabled ? 'text-green-600' : 'text-gray-500'}`}>{isEnabled ? 'AI自动回复' : '手动回复'}</span>}
    </div>
  );
}
