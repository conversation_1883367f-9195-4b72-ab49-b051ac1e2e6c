import { AlertCircle, RefreshCw, WifiOff } from 'lucide-react';
import { ReactNode } from 'react';

import { ApiError } from '../../lib/api-client';

import { Button } from '@/components/ui/button';

interface QueryErrorBoundaryProps {
  error: Error | ApiError | null;
  onRetry?: () => void;
  children: ReactNode;
  fallback?: ReactNode;
}

export default function QueryErrorBoundary({ error, onRetry, children, fallback }: QueryErrorBoundaryProps) {
  if (!error) {
    return <>{children}</>;
  }

  if (fallback) {
    return <>{fallback}</>;
  }

  const isApiError = error instanceof ApiError;
  const isNetworkError = isApiError && error.isNetworkError;
  const isClientError = isApiError && error.isClientError;
  const isServerError = isApiError && error.isServerError;

  const getErrorIcon = () => {
    if (isNetworkError) return <WifiOff className="w-8 h-8 text-red-500" />;
    if (isServerError) return <AlertCircle className="w-8 h-8 text-red-500" />;
    return <AlertCircle className="w-8 h-8 text-yellow-500" />;
  };

  const getErrorTitle = () => {
    if (isNetworkError) return '网络连接失败';
    if (isApiError && error.status === 404) return '数据不存在';
    if (isApiError && error.status === 403) return '没有访问权限';
    if (isApiError && error.status === 401) return '请先登录';
    if (isServerError) return '服务器错误';
    return '加载失败';
  };

  const getErrorMessage = () => {
    if (isNetworkError) return '请检查网络连接后重试';
    if (isApiError && error.status === 404) return '请求的数据不存在或已被删除';
    if (isApiError && error.status === 403) return '您没有权限访问此内容';
    if (isApiError && error.status === 401) return '登录已过期，请重新登录';
    if (isServerError) return '服务器暂时无法响应，请稍后重试';
    return error.message || '发生了未知错误';
  };

  const shouldShowRetry = () => {
    // 客户端错误通常不需要重试按钮
    return !isClientError || isNetworkError;
  };

  return (
    <div className="flex flex-col items-center justify-center py-12 px-4 text-center">
      <div className="mb-4">{getErrorIcon()}</div>

      <h3 className="text-lg font-semibold text-gray-900 mb-2">{getErrorTitle()}</h3>

      <p className="text-sm text-gray-600 mb-6 max-w-md">{getErrorMessage()}</p>

      {shouldShowRetry() && onRetry && (
        <Button onClick={onRetry} variant="outline" className="flex items-center gap-2">
          <RefreshCw className="w-4 h-4" />
          重试
        </Button>
      )}

      {/* 开发环境显示详细错误信息 */}
      {import.meta.env.DEV && (
        <details className="mt-6 text-left">
          <summary className="text-xs text-gray-400 cursor-pointer">开发调试信息</summary>
          <pre className="mt-2 text-xs text-gray-500 bg-gray-100 p-2 rounded overflow-auto max-w-md">
            {JSON.stringify(
              {
                message: error.message,
                status: isApiError ? error.status : undefined,
                code: isApiError ? error.code : undefined,
                stack: error.stack?.split('\n').slice(0, 5).join('\n'),
              },
              null,
              2,
            )}
          </pre>
        </details>
      )}
    </div>
  );
}

// 简化版本的错误边界，用于内联显示
interface InlineErrorProps {
  error: Error | ApiError | null;
  onRetry?: () => void;
  size?: 'sm' | 'md';
}

export function InlineError({ error, onRetry, size = 'md' }: InlineErrorProps) {
  if (!error) return null;

  const isApiError = error instanceof ApiError;
  const isNetworkError = isApiError && error.isNetworkError;

  const sizeClasses = {
    sm: 'text-xs py-2 px-3',
    md: 'text-sm py-3 px-4',
  };

  return (
    <div className={`bg-red-50 border border-red-200 rounded-md ${sizeClasses[size]}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {isNetworkError ? <WifiOff className="w-4 h-4 text-red-500 flex-shrink-0" /> : <AlertCircle className="w-4 h-4 text-red-500 flex-shrink-0" />}
          <span className="text-red-700">{isNetworkError ? '网络错误' : error.message}</span>
        </div>

        {onRetry && (
          <Button size="sm" variant="ghost" onClick={onRetry} className="text-red-600 hover:text-red-700 h-auto p-1">
            <RefreshCw className="w-3 h-3" />
          </Button>
        )}
      </div>
    </div>
  );
}
