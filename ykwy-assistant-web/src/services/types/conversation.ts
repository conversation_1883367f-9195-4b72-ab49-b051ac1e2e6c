// 对话相关类型定义

import type { Message } from './message';

export interface Conversation {
  id: string;
  customerId: string;
  platformId: string;
  title?: string;
  status: 'PENDING' | 'IN_PROGRESS' | 'WAITING' | 'RESOLVED' | 'CLOSED';
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT';
  lastActivity: string;
  autoReplyEnabled: boolean;
  assignedUserId?: string;
  tags: string[];
  customer?: {
    id: string;
    nickname: string;
    avatar?: string;
    vipLevel?: string;
  };
  platform?: {
    id: string;
    name: string;
    type: string;
  };
  assignedUser?: {
    id: string;
    name: string;
    email: string;
  };
  messages?: Message[];
}

export interface ConversationFilters {
  status?: Conversation['status'];
  priority?: Conversation['priority'];
  assignedUserId?: string;
  organizationId?: string;
  qianniuAccountId?: string;
  customerId?: string;
  search?: string;
}

export interface CreateConversationData extends Record<string, unknown> {
  customerId: string;
  title?: string;
  priority?: Conversation['priority'];
}

export interface UpdateConversationData extends Record<string, unknown> {
  title?: string;
  status?: Conversation['status'];
  priority?: Conversation['priority'];
  assignedUserId?: string;
  tags?: string[];
}
