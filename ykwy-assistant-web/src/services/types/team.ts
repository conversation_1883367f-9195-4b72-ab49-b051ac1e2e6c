// 团队相关类型定义

export interface Team {
  id: string;
  name: string;
  description?: string;
  organizationId: string;
  createdAt: string;
  updatedAt: string;
  _count?: {
    members: number;
    qianniuClients: number;
    connectionInvitations: number;
  };
}

export interface TeamMember {
  id: string;
  name: string;
  email: string;
  role: string;
  image?: string;
  createdAt: string;
  permissionLevel?: number;
  canManageTeams?: boolean;
}

export interface TeamDetail extends Team {
  members: TeamMember[];
  qianniuClients: Array<{
    id: string;
    name: string;
    isOnline: boolean;
    lastOnlineAt: string;
  }>;
  connectionInvitations: Array<{
    id: string;
    name: string;
    status: string;
    createdAt: string;
  }>;
  organization?: {
    id: string;
    name: string;
  };
}

// 请求和响应类型
export interface CreateTeamRequest extends Record<string, unknown> {
  name: string;
  description?: string;
  organizationId?: string;
}

export interface UpdateTeamRequest extends Record<string, unknown> {
  name?: string;
  description?: string;
}

export interface TeamFilters extends Record<string, unknown> {
  page?: number;
  limit?: number;
  search?: string;
  organizationId?: string;
}

export interface TeamMemberFilters extends Record<string, unknown> {
  page?: number;
  limit?: number;
}

export interface AddTeamMemberRequest extends Record<string, unknown> {
  userId: string;
}

export interface RemoveTeamMemberRequest extends Record<string, unknown> {
  userId: string;
}

export interface UpdateTeamMemberRequest extends Record<string, unknown> {
  role?: string;
  permissionLevel?: number;
  canManageTeams?: boolean;
}

// 响应类型
export interface TeamsListResponse {
  data: Team[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface TeamMembersListResponse {
  data: TeamMember[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// 简化的团队选择类型
export interface TeamSelectOption {
  id: string;
  name: string;
}
