// 消息相关类型定义

export interface Message {
  id: string;
  conversationId: string;
  content: string;
  senderType: 'CUSTOMER' | 'CUSTOMER_SERVICE' | 'SYSTEM' | 'AI';
  senderId?: string;
  sentAt: string;
  messageType: 'TEXT' | 'IMAGE' | 'FILE' | 'AUDIO' | 'VIDEO' | 'SYSTEM';
  parentMessageId?: string;
  metadata?: Record<string, unknown>; // 添加metadata字段用于识别AI消息
  sender?: {
    id: string;
    name: string;
    email: string;
  };
}

export interface SendMessageData {
  conversationId: string;
  content: string;
  parentMessageId?: string;
  messageType?: Message['messageType'];
}

export interface MessageFilters {
  conversationId: string;
  senderType?: Message['senderType'];
  messageType?: Message['messageType'];
  startDate?: string;
  endDate?: string;
}
