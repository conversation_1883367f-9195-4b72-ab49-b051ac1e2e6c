// 客户相关类型定义

export interface Customer {
  id: string;
  name: string;
  avatar?: string;
  vipLevel: string;
  totalOrders: number;
  totalSpent: number;
  lastActive: string;
  tags: string[];
}

export interface CustomerFilters {
  vipLevel?: string;
  tags?: string[];
  search?: string;
}

export interface CreateCustomerData extends Record<string, unknown> {
  name: string;
  avatar?: string;
  vipLevel?: string;
  tags?: string[];
}

export interface UpdateCustomerData extends Record<string, unknown> {
  name?: string;
  avatar?: string;
  vipLevel?: string;
  tags?: string[];
}
