// 千牛账号相关 API 查询选项

import { apiClient } from '../../lib/api-client';
import { queryKeys } from '../../lib/query-keys';
import type { ApiResponse, CreateQianniuAccountRequest, QianniuAccount, UpdateQianniuAccountRequest } from '../types';

// 客户端账号列表查询选项
export const qianniuAccountsQueryOptions = (clientId: string) => ({
  queryKey: queryKeys.qianniuAccounts(clientId),
  queryFn: async () => {
    const response = await apiClient.get<ApiResponse<QianniuAccount[]>>(`qianniu-accounts/client/${clientId}`);
    return response;
  },
});

// 单个千牛账号详情查询选项
export const qianniuAccountQueryOptions = (accountId: string) => ({
  queryKey: queryKeys.qianniuAccount(accountId),
  queryFn: async () => {
    const response = await apiClient.get<ApiResponse<QianniuAccount>>(`qianniu-accounts/${accountId}`);
    return response.data;
  },
});

// 创建千牛账号
export const createQianniuAccountMutation = {
  mutationFn: async (data: CreateQianniuAccountRequest) => {
    const response = await apiClient.post<ApiResponse<QianniuAccount>>('qianniu-accounts', data as unknown as Record<string, unknown>);
    return response.data;
  },
};

// 更新千牛账号
export const updateQianniuAccountMutation = {
  mutationFn: async ({ accountId, data }: { accountId: string; data: UpdateQianniuAccountRequest }) => {
    const response = await apiClient.put<ApiResponse<QianniuAccount>>(`qianniu-accounts/${accountId}`, data as unknown as Record<string, unknown>);
    return response.data;
  },
};

// 删除千牛账号
export const deleteQianniuAccountMutation = {
  mutationFn: async (accountId: string) => {
    const response = await apiClient.delete<ApiResponse<{ id: string; clientId: string; accountName: string }>>(`qianniu-accounts/${accountId}`);
    return response.data;
  },
};
