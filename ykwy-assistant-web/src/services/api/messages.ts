// 消息相关 API 查询选项

import { apiClient } from '../../lib/api-client';
import { queryKeys } from '../../lib/query-keys';
import type { ApiResponse, Message, PaginatedResponse, SendMessageData } from '../types';

// 对话消息列表查询选项
export const conversationMessagesQueryOptions = (conversationId: string) => ({
  queryKey: queryKeys.conversationMessages(conversationId),
  queryFn: async () => {
    const response = await apiClient.get<PaginatedResponse<Message>>(`conversations/${conversationId}/messages`);
    return response;
  },
});

// 发送消息
export const sendMessageMutation = {
  mutationFn: async (data: SendMessageData) => {
    const response = await apiClient.post<ApiResponse<Message>>(`conversations/${data.conversationId}/messages`, {
      content: data.content,
      parentMessageId: data.parentMessageId,
      messageType: data.messageType || 'TEXT',
    });
    return response.data;
  },
};
