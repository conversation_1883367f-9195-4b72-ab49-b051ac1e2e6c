// 销售智能体推荐 API 服务

import { apiClient } from '../../lib/api-client';
import { queryKeys } from '../../lib/query-keys';

// AI推荐查询选项
export const aiRecommendationsQueryOptions = (conversationId: string, enabled = true) => ({
  queryKey: queryKeys.aiRecommendations(conversationId), // 只使用conversationId，保持稳定
  queryFn: async () => {
    try {
      const response = await apiClient.get<{ recommendations: string[] }>(`salesAgent/recommendations/conversation/${conversationId}`);
      return response.recommendations;
    } catch (error) {
      console.warn('推荐请求失败，返回默认推荐:', error);
      // 返回默认推荐而不是抛出错误，避免UI显示错误状态
      return ['感谢您的咨询，我来为您详细解答这个问题。', '我理解您的需求，让我为您提供专业的建议。', '根据您的情况，我来为您推荐最合适的解决方案。'];
    }
  },
  enabled: !!conversationId && enabled,
  staleTime: 1000 * 60 * 5, // 延长到5分钟，减少频繁请求
  gcTime: 1000 * 60 * 10, // 延长到10分钟
  refetchOnMount: false,
  refetchOnWindowFocus: false,
  refetchOnReconnect: false, // 网络重连时不自动刷新
  retry: 1, // 只重试1次，避免长时间等待
  retryDelay: 1000, // 重试延迟1秒
  // 添加查询超时，避免长时间等待
  meta: {
    timeout: 10000, // 10秒超时
  },
});
