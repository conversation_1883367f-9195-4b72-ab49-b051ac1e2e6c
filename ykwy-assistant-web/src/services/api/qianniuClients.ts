// 千牛客户端相关 API 查询选项

import { apiClient } from '../../lib/api-client';
import { queryKeys } from '../../lib/query-keys';
import type { ApiResponse, QianniuClient, QianniuClientFilters, RegisterQianniuClientRequest, UpdateQianniuClientStatusRequest } from '../types';

// 千牛客户端列表查询选项
export const qianniuClientsQueryOptions = (filters: QianniuClientFilters = {}) => ({
  queryKey: queryKeys.qianniuClients(filters as Record<string, unknown>),
  queryFn: async () => {
    const response = await apiClient.get<ApiResponse<QianniuClient[]>>('qianniu-clients', filters as Record<string, string | number | boolean | string[] | number[] | null | undefined>);
    return response;
  },
});

// 单个千牛客户端详情查询选项
export const qianniuClientQueryOptions = (clientId: string) => ({
  queryKey: queryKeys.qianniuClient(clientId),
  queryFn: async () => {
    const response = await apiClient.get<ApiResponse<QianniuClient>>(`qianniu-clients/${clientId}`);
    return response.data;
  },
});

// 组织的千牛客户端查询选项
export const organizationQianniuClientsQueryOptions = (organizationId: string) => ({
  queryKey: queryKeys.organizationQianniuClients(organizationId),
  queryFn: async () => {
    const response = await apiClient.get<ApiResponse<QianniuClient[]>>(`qianniu-clients/organization/${organizationId}`);
    return response.data;
  },
});

// 注册千牛客户端
export const registerQianniuClientMutation = {
  mutationFn: async (data: RegisterQianniuClientRequest) => {
    const response = await apiClient.post<ApiResponse<QianniuClient>>('qianniu-clients/register', data as unknown as Record<string, unknown>);
    return response.data;
  },
};

// 更新千牛客户端状态
export const updateQianniuClientStatusMutation = {
  mutationFn: async ({ clientId, data }: { clientId: string; data: UpdateQianniuClientStatusRequest }) => {
    const response = await apiClient.put<ApiResponse<QianniuClient>>(`qianniu-clients/${clientId}/status`, data as unknown as Record<string, unknown>);
    return response.data;
  },
};

// 删除千牛客户端
export const deleteQianniuClientMutation = {
  mutationFn: async (clientId: string) => {
    const response = await apiClient.delete<ApiResponse<null>>(`qianniu-clients/${clientId}`);
    return response.data;
  },
};
