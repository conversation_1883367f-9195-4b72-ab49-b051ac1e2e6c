// 对话相关 API 查询选项

import { apiClient } from '../../lib/api-client';
import { queryKeys } from '../../lib/query-keys';
import type { ApiResponse, Conversation, ConversationFilters, CreateConversationData, PaginatedResponse, UpdateConversationData } from '../types';

// 对话列表查询选项
export const conversationsQueryOptions = (filters: ConversationFilters = {}, page = 1, limit = 20) => ({
  queryKey: queryKeys.conversations({ ...filters, page, limit }),
  queryFn: async () => {
    const params = { page, limit, ...filters };
    console.log('🔍 [conversationsQueryOptions] 调用参数:', { filters, page, limit, params });

    // 确保 organizationId 存在才发起请求
    if (!('organizationId' in filters) || !filters.organizationId) {
      console.warn('⚠️ [conversationsQueryOptions] organizationId 缺失，跳过请求');
      throw new Error('organizationId is required');
    }

    const response = await apiClient.get<PaginatedResponse<Conversation>>('conversations', params);
    return response;
  },
});

// 单个对话详情查询选项
export const conversationQueryOptions = (id: string) => ({
  queryKey: queryKeys.conversation(id),
  queryFn: async () => {
    const response = await apiClient.get<ApiResponse<Conversation>>(`conversations/${id}`);
    return response.data;
  },
});

// 创建对话
export const createConversationMutation = {
  mutationFn: async (data: CreateConversationData) => {
    const response = await apiClient.post<ApiResponse<Conversation>>('conversations', data);
    return response.data;
  },
};

// 更新对话
export const updateConversationMutation = (conversationId: string) => ({
  mutationFn: async (data: UpdateConversationData) => {
    const response = await apiClient.put<ApiResponse<Conversation>>(`conversations/${conversationId}`, data);
    return response.data;
  },
});
