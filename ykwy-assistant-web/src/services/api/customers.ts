// 客户相关 API 查询选项

import { apiClient } from '../../lib/api-client';
import { queryKeys } from '../../lib/query-keys';
import type { ApiResponse, CreateCustomerData, Customer, CustomerFilters, PaginatedResponse, UpdateCustomerData } from '../types';

// 客户详情查询选项
export const customerQueryOptions = (customerId: string) => ({
  queryKey: queryKeys.customer(customerId),
  queryFn: async () => {
    try {
      const res = await apiClient.get<ApiResponse<Customer>>(`customers/${customerId}`);
      return res.data;
    } catch {
      // --- MOCK 数据 ---
      return {
        id: customerId,
        name: '李小明',
        avatar: undefined,
        vipLevel: 'VIP3',
        totalOrders: 12,
        totalSpent: 5231.5,
        lastActive: new Date().toISOString(),
        tags: ['老客户', '高价值'],
      } as Customer;
    }
  },
});

// 客户列表查询选项
export const customersQueryOptions = (filters: CustomerFilters = {}, page = 1, limit = 20) => ({
  queryKey: queryKeys.customers({ ...filters, page, limit }),
  queryFn: async () => {
    const params = { page, limit, ...filters };
    const response = await apiClient.get<PaginatedResponse<Customer>>('customers', params);
    return response;
  },
});

// 创建客户
export const createCustomerMutation = {
  mutationFn: async (data: CreateCustomerData) => {
    const response = await apiClient.post<ApiResponse<Customer>>('customers', data);
    return response.data;
  },
};

// 更新客户
export const updateCustomerMutation = (customerId: string) => ({
  mutationFn: async (data: UpdateCustomerData) => {
    const response = await apiClient.put<ApiResponse<Customer>>(`customers/${customerId}`, data);
    return response.data;
  },
});
