// 对话自动回复控制相关 API

import { apiClient } from '../../lib/api-client';
import { queryKeys } from '../../lib/query-keys';
import type { ApiResponse } from '../types';

// 自动回复状态类型
export interface ConversationAutoReplyStatus {
  conversationId: string;
  autoReplyEnabled: boolean;
}

// 设置自动回复状态的请求类型
export interface SetAutoReplyRequest {
  enabled: boolean;
}

// 设置自动回复状态的响应类型
export interface SetAutoReplyResponse {
  conversationId: string;
  autoReplyEnabled: boolean;
  changed: boolean;
}

// 获取对话自动回复状态
export const conversationAutoReplyStatusQueryOptions = (conversationId: string) => ({
  queryKey: queryKeys.conversationAutoReplyStatus(conversationId),
  queryFn: async (): Promise<ApiResponse<ConversationAutoReplyStatus>> => {
    if (!conversationId) {
      throw new Error('Conversation ID is required');
    }

    const response = await apiClient.get<ApiResponse<ConversationAutoReplyStatus>>(`conversations/${conversationId}/auto-reply`);
    return response;
  },
  enabled: !!conversationId,
  staleTime: 0, // 不缓存，总是重新获取
});

// 设置对话自动回复状态的 mutation
export const setConversationAutoReplyMutation = {
  mutationFn: async ({ conversationId, enabled }: { conversationId: string; enabled: boolean }): Promise<SetAutoReplyResponse> => {
    const response = await apiClient.put<SetAutoReplyResponse>(`conversations/${conversationId}/auto-reply`, { enabled });
    return response;
  },
};

// 直接取消自动回复的 mutation（客服接入专用）
export const cancelConversationAutoReplyMutation = {
  mutationFn: async (conversationId: string): Promise<SetAutoReplyResponse> => {
    const response = await apiClient.post<SetAutoReplyResponse>(`conversations/${conversationId}/cancel-auto-reply`);
    return response;
  },
};

// 批量设置多个对话的自动回复状态
export const batchSetConversationAutoReplyMutation = {
  mutationFn: async ({ conversationIds, enabled }: { conversationIds: string[]; enabled: boolean }): Promise<{ updatedCount: number; autoReplyEnabled: boolean }> => {
    const response = await apiClient.put<{
      updatedCount: number;
      autoReplyEnabled: boolean;
    }>('conversations/batch/auto-reply', { conversationIds, enabled });
    return response;
  },
};
