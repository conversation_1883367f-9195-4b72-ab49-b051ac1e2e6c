// 团队相关 React Query Hooks

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { useOrganization } from '../../hooks/useOrganization';
import { queryKeys } from '../../lib/query-keys';
import {
  addTeamMemberMutation,
  createTeamMutation,
  deleteTeamMutation,
  removeTeamMemberMutation,
  teamMembersQueryOptions,
  teamQueryOptions,
  teamsForSelectQueryOptions,
  teamsQueryOptions,
  updateTeamMemberMutation,
  updateTeamMutation,
} from '../api/teams';
import type { TeamFilters, TeamMemberFilters } from '../types';

// 获取团队列表
export const useTeams = (filters: TeamFilters = {}) => {
  const { organizationId } = useOrganization();

  return useQuery({
    ...teamsQueryOptions({ ...filters, organizationId }),
    enabled: !!organizationId, // 只有 organizationId 存在时才启用查询
  });
};

// 获取团队详情
export const useTeam = (teamId?: string) => {
  return useQuery({
    ...teamQueryOptions(teamId || ''),
    enabled: !!teamId,
  });
};

// 获取团队成员列表
export const useTeamMembers = (teamId?: string, filters: TeamMemberFilters = {}) => {
  return useQuery({
    ...teamMembersQueryOptions(teamId || '', filters),
    enabled: !!teamId,
  });
};

// 获取团队选择选项（用于下拉选择）
export const useTeamsForSelect = () => {
  const { organizationId } = useOrganization();

  return useQuery({
    ...teamsForSelectQueryOptions(organizationId),
    enabled: !!organizationId,
  });
};

// 创建团队
export const useCreateTeam = () => {
  const queryClient = useQueryClient();

  return useMutation({
    ...createTeamMutation,
    onSuccess: () => {
      // 刷新团队列表
      queryClient.invalidateQueries({
        queryKey: queryKeys.teams(),
      });
    },
  });
};

// 更新团队
export const useUpdateTeam = (teamId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    ...updateTeamMutation(teamId),
    onSuccess: (data) => {
      // 更新缓存中的团队详情
      queryClient.setQueryData(queryKeys.team(teamId), data);
      // 刷新团队列表
      queryClient.invalidateQueries({
        queryKey: queryKeys.teams(),
      });
    },
  });
};

// 删除团队
export const useDeleteTeam = (teamId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    ...deleteTeamMutation(teamId),
    onSuccess: () => {
      // 刷新团队列表
      queryClient.invalidateQueries({
        queryKey: queryKeys.teams(),
      });
      // 移除团队详情缓存
      queryClient.removeQueries({
        queryKey: queryKeys.team(teamId),
      });
    },
  });
};

// 添加团队成员
export const useAddTeamMember = (teamId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    ...addTeamMemberMutation(teamId),
    onSuccess: () => {
      // 刷新团队成员列表
      queryClient.invalidateQueries({
        queryKey: queryKeys.teamMembers(teamId),
      });
      // 刷新团队详情
      queryClient.invalidateQueries({
        queryKey: queryKeys.team(teamId),
      });
    },
  });
};

// 移除团队成员
export const useRemoveTeamMember = (teamId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    ...removeTeamMemberMutation(teamId),
    onSuccess: () => {
      // 刷新团队成员列表
      queryClient.invalidateQueries({
        queryKey: queryKeys.teamMembers(teamId),
      });
      // 刷新团队详情
      queryClient.invalidateQueries({
        queryKey: queryKeys.team(teamId),
      });
    },
  });
};

// 更新团队成员
export const useUpdateTeamMember = (teamId: string, memberId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    ...updateTeamMemberMutation(teamId, memberId),
    onSuccess: () => {
      // 刷新团队成员列表
      queryClient.invalidateQueries({
        queryKey: queryKeys.teamMembers(teamId),
      });
      // 刷新团队详情
      queryClient.invalidateQueries({
        queryKey: queryKeys.team(teamId),
      });
    },
  });
};
