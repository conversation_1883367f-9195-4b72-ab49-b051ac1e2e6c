// 千牛监控相关 React Query Hooks

import { useQuery } from '@tanstack/react-query';

import { activeConnectionsQueryOptions, connectionStatsQueryOptions, messageStatsQueryOptions } from '../api/qianniuMonitor';

// 获取连接统计
export const useConnectionStats = (refreshInterval: number = 30000) => {
  return useQuery({
    ...connectionStatsQueryOptions(),
    refetchInterval: refreshInterval,
  });
};

// 获取活跃连接列表
export const useActiveConnections = (refreshInterval: number = 30000) => {
  return useQuery({
    ...activeConnectionsQueryOptions(),
    refetchInterval: refreshInterval,
  });
};

// 获取消息统计
export const useMessageStats = (refreshInterval: number = 30000) => {
  return useQuery({
    ...messageStatsQueryOptions(),
    refetchInterval: refreshInterval,
  });
};
