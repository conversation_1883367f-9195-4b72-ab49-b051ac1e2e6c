// 千牛TCP相关 React Query Hooks

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { queryKeys } from '../../lib/query-keys';
import { deleteTcpConnectionMutation, registerTcpConnectionMutation, tcpConnectionsQueryOptions, testSendMessageMutation } from '../api/qianniuTcp';

// 获取TCP连接状态
export const useTcpConnections = () => {
  return useQuery({
    ...tcpConnectionsQueryOptions(),
    refetchInterval: 5000, // 每5秒刷新一次
  });
};

// 注意：TCP连接映射信息已包含在useTcpConnections()的响应中，无需单独的hook

// 注册TCP连接映射
export const useRegisterTcpConnection = () => {
  const queryClient = useQueryClient();

  return useMutation({
    ...registerTcpConnectionMutation,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.tcpConnections() });
    },
  });
};

// 删除TCP连接映射
export const useDeleteTcpConnection = () => {
  const queryClient = useQueryClient();

  return useMutation({
    ...deleteTcpConnectionMutation,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.tcpConnections() });
    },
  });
};

// 测试发送消息
export const useTestSendMessage = () => {
  return useMutation({
    ...testSendMessageMutation,
  });
};
