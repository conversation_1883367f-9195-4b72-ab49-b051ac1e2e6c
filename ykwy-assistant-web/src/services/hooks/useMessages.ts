// 消息相关 React Query Hooks

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { queryKeys } from '../../lib/query-keys';
import { conversationMessagesQueryOptions, sendMessageMutation } from '../api/messages';

// 获取对话消息列表
export const useConversationMessages = (conversationId?: string) => {
  return useQuery({
    ...conversationMessagesQueryOptions(conversationId!),
    enabled: !!conversationId, // 只有 conversationId 存在时才启用查询
  });
};

// 发送消息
export const useSendMessage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    ...sendMessageMutation,
    onSuccess: (_, variables) => {
      // 刷新对应对话的消息列表
      queryClient.invalidateQueries({
        queryKey: queryKeys.conversationMessages(variables.conversationId),
      });

      // 刷新对话列表（更新最后活动时间）
      queryClient.invalidateQueries({
        queryKey: queryKeys.conversations(),
      });
    },
  });
};
