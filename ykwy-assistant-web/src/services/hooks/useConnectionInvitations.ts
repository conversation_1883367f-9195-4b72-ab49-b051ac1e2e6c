// 连接邀请相关 React Query Hooks

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { useOrganization } from '../../hooks/useOrganization';
import { authClient } from '../../lib/auth-client';
import { queryKeys } from '../../lib/query-keys';
import {
  connectionInvitationQueryOptions,
  connectionInvitationsQueryOptions,
  createConnectionInvitationMutation,
  regenerateConnectionInvitationMutation,
  revokeConnectionInvitationMutation,
} from '../api/connectionInvitations';
import type { ConnectionInvitationFilters } from '../types';

// 获取连接邀请列表
export const useConnectionInvitations = (filters: ConnectionInvitationFilters = {}) => {
  const { data: session } = authClient.useSession();
  const { organizationId } = useOrganization();
  const user = session?.user as { id: string } | undefined;

  // 调试信息
  console.log('[useConnectionInvitations] Debug info:', {
    user: user,
    organizationId: organizationId,
    enabled: !!user?.id && !!organizationId,
  });

  return useQuery({
    ...connectionInvitationsQueryOptions({ ...filters, organizationId }),
    enabled: !!user?.id && !!organizationId, // 仅有用户ID和组织ID后才请求
  });
};

// 获取单个连接邀请详情
export const useConnectionInvitation = (id?: string) => {
  return useQuery({
    ...connectionInvitationQueryOptions(id || ''),
    enabled: !!id, // 仅有ID时才请求
  });
};

// 创建连接邀请
export const useCreateConnectionInvitation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    ...createConnectionInvitationMutation,
    onSuccess: () => {
      // 创建成功后刷新所有连接邀请相关的查询
      queryClient.invalidateQueries({
        queryKey: queryKeys.connectionInvitations(),
      });
    },
  });
};

// 撤销连接邀请
export const useRevokeConnectionInvitation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    ...revokeConnectionInvitationMutation,
    onSuccess: () => {
      // 撤销成功后刷新所有连接邀请相关的查询
      queryClient.invalidateQueries({
        queryKey: queryKeys.connectionInvitations(),
      });
    },
  });
};

// 重新生成连接邀请
export const useRegenerateConnectionInvitation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    ...regenerateConnectionInvitationMutation,
    onSuccess: (_, variables) => {
      // 重新生成成功后刷新列表和详情
      queryClient.invalidateQueries({
        queryKey: queryKeys.connectionInvitations(),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.connectionInvitation(variables.id),
      });
    },
  });
};
