import { useOrganization } from '../../hooks/useOrganization';
import { authClient } from '../../lib/auth-client';

export default function SettingsPage() {
  /* 用户信息 */
  const { data: session } = authClient.useSession();
  const { organizationId, organization } = useOrganization();

  // 在单一组织架构下，用户只属于一个组织，不需要切换组织功能

  return (
    <div className="p-6 space-y-6 min-h-full max-w-6xl mx-auto">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-2">系统设置</h1>
        <p className="text-gray-600">管理个人资料与当前工作空间</p>
      </div>

      {/* 个人资料 */}
      <section className="bg-white rounded-lg border border-gray-200 p-6 w-full max-w-xl">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">个人资料</h2>
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <span className="w-24 text-gray-600">姓名</span>
            <span>{session?.user?.name ?? '未设置'}</span>
          </div>
          <div className="flex items-center gap-4">
            <span className="w-24 text-gray-600">邮箱</span>
            <span>{session?.user?.email}</span>
          </div>
          {/* 如需编辑，可在此处添加表单并调用 authClient.profile.update */}
        </div>
      </section>

      {/* 组织信息 */}
      <section className="bg-white rounded-lg border border-gray-200 p-6 w-full max-w-xl">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">组织信息</h2>
        <div className="space-y-3">
          {organizationId ? (
            <div>
              <label className="block text-sm font-medium text-gray-700">所属组织</label>
              <p className="mt-1 text-sm text-gray-900">组织ID: {organizationId}</p>
              <p className="mt-1 text-sm text-gray-900">组织名称: {organization?.name || '未知'}</p>
              <p className="text-xs text-gray-500 mt-1">在单一组织架构下，用户只能属于一个组织</p>
            </div>
          ) : (
            <div>
              <p className="text-sm text-gray-500">您当前未属于任何组织</p>
              <p className="text-xs text-gray-400 mt-1">请联系管理员邀请您加入组织</p>
            </div>
          )}
        </div>
      </section>
    </div>
  );
}
