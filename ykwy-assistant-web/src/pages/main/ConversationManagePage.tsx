import { <PERSON><PERSON>, <PERSON>u, MessageCircle } from 'lucide-react';
import { useEffect, useState } from 'react';

import AIPanel from '../../components/main/conversations/AIPanel';
import ChatArea from '../../components/main/conversations/ChatArea';
import ConversationList from '../../components/main/conversations/ConversationList';
import { useAppStore } from '../../lib/store';

import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

type ActivePanel = 'list' | 'chat' | 'ai';

export default function ConversationManagePage() {
  const [selectedConversationId, setSelectedConversationId] = useState<string | undefined>();

  // 添加移动端活动面板状态
  const isMobileView = useAppStore((state) => state.isMobileView);
  const [activePanel, setActivePanel] = useState<ActivePanel>('chat');

  // 添加小屏幕PC判断
  const [isSmallScreen, setIsSmallScreen] = useState(false);

  // 监听窗口大小判断是否为小屏幕PC
  useEffect(() => {
    const handleResize = () => {
      // 768px 以上 1024px 以下为小屏幕PC
      setIsSmallScreen(window.innerWidth >= 768 && window.innerWidth < 1024);
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 处理对话选择，在移动端上自动切换到聊天区域
  const handleSelectConversation = (id: string) => {
    setSelectedConversationId(id);
    if (isMobileView || isSmallScreen) {
      setActivePanel('chat');
    }
  };

  return (
    <div className="flex h-full flex-col">
      {/* 移动端和小屏PC导航选项卡 */}
      {(isMobileView || isSmallScreen) && (
        <div className="flex justify-center p-2 border-b bg-white">
          <div className="flex space-x-2">
            <Button variant={activePanel === 'list' ? 'default' : 'outline'} size="sm" onClick={() => setActivePanel('list')} className="flex items-center gap-1">
              <Menu className="w-4 h-4" />
              <span>对话列表</span>
            </Button>
            <Button variant={activePanel === 'chat' ? 'default' : 'outline'} size="sm" onClick={() => setActivePanel('chat')} className="flex items-center gap-1">
              <MessageCircle className="w-4 h-4" />
              <span>聊天</span>
            </Button>
            <Button variant={activePanel === 'ai' ? 'default' : 'outline'} size="sm" onClick={() => setActivePanel('ai')} className="flex items-center gap-1">
              <Bot className="w-4 h-4" />
              <span>AI面板</span>
            </Button>
          </div>
        </div>
      )}

      {/* 内容区域 */}
      <div className={cn('flex flex-1 h-full overflow-hidden', (isMobileView || isSmallScreen) && 'flex-col')}>
        {/* 左侧对话列表 */}
        <div
          className={cn(
            'border-r h-full overflow-hidden',
            // 大屏幕视图
            !isMobileView && !isSmallScreen && 'w-80',
            // 小屏PC和移动视图
            (isMobileView || isSmallScreen) && 'w-full flex-1',
            // 在小屏PC或移动端，如果不是活动面板则隐藏
            (isMobileView || isSmallScreen) && activePanel !== 'list' && 'hidden',
          )}
        >
          <ConversationList onSelectConversation={handleSelectConversation} selectedConversationId={selectedConversationId} />
        </div>

        {/* 中间聊天区域 */}
        <div
          className={cn(
            'h-full overflow-hidden flex',
            // 大屏幕视图
            !isMobileView && !isSmallScreen && 'flex-1',
            // 小屏PC和移动视图
            (isMobileView || isSmallScreen) && 'w-full flex-1',
            // 在小屏PC或移动端，如果不是活动面板则隐藏
            (isMobileView || isSmallScreen) && activePanel !== 'chat' && 'hidden',
          )}
        >
          <ChatArea conversationId={selectedConversationId} />
        </div>

        {/* 右侧AI面板 */}
        <div
          className={cn(
            'border-l h-full overflow-hidden',
            // 大屏幕视图
            !isMobileView && !isSmallScreen && 'w-72',
            // 小屏PC和移动视图
            (isMobileView || isSmallScreen) && 'w-full flex-1',
            // 在小屏PC或移动端，如果不是活动面板则隐藏
            (isMobileView || isSmallScreen) && activePanel !== 'ai' && 'hidden',
          )}
        >
          <AIPanel conversationId={selectedConversationId} />
        </div>
      </div>
    </div>
  );
}
