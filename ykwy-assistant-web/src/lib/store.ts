import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 自动回复通知类型
export interface AutoReplyNotification {
  id: string;
  conversationId: string;
  customerName: string;
  timestamp: string;
  read: boolean;
}

// 客户消息通知类型
export interface CustomerMessageNotification {
  conversationId: string; // 会话ID
  messageIds: string[]; // 未读消息ID列表
  customerName: string; // 客户名称
  count: number; // 未读消息数量
  timestamp: string; // 最新消息时间戳
  isAutoReplied: boolean; // 是否已被自动回复
}

// 新通知输入类型
export type NewNotification = {
  conversationId: string;
  customerName: string;
  timestamp: string;
};

// 新客户消息类型
export type NewCustomerMessage = {
  conversationId: string;
  messageId: string;
  customerName: string;
  timestamp: string;
  isAutoReplied: boolean;
};

// 全局状态类型
export interface AppState {
  // 自动回复通知相关
  autoReplyNotifications: AutoReplyNotification[];
  addAutoReplyNotification: (notification: NewNotification) => void;
  markNotificationAsRead: (conversationId: string) => void;
  markAllNotificationsAsRead: () => void;
  clearNotifications: () => void;
  getUnreadCount: () => number;
  getNotificationsByConversation: (conversationId: string) => AutoReplyNotification[];
  hasUnreadNotifications: (conversationId: string) => boolean;

  // 客户消息通知相关
  customerMessageNotifications: Record<string, CustomerMessageNotification>;
  addCustomerMessage: (message: NewCustomerMessage) => void;
  markConversationAsRead: (conversationId: string) => void;
  getUnreadMessageCount: (conversationId: string) => number;
  setMessageAutoReplied: (conversationId: string, isAutoReplied: boolean) => void;
  getTotalUnreadConversations: () => number;

  // 侧边栏折叠状态
  sidebarCollapsed: boolean;
  toggleSidebar: () => void;

  // 移动视图状态
  isMobileView: boolean;
  setIsMobileView: (isMobile: boolean) => void;
  mobileSidebarOpen: boolean;
  openMobileSidebar: () => void;
  closeMobileSidebar: () => void;
  toggleMobileSidebar: () => void;
}

// 创建全局状态存储
export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // 自动回复通知列表
      autoReplyNotifications: [],

      // 添加一条自动回复通知
      addAutoReplyNotification: (notification: NewNotification) =>
        set((state) => {
          // 检查是否已存在相同conversationId的通知
          const existingIndex = state.autoReplyNotifications.findIndex((n) => n.conversationId === notification.conversationId);

          if (existingIndex >= 0) {
            // 更新现有通知
            const updated = [...state.autoReplyNotifications];
            const existingNotification = updated[existingIndex];

            // 确保存在通知对象且有id属性
            if (existingNotification && existingNotification.id) {
              updated[existingIndex] = {
                id: existingNotification.id,
                conversationId: notification.conversationId,
                customerName: notification.customerName,
                timestamp: notification.timestamp,
                read: false,
              };
              return { autoReplyNotifications: updated };
            }

            // 如果现有通知无效，则创建新通知
            const newNotification: AutoReplyNotification = {
              id: `notification-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
              conversationId: notification.conversationId,
              customerName: notification.customerName,
              timestamp: notification.timestamp,
              read: false,
            };

            updated[existingIndex] = newNotification;
            return { autoReplyNotifications: updated };
          } else {
            // 添加新通知
            const newNotification: AutoReplyNotification = {
              id: `notification-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
              conversationId: notification.conversationId,
              customerName: notification.customerName,
              timestamp: notification.timestamp,
              read: false,
            };

            return {
              autoReplyNotifications: [newNotification, ...state.autoReplyNotifications],
            };
          }
        }),

      // 将指定会话的通知标记为已读
      markNotificationAsRead: (conversationId) => {
        // 先检查是否有需要标记为已读的通知
        const hasUnread = get().autoReplyNotifications.some((n) => n.conversationId === conversationId && !n.read);

        // 只有存在未读通知时才更新状态
        if (hasUnread) {
          set((state) => ({
            autoReplyNotifications: state.autoReplyNotifications.map((notification) => (notification.conversationId === conversationId ? { ...notification, read: true } : notification)),
          }));
        }
      },

      // 将所有通知标记为已读
      markAllNotificationsAsRead: () => {
        // 先检查是否有需要标记为已读的通知
        const hasUnread = get().autoReplyNotifications.some((n) => !n.read);

        // 只有存在未读通知时才更新状态
        if (hasUnread) {
          set((state) => ({
            autoReplyNotifications: state.autoReplyNotifications.map((notification) => ({
              ...notification,
              read: true,
            })),
          }));
        }
      },

      // 清空所有通知
      clearNotifications: () => {
        // 只有存在通知时才更新状态
        if (get().autoReplyNotifications.length > 0) {
          set({ autoReplyNotifications: [] });
        }
      },

      // 获取未读通知数量
      getUnreadCount: () => {
        return get().autoReplyNotifications.filter((n) => !n.read).length;
      },

      // 获取指定会话的通知
      getNotificationsByConversation: (conversationId) => {
        return get().autoReplyNotifications.filter((n) => n.conversationId === conversationId);
      },

      // 检查指定会话是否有未读通知
      hasUnreadNotifications: (conversationId) => {
        return get().autoReplyNotifications.some((n) => n.conversationId === conversationId && !n.read);
      },

      // 客户消息通知存储
      customerMessageNotifications: {},

      // 添加一条客户消息
      addCustomerMessage: (message) => {
        set((state) => {
          const { conversationId, messageId, customerName, timestamp, isAutoReplied } = message;

          // 获取当前会话的通知
          const existingNotification = state.customerMessageNotifications[conversationId];

          if (existingNotification) {
            // 更新现有通知
            return {
              customerMessageNotifications: {
                ...state.customerMessageNotifications,
                [conversationId]: {
                  ...existingNotification,
                  messageIds: [...existingNotification.messageIds, messageId],
                  count: existingNotification.count + 1,
                  timestamp: timestamp,
                  isAutoReplied: isAutoReplied,
                },
              },
            };
          } else {
            // 添加新通知
            return {
              customerMessageNotifications: {
                ...state.customerMessageNotifications,
                [conversationId]: {
                  conversationId,
                  messageIds: [messageId],
                  customerName,
                  count: 1,
                  timestamp,
                  isAutoReplied,
                },
              },
            };
          }
        });
      },

      // 将指定会话标记为已读
      markConversationAsRead: (conversationId) => {
        set((state) => {
          // 创建一个新的对象，不包含已读的会话
          const newNotifications = { ...state.customerMessageNotifications };
          delete newNotifications[conversationId];

          return {
            customerMessageNotifications: newNotifications,
          };
        });
      },

      // 获取指定会话的未读消息数量
      getUnreadMessageCount: (conversationId) => {
        const notification = get().customerMessageNotifications[conversationId];
        // 如果有通知且没有自动回复，则返回未读消息数量
        return notification && !notification.isAutoReplied ? notification.count : 0;
      },

      // 设置会话的自动回复状态
      setMessageAutoReplied: (conversationId, isAutoReplied) => {
        set((state) => {
          const notification = state.customerMessageNotifications[conversationId];
          if (!notification) return state;

          return {
            customerMessageNotifications: {
              ...state.customerMessageNotifications,
              [conversationId]: {
                ...notification,
                isAutoReplied,
              },
            },
          };
        });
      },

      // 获取未读会话总数
      getTotalUnreadConversations: () => {
        const { customerMessageNotifications } = get();

        // 只计算未自动回复的会话
        return Object.values(customerMessageNotifications).filter((notification) => !notification.isAutoReplied).length;
      },

      // 侧边栏折叠状态
      sidebarCollapsed: false,
      toggleSidebar: () => set((state) => ({ sidebarCollapsed: !state.sidebarCollapsed })),

      // 移动视图状态
      isMobileView: false,
      setIsMobileView: (isMobile: boolean) => set({ isMobileView: isMobile }),
      mobileSidebarOpen: false,
      openMobileSidebar: () => set({ mobileSidebarOpen: true }),
      closeMobileSidebar: () => set({ mobileSidebarOpen: false }),
      toggleMobileSidebar: () => set((state) => ({ mobileSidebarOpen: !state.mobileSidebarOpen })),
    }),
    {
      name: 'ykwy-app-storage',
      partialize: (state) => ({
        autoReplyNotifications: state.autoReplyNotifications,
        customerMessageNotifications: state.customerMessageNotifications,
      }),
    },
  ),
);
