const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3002';

// 获取存储的认证token
function getAuthToken(): string | null {
  try {
    const stored = localStorage.getItem('auth-storage');
    if (stored) {
      const data = JSON.parse(stored);
      return data.accessToken || null;
    }
  } catch (error) {
    console.error('Failed to get auth token:', error);
  }
  return null;
}

export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: Response,
    public code?: string,
  ) {
    super(message);
    this.name = 'ApiError';
  }

  get isNetworkError(): boolean {
    return this.status === 0;
  }

  get isClientError(): boolean {
    return this.status >= 400 && this.status < 500;
  }

  get isServerError(): boolean {
    return this.status >= 500;
  }
}

interface ApiRequestConfig {
  method?: string;
  headers?: Record<string, string>;
  body?: string;
  credentials?: 'include' | 'omit' | 'same-origin';
  timeout?: number;
  retries?: number;
  retryDelay?: number;
}

async function fetchWithTimeout(url: string, config: ApiRequestConfig, timeout: number = 10000): Promise<Response> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(url, {
      ...config,
      signal: controller.signal,
    });
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
}

async function fetchWithRetry<T>(url: string, config: ApiRequestConfig, retries: number = 2, retryDelay: number = 1000): Promise<T> {
  let lastError: Error | null = null;

  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      const response = await fetchWithTimeout(url, config, config.timeout);

      if (!response.ok) {
        let errorMessage = `API Error: ${response.status} ${response.statusText}`;
        let errorCode: string | undefined;

        try {
          const errorData = await response.json();
          if (errorData.message) {
            errorMessage = errorData.message;
          }
          if (errorData.code) {
            errorCode = errorData.code;
          }
        } catch {
          // 忽略解析错误，使用默认错误消息
        }

        const apiError = new ApiError(errorMessage, response.status, response, errorCode);

        // 客户端错误不重试
        if (apiError.isClientError) {
          throw apiError;
        }

        // 最后一次尝试，直接抛出错误
        if (attempt === retries) {
          throw apiError;
        }

        lastError = apiError;
      } else {
        return await response.json();
      }
    } catch (error) {
      lastError = error as Error;

      // 网络错误或服务器错误才重试
      if (attempt === retries || (error instanceof ApiError && error.isClientError)) {
        break;
      }

      // 等待后重试
      if (attempt < retries) {
        await new Promise((resolve) => setTimeout(resolve, retryDelay * Math.pow(2, attempt)));
      }
    }
  }

  if (lastError instanceof ApiError) {
    throw lastError;
  }
  throw new ApiError('Network error', 0, undefined, 'NETWORK_ERROR');
}

async function fetchApi<T>(endpoint: string, apiVersion: string = 'v1', options: ApiRequestConfig = {}): Promise<T> {
  const url = `${API_BASE_URL}/api/${apiVersion}/${endpoint}`;

  // 调试日志
  console.log('🔍 [API Client] 构造的 URL:', url);
  console.log('🔍 [API Client] endpoint:', endpoint);
  console.log('🔍 [API Client] API_BASE_URL:', API_BASE_URL);

  // 获取认证token并添加到请求头
  const token = getAuthToken();

  console.log('🔍 [API Client] 认证token存在:', !!token);
  if (token) {
    console.log('🔍 [API Client] 添加Authorization头');
  }

  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...((options.headers as Record<string, string>) || {}),
  };

  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  const config: ApiRequestConfig = {
    headers,
    credentials: 'include', // 包含认证cookie
    timeout: 1000 * 60 * 3,
    retries: 2,
    retryDelay: 1000,
    ...options,
  };

  return fetchWithRetry<T>(url, config, config.retries, config.retryDelay);
}

// 参数类型定义
type QueryParams = Record<string, string | number | boolean | string[] | number[] | undefined | null>;
type RequestData = Record<string, unknown> | FormData | string | null | undefined;

// 参数序列化工具
function serializeParams(params: QueryParams): string {
  const searchParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      if (Array.isArray(value)) {
        value.forEach((item) => searchParams.append(key, String(item)));
      } else {
        searchParams.append(key, String(value));
      }
    }
  });

  return searchParams.toString();
}

export const apiClient = {
  get: <T>(endpoint: string, params?: QueryParams, config?: ApiRequestConfig) => {
    console.log('🔍 [API Client GET] 原始 endpoint:', endpoint);
    console.log('🔍 [API Client GET] 查询参数:', params);

    const url = params && Object.keys(params).length > 0 ? `${endpoint}?${serializeParams(params)}` : endpoint;

    console.log('🔍 [API Client GET] 最终 URL:', url);
    return fetchApi<T>(url, 'v1', { ...config, method: 'GET' });
  },

  post: <T>(endpoint: string, data?: RequestData, config?: ApiRequestConfig) =>
    fetchApi<T>(endpoint, 'v1', {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
      ...config,
    }),

  put: <T>(endpoint: string, data?: RequestData, config?: ApiRequestConfig) =>
    fetchApi<T>(endpoint, 'v1', {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
      ...config,
    }),

  patch: <T>(endpoint: string, data?: RequestData, config?: ApiRequestConfig) =>
    fetchApi<T>(endpoint, 'v1', {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
      ...config,
    }),

  delete: <T>(endpoint: string, config?: ApiRequestConfig) => fetchApi<T>(endpoint, 'v1', { method: 'DELETE', ...config }),
};

// 导出类型
export type { ApiRequestConfig };
