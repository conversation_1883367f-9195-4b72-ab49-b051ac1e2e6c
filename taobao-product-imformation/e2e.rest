### E2E测试 - 智能销售代理API

@baseUrl = http://localhost:8000
@shopId = 54898e9a-e7d2-4351-9f74-6e45d74ec153
@brandId = qinggong

### 1. 健康检查
GET {{baseUrl}}/health

### 2. 数据同步 - 增量同步
POST {{baseUrl}}/sync
Content-Type: application/json

{
  "shop_id": "{{shopId}}",
  "incremental": true,
  "sync_products": true,
  "sync_qa": true
}

### 3. 数据同步 - 全量同步
POST {{baseUrl}}/sync
Content-Type: application/json

{
  "shop_id": "{{shopId}}",
  "incremental": false,
  "sync_products": true,
  "sync_qa": true
}

### 4. 只同步产品数据 - 增量
POST {{baseUrl}}/sync/products?shop_id={{shopId}}&incremental=true

### 5. 只同步产品数据 - 全量
POST {{baseUrl}}/sync/products?shop_id={{shopId}}&incremental=false

### 6. 只同步问答知识库 - 增量
POST {{baseUrl}}/sync/qa?shop_id={{shopId}}&incremental=true

### 7. 只同步问答知识库 - 全量
POST {{baseUrl}}/sync/qa?shop_id={{shopId}}&incremental=false

### 8. 聊天测试 - 问候
POST {{baseUrl}}/chat
Content-Type: application/json

{
  "message": "你好",
  "brand_id": "{{brandId}}",
  "shop_id": "{{shopId}}"
}

### 9. 聊天测试 - 产品咨询
POST {{baseUrl}}/chat
Content-Type: application/json

{
  "message": "有什么跑步外套推荐吗？",
  "brand_id": "{{brandId}}",
  "shop_id": "{{shopId}}"
}

### 10. 聊天测试 - 售后问题
POST {{baseUrl}}/chat
Content-Type: application/json

{
  "message": "怎么退货？",
  "brand_id": "{{brandId}}",
  "shop_id": "{{shopId}}"
}

### 11. 聊天测试 - 尺码咨询
POST {{baseUrl}}/chat
Content-Type: application/json

{
  "message": "XL码适合多高的人穿？",
  "brand_id": "{{brandId}}",
  "shop_id": "{{shopId}}"
}

### 12. 聊天测试 - 优惠咨询
POST {{baseUrl}}/chat
Content-Type: application/json

{
  "message": "有优惠券吗？",
  "brand_id": "{{brandId}}",
  "shop_id": "{{shopId}}"
}

### 13. 向量搜索 - 产品搜索
POST {{baseUrl}}/vector/search/products
Content-Type: application/json

{
  "query": "跑步外套",
  "limit": 3,
  "shop_id": "{{shopId}}"
}

### 14. 重置对话
POST {{baseUrl}}/reset

### 15. 直接测试问答知识库API
# @name testQaApi
GET https://ykwy-api.wuyoutansuo.com/api/v2/qa-knowledge-base/by-shop?shopId={{shopId}}
Access-Token: PoNyP4f0zYDWuXg8NytR

### 16. 直接测试产品API
# @name testProductApi
GET https://ykwy-api.wuyoutansuo.com/api/v2/temp-product/search?shopId={{shopId}}
Access-Token: PoNyP4f0zYDWuXg8NytR

### 17. 测试问答知识库分页查询 - 第1页
# @name testQaPagination1
GET https://ykwy-api.wuyoutansuo.com/api/v2/qa-knowledge-base/by-shop?shopId={{shopId}}&page=1&pageSize=10
Access-Token: PoNyP4f0zYDWuXg8NytR

### 18. 测试问答知识库分页查询 - 第2页
# @name testQaPagination2
GET https://ykwy-api.wuyoutansuo.com/api/v2/qa-knowledge-base/by-shop?shopId={{shopId}}&page=2&pageSize=10
Access-Token: PoNyP4f0zYDWuXg8NytR

### 19. 测试产品API分页查询 - 第1页
# @name testProductPagination1
GET https://ykwy-api.wuyoutansuo.com/api/v2/temp-product/search?shopId={{shopId}}&page=1&pageSize=20
Access-Token: PoNyP4f0zYDWuXg8NytR

### 20. AI推荐测试 - 创业者融资路演场景
# @name testAIRecommendations1
POST {{baseUrl}}/recommendations
Content-Type: application/json

{
  "conversation_history": [
    {
      "speaker": "customer",
      "message": "http://item.taobao.com/item.htm?id=953394282130"
    },
    {
      "speaker": "service",
      "message": "您好！看到您关注我们的AI PPT制作服务了～请问是要做什么类型的PPT呢？"
    },
    {
      "speaker": "customer",
      "message": "我要做融资路演PPT，你们能做吗？"
    },
    {
      "speaker": "service",
      "message": "亲，当然可以！融资路演PPT我们很专业的，您具体需要多少页？什么风格的？💼"
    },
    {
      "speaker": "customer",
      "message": "我们是做AI教育的初创公司，下个月要见投资人，大概需要20-25页"
    }
  ],
  "conversation_id": "test_conv_001",
  "brand_id": "ykwy",
  "customer_type": "创业者",
  "customer_personality": "目标明确，关注融资路演效果"
}

### 21. AI推荐测试 - 运动服装咨询场景
# @name testAIRecommendations2
POST {{baseUrl}}/recommendations
Content-Type: application/json

{
  "conversation_history": [
    {
      "speaker": "customer",
      "message": "你好，我想买件跑步外套"
    },
    {
      "speaker": "service",
      "message": "您好！欢迎来到轻功体育～我们有很多款跑步外套，请问您主要在什么环境下跑步呢？"
    },
    {
      "speaker": "customer",
      "message": "主要是晨跑，有时候会下雨，需要防水的"
    },
    {
      "speaker": "service",
      "message": "明白了！晨跑防水外套我们有几款很不错的，都是专业跑步设计。您平时穿什么尺码呢？"
    },
    {
      "speaker": "customer",
      "message": "L码，但是我比较关心价格，有优惠吗？"
    }
  ],
  "conversation_id": "test_conv_002",
  "brand_id": "qinggong",
  "shop_id": "{{shopId}}",
  "customer_type": "个人用户",
  "customer_personality": "注重性价比，关注实用性"
}

### 22. AI推荐测试 - 犹豫客户场景
# @name testAIRecommendations3
POST {{baseUrl}}/recommendations
Content-Type: application/json

{
  "conversation_history": [
    {
      "speaker": "customer",
      "message": "我需要做个公司介绍的PPT"
    },
    {
      "speaker": "service",
      "message": "好的！公司介绍PPT我们很擅长，请问大概需要多少页？用于什么场合呢？"
    },
    {
      "speaker": "customer",
      "message": "大概15页左右，用于客户拜访，要求比较正式"
    },
    {
      "speaker": "service",
      "message": "明白了！正式的商务风格，15页的公司介绍，我们可以做得很专业。您希望什么时候完成呢？"
    },
    {
      "speaker": "customer",
      "message": "下周要用，但是我还要考虑一下，你们的价格怎么样？"
    },
    {
      "speaker": "service",
      "message": "15页商务PPT我们报价是680元，包含设计和修改，您觉得怎么样？"
    },
    {
      "speaker": "customer",
      "message": "我先考虑考虑吧"
    }
  ],
  "conversation_id": "test_conv_003",
  "brand_id": "ykwy",
  "customer_type": "企业用户",
  "customer_personality": "谨慎决策，关注性价比"
}

### 23. AI推荐测试 - 简单对话场景
# @name testAIRecommendations4
POST {{baseUrl}}/recommendations
Content-Type: application/json

{
  "conversation_history": [
    {
      "speaker": "customer",
      "message": "你好"
    },
    {
      "speaker": "service",
      "message": "您好！欢迎来到轻功体育，请问有什么可以帮助您的吗？"
    }
  ],
  "conversation_id": "test_conv_004",
  "brand_id": "qinggong"
}

### 24. AI推荐测试 - 紧急需求场景
# @name testAIRecommendations5
POST {{baseUrl}}/recommendations
Content-Type: application/json

{
  "conversation_history": [
    {
      "speaker": "customer",
      "message": "我急需一个产品介绍PPT，明天就要用"
    },
    {
      "speaker": "service",
      "message": "您好！明天要用的话时间确实比较紧，不过我们可以加急处理。请问是什么产品的介绍PPT呢？"
    },
    {
      "speaker": "customer",
      "message": "是我们公司新开发的APP，需要给投资人看的"
    }
  ],
  "conversation_id": "test_conv_005",
  "brand_id": "ykwy",
  "customer_type": "创业者",
  "customer_personality": "时间紧急，决策快速"
}

### 25. 测试推荐接口错误处理 - 空对话历史
# @name testAIRecommendationsError1
POST {{baseUrl}}/recommendations
Content-Type: application/json

{
  "conversation_history": [],
  "conversation_id": "test_conv_empty",
  "brand_id": "qinggong"
}

### 26. 测试推荐接口错误处理 - 无效品牌ID
# @name testAIRecommendationsError2
POST {{baseUrl}}/recommendations
Content-Type: application/json

{
  "conversation_history": [
    {
      "speaker": "customer",
      "message": "你好"
    }
  ],
  "conversation_id": "test_conv_invalid",
  "brand_id": "invalid_brand"
}
