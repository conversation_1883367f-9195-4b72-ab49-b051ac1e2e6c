"""
数据加载器模块
"""
import json
import os
from typing import List, Dict, Any
from pathlib import Path

from llama_index.core import Document
from llama_index.core.schema import TextNode

from ..utils.config import settings


class DataLoader:
    """数据加载器"""

    def __init__(self, data_dir: str = None):
        self.data_dir = Path(data_dir or settings.data_dir)

    def load_product_data(self) -> List[Document]:
        """加载产品数据"""
        documents = []

        # 加载各产品的描述信息
        for product_dir in ["ai-ppt-maker", "ai-video-creator", "ai-writing-assistant"]:
            product_path = self.data_dir / product_dir
            if not product_path.exists():
                continue

            # 加载产品描述
            desc_file = product_path / "product-description.json"
            if desc_file.exists():
                with open(desc_file, 'r', encoding='utf-8') as f:
                    product_data = json.load(f)

                doc = Document(
                    text=self._format_product_description(product_data),
                    metadata={
                        "type": "product_description",
                        "product": product_dir,
                        "source": str(desc_file)
                    }
                )
                documents.append(doc)

            # 加载QA数据库
            qa_file = product_path / "qa-database.json"
            if qa_file.exists():
                with open(qa_file, 'r', encoding='utf-8') as f:
                    qa_data = json.load(f)

                for category, qas in qa_data.items():
                    if isinstance(qas, list):  # 确保是QA列表
                        for qa in qas:
                            if isinstance(qa, dict) and 'question' in qa and 'answer' in qa:
                                doc = Document(
                                    text=qa['question'],  # 只有客户的问题
                                    metadata={
                                        "type": "qa",
                                        "product": product_dir,
                                        "category": category,
                                        "stage": qa.get("stage", category),  # 销售阶段
                                        "purpose": qa.get("purpose", ""),  # 销售目的
                                        "keywords": qa.get("keywords", []),
                                        "answer": qa['answer'],  # 答案放在metadata中
                                        "source": str(qa_file)
                                    }
                                )
                                documents.append(doc)

        return documents

    def load_dialogue_data(self) -> List[Document]:
        """加载对话数据"""
        documents = []

        for product_dir in ["ai-ppt-maker", "ai-video-creator", "ai-writing-assistant"]:
            dialogue_file = self.data_dir / product_dir / "customer-dialogues.json"
            if not dialogue_file.exists():
                continue

            with open(dialogue_file, 'r', encoding='utf-8') as f:
                dialogues = json.load(f)

            for dialogue in dialogues:
                # 智能处理对话，考虑连续发言情况
                dialogue_turns = dialogue["dialogue"]

                # 将连续的客户发言合并，找到对应的客服回复
                i = 0
                while i < len(dialogue_turns):
                    if dialogue_turns[i]["speaker"] == "customer":
                        # 收集连续的客户发言
                        customer_messages = []
                        customer_turn_start = i

                        while i < len(dialogue_turns) and dialogue_turns[i]["speaker"] == "customer":
                            customer_messages.append(dialogue_turns[i]["message"])
                            i += 1

                        # 合并客户发言
                        combined_customer_message = " ".join(customer_messages)

                        # 查找对应的客服回复（可能是连续多条）
                        service_responses = []

                        while i < len(dialogue_turns) and dialogue_turns[i]["speaker"] == "service":
                            service_responses.append(dialogue_turns[i]["message"])
                            i += 1

                        # 合并客服回复
                        combined_service_response = " ".join(service_responses) if service_responses else ""

                        # 创建文档
                        doc = Document(
                            text=combined_customer_message,  # 合并后的客户发言
                            metadata={
                                "type": "dialogue",
                                "product": product_dir,
                                "dialogue_id": dialogue["dialogueId"],
                                "customer_type": dialogue["customerType"],
                                "customer_personality": dialogue["customerPersonality"],
                                "service_response": combined_service_response,  # 合并后的客服回复
                                "customer_turn_count": len(customer_messages),  # 客户发言条数
                                "service_turn_count": len(service_responses),   # 客服回复条数
                                "turn_start_index": customer_turn_start,
                                "order_status": dialogue.get("orderStatus", ""),
                                "source": str(dialogue_file)
                            }
                        )
                        documents.append(doc)
                    else:
                        # 跳过单独的客服发言（已经在上面处理过了）
                        i += 1

        return documents

    def load_sales_strategies(self) -> List[Document]:
        """加载销售策略数据"""
        documents = []

        strategies_file = self.data_dir / "common" / "sales-strategies.json"
        if not strategies_file.exists():
            return documents

        with open(strategies_file, 'r', encoding='utf-8') as f:
            strategies = json.load(f)

        for category, strategy_list in strategies.items():
            if isinstance(strategy_list, list):
                for strategy in strategy_list:
                    doc = Document(
                        text=f"问题: {strategy['question']}\n答案: {strategy['answer']}",
                        metadata={
                            "type": "sales_strategy",
                            "category": category,
                            "keywords": strategy.get("keywords", []),
                            "source": str(strategies_file)
                        }
                    )
                    documents.append(doc)

        return documents

    def _format_product_description(self, product_data: Dict[str, Any]) -> str:
        """格式化产品描述"""
        basic_info = product_data.get("basic_info", {})
        product_details = product_data.get("product_details", {})

        text = f"""
产品名称: {basic_info.get('service_name', '')}
产品类别: {basic_info.get('category', '')}
品牌: {basic_info.get('brand', '')}
标语: {basic_info.get('tagline', '')}
产品类型: {basic_info.get('service_type', '')}

主要功能:
{chr(10).join(f"- {feature}" for feature in basic_info.get('main_features', []))}

支持类型:
{chr(10).join(f"- {ptype}" for ptype in product_details.get('supported_ppt_types', product_details.get('supported_video_types', product_details.get('supported_content_types', []))))}
        """.strip()

        return text

    def _format_dialogue(self, dialogue: Dict[str, Any]) -> str:
        """格式化对话"""
        dialogue_text = f"客户类型: {dialogue['customerType']}\n"
        dialogue_text += f"客户性格: {dialogue['customerPersonality']}\n\n"
        dialogue_text += "对话内容:\n"

        for turn in dialogue["dialogue"]:
            speaker = "客户" if turn["speaker"] == "customer" else "客服"
            dialogue_text += f"{speaker}: {turn['message']}\n"

        return dialogue_text
