"""
统一日志系统 - 与 ykwy-assistant-api 保持一致
支持控制台输出、JSON格式输出和Loki日志收集
"""
import logging
import sys
import json
import os
import time
from typing import Any, Dict, Union, Optional
from .loki import loki_service, LogLevel


class UnifiedLogger:
    """统一日志器 - 支持控制台、JSON和Loki输出"""

    def __init__(self):
        """初始化统一日志器"""
        self.loki_service = loki_service

        # 根据环境变量配置输出方式
        node_env = os.environ.get('NODE_ENV', 'development')
        self.enable_console = True  # 始终启用控制台输出
        self.enable_json = node_env == 'production'  # 生产环境启用JSON输出
        self.enable_loki = loki_service.is_enabled()  # 只要Loki配置完整就启用

        # 日志级别映射
        self.level_map = {
            'error': LogLevel.ERROR,
            'warn': LogLevel.WARN,
            'info': LogLevel.INFO,
            'debug': LogLevel.DEBUG,
            'trace': LogLevel.TRACE
        }

    def _format_console_output(self, level: str, message: str, context: Optional[Dict[str, Any]] = None) -> str:
        """格式化控制台输出"""
        import time
        timestamp = time.strftime('%H:%M:%S')

        # 设置颜色
        colors = {
            'error': '\033[91m',  # 红色
            'warn': '\033[93m',   # 黄色
            'info': '\033[92m',   # 绿色
            'debug': '\033[94m',  # 蓝色
            'trace': '\033[37m'   # 白色
        }
        color = colors.get(level, '\033[0m')
        reset = '\033[0m'

        formatted = f"{color}[{timestamp}] [{level.upper()}] {message}{reset}"

        # 添加上下文信息
        if context:
            formatted += f"\n{color}Context: {json.dumps(context, ensure_ascii=False, indent=2)}{reset}"

        return formatted

    def _log(self, level: str, message: str, context: Optional[Dict[str, Any]] = None,
            error: Optional[Exception] = None, request_id: Optional[str] = None,
            user_id: Optional[str] = None, organization_id: Optional[str] = None) -> None:
        """内部日志方法"""

        # 控制台输出
        if self.enable_console:
            console_output = self._format_console_output(level, message, context)
            print(console_output)

        # JSON格式输出
        if self.enable_json:
            log_entry = {
                'timestamp': time.time(),
                'level': level,
                'message': message,
                'service': 'sales-agent'
            }
            if context:
                log_entry['context'] = context
            if request_id:
                log_entry['request_id'] = request_id
            if user_id:
                log_entry['user_id'] = user_id
            if organization_id:
                log_entry['organization_id'] = organization_id
            if error:
                log_entry['error'] = {
                    'name': type(error).__name__,
                    'message': str(error)
                }

            print(json.dumps(log_entry, ensure_ascii=False))

        # Loki输出
        if self.enable_loki:
            loki_level = self.level_map.get(level, LogLevel.INFO)
            # 检查是否使用同步发送（避免异步问题）
            # 默认使用同步模式，除非明确设置为异步
            use_async_loki = os.environ.get('LOKI_ASYNC_MODE', 'false').lower() == 'true'
            if use_async_loki:
                # 使用异步模式发送
                self.loki_service.send_log(loki_level, message, context, error, request_id, user_id, organization_id)
            else:
                # 使用同步模式发送（默认）
                self.loki_service.send_log_sync(loki_level, message, context, error, request_id, user_id, organization_id)

    def error(self, message: str, context: Optional[Dict[str, Any]] = None,
             error: Optional[Exception] = None, request_id: Optional[str] = None,
             user_id: Optional[str] = None, organization_id: Optional[str] = None) -> None:
        """记录错误日志"""
        self._log('error', message, context, error, request_id, user_id, organization_id)

    def warn(self, message: str, context: Optional[Dict[str, Any]] = None,
            request_id: Optional[str] = None, user_id: Optional[str] = None,
            organization_id: Optional[str] = None) -> None:
        """记录警告日志"""
        self._log('warn', message, context, None, request_id, user_id, organization_id)

    def info(self, message: str, context: Optional[Dict[str, Any]] = None,
            request_id: Optional[str] = None, user_id: Optional[str] = None,
            organization_id: Optional[str] = None) -> None:
        """记录信息日志"""
        self._log('info', message, context, None, request_id, user_id, organization_id)

    def debug(self, message: str, context: Optional[Dict[str, Any]] = None,
             request_id: Optional[str] = None, user_id: Optional[str] = None,
             organization_id: Optional[str] = None) -> None:
        """记录调试日志"""
        self._log('debug', message, context, None, request_id, user_id, organization_id)

    def trace(self, message: str, context: Optional[Dict[str, Any]] = None,
             request_id: Optional[str] = None, user_id: Optional[str] = None,
             organization_id: Optional[str] = None) -> None:
        """记录跟踪日志"""
        self._log('trace', message, context, None, request_id, user_id, organization_id)


# 创建全局统一日志器实例
unified_logger = UnifiedLogger()


class LokiHandler(logging.Handler):
    """自定义 Loki 日志处理器 - 将传统 logging 日志发送到 Loki"""

    def __init__(self):
        super().__init__()
        self.unified_logger = unified_logger

        # 日志级别映射
        self.level_mapping = {
            logging.ERROR: 'error',
            logging.WARNING: 'warn',
            logging.INFO: 'info',
            logging.DEBUG: 'debug',
            logging.NOTSET: 'trace'
        }

    def emit(self, record: logging.LogRecord):
        """发送日志记录到 Loki"""
        try:
            # 映射日志级别
            level_str = self.level_mapping.get(record.levelno, 'info')
            loki_level = self.unified_logger.level_map.get(level_str, LogLevel.INFO)

            # 获取原始消息（不格式化）
            message = record.getMessage()

            # 构建上下文信息
            context = {
                'module': record.name,
                'filename': record.filename,
                'lineno': record.lineno,
                'funcName': record.funcName
            }

            # 处理异常信息
            error = None
            if record.exc_info:
                error = record.exc_info[1] if record.exc_info[1] else Exception(record.getMessage())

            # 直接发送到 Loki（不通过控制台输出）
            if self.unified_logger.enable_loki:
                # 使用统一的发送逻辑（同步/异步选择）
                use_async_loki = os.environ.get('LOKI_ASYNC_MODE', 'false').lower() == 'true'
                if use_async_loki:
                    self.unified_logger.loki_service.send_log(loki_level, message, context, error)
                else:
                    self.unified_logger.loki_service.send_log_sync(loki_level, message, context, error)

        except Exception:
            # 静默处理错误，避免影响主业务
            pass


class DetailedFormatter(logging.Formatter):
    """自定义日志格式化器，用于更好地显示销售代理的处理流程"""

    def __init__(self):
        super().__init__()

    def format(self, record: logging.LogRecord) -> str:
        # 获取时间戳
        timestamp = self.formatTime(record, '%H:%M:%S')

        # 根据日志级别设置颜色
        if record.levelno >= logging.ERROR:
            color = '\033[91m'  # 红色
        elif record.levelno >= logging.WARNING:
            color = '\033[93m'  # 黄色
        elif record.levelno >= logging.INFO:
            color = '\033[92m'  # 绿色
        else:
            color = '\033[94m'  # 蓝色

        reset_color = '\033[0m'

        # 格式化消息
        message = record.getMessage()

        # 如果是工具调用相关的日志，添加特殊格式
        if '[工具调用]' in message:
            return f"{color}[{timestamp}] {message}{reset_color}"
        elif message.startswith('   '):  # 缩进的子日志
            return f"{color}[{timestamp}] {message}{reset_color}"
        elif message.startswith('='):  # 分隔线
            return f"{color}{message}{reset_color}"
        else:
            return f"{color}[{timestamp}] [{record.name}] {message}{reset_color}"


def setup_detailed_logging():
    """设置详细的日志记录"""

    # 创建根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)

    # 清除现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)

    # 设置自定义格式化器
    formatter = DetailedFormatter()
    console_handler.setFormatter(formatter)

    # 添加处理器到根日志记录器
    root_logger.addHandler(console_handler)

    # 添加 Loki 处理器（如果启用）
    if unified_logger.enable_loki:
        loki_handler = LokiHandler()
        loki_handler.setLevel(logging.INFO)
        # Loki 处理器不需要格式化器，因为它会直接处理原始消息
        root_logger.addHandler(loki_handler)
        print("🌐 Loki 日志处理器已启用")

    # 设置特定模块的日志级别
    logging.getLogger('src.sales_agent.core.agent').setLevel(logging.INFO)
    logging.getLogger('src.sales_agent.tools').setLevel(logging.INFO)
    logging.getLogger('src.sales_agent.api.app').setLevel(logging.INFO)

    # 降低第三方库的日志级别
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('openai').setLevel(logging.WARNING)
    logging.getLogger('llama_index').setLevel(logging.WARNING)
    logging.getLogger('uvicorn').setLevel(logging.INFO)

    print("🔧 详细日志记录已启用")
    if unified_logger.enable_loki:
        print(f"📡 Loki 服务: {unified_logger.loki_service.url}")
        print(f"🏷️ 服务标签: service=\"{unified_logger.loki_service.server_name}\"")


def log_tool_call(tool_name: str, **kwargs):
    """记录工具调用"""
    logger = logging.getLogger('sales_agent.tools')
    logger.info(f"🔧 [工具调用] {tool_name}")
    for key, value in kwargs.items():
        if isinstance(value, str) and len(value) > 100:
            value = value[:100] + "..."
        logger.info(f"   📝 {key}: {value}")


def log_tool_result(tool_name: str, result: Any, success: bool = True):
    """记录工具结果"""
    logger = logging.getLogger('sales_agent.tools')
    if success:
        if isinstance(result, str) and len(result) > 100:
            result_preview = result[:100] + "..."
        else:
            result_preview = str(result)
        logger.info(f"   ✅ {tool_name} 执行成功: {result_preview}")
    else:
        logger.error(f"   ❌ {tool_name} 执行失败: {result}")


def format_json_for_log(data: Union[Dict, list, str], max_length: int = 500, indent: int = 2) -> str:
    """
    格式化JSON/字典数据用于日志输出

    Args:
        data: 要格式化的数据（字典、列表或JSON字符串）
        max_length: 最大长度，超过会截断
        indent: 缩进空格数

    Returns:
        格式化后的字符串
    """
    try:
        # 如果是字符串，尝试解析为JSON
        if isinstance(data, str):
            try:
                data = json.loads(data)
            except json.JSONDecodeError:
                # 如果不是有效JSON，直接返回原字符串
                return data[:max_length] + "..." if len(data) > max_length else data

        # 格式化为JSON字符串
        formatted = json.dumps(data, ensure_ascii=False, indent=indent)

        # 如果太长，截断并添加省略号
        if len(formatted) > max_length:
            # 尝试找到合适的截断点（在换行符处）
            truncated = formatted[:max_length]
            last_newline = truncated.rfind('\n')
            if last_newline > max_length * 0.7:  # 如果换行符位置合理
                truncated = truncated[:last_newline]
            formatted = truncated + "\n  ... (内容已截断)"

        return formatted
    except Exception as e:
        # 如果格式化失败，返回字符串表示
        return f"<格式化失败: {str(e)}> {str(data)[:max_length]}"


def log_json_data(logger_instance: logging.Logger, message: str, data: Union[Dict, list, str],
                  level: str = "info", max_length: int = 500):
    """
    记录JSON数据到日志

    Args:
        logger_instance: 日志记录器实例
        message: 日志消息
        data: 要记录的数据
        level: 日志级别 (debug, info, warning, error)
        max_length: 最大长度
    """
    formatted_data = format_json_for_log(data, max_length)
    log_message = f"{message}:\n{formatted_data}"

    # 根据级别记录日志
    if level.lower() == "debug":
        logger_instance.debug(log_message)
    elif level.lower() == "info":
        logger_instance.info(log_message)
    elif level.lower() == "warning":
        logger_instance.warning(log_message)
    elif level.lower() == "error":
        logger_instance.error(log_message)
    else:
        logger_instance.info(log_message)


def log_agent_step(step: str, details: str = ""):
    """记录代理处理步骤"""
    logger = logging.getLogger('sales_agent.core')
    if details:
        logger.info(f"🤖 {step}: {details}")
    else:
        logger.info(f"🤖 {step}")


def log_user_interaction(message: str, response: str):
    """记录用户交互"""
    logger = logging.getLogger('sales_agent.interaction')
    logger.info("=" * 80)
    logger.info(f"👤 用户: {message}")
    logger.info(f"🤖 助手: {response}")
    logger.info("=" * 80)


# ==================== 便捷日志函数（向后兼容） ====================

def log_info(message: str, context: Optional[Dict[str, Any]] = None, request_id: Optional[str] = None):
    """记录信息日志（便捷函数）"""
    unified_logger.info(message, context, request_id)

def log_error(message: str, context: Optional[Dict[str, Any]] = None, error: Optional[Exception] = None, request_id: Optional[str] = None):
    """记录错误日志（便捷函数）"""
    unified_logger.error(message, context, error, request_id)

def log_warn(message: str, context: Optional[Dict[str, Any]] = None, request_id: Optional[str] = None):
    """记录警告日志（便捷函数）"""
    unified_logger.warn(message, context, request_id)

def log_debug(message: str, context: Optional[Dict[str, Any]] = None, request_id: Optional[str] = None):
    """记录调试日志（便捷函数）"""
    unified_logger.debug(message, context, request_id)

def log_api_call(api_name: str, params: Optional[Dict[str, Any]] = None, request_id: Optional[str] = None):
    """记录API调用"""
    unified_logger.info(f"🔌 API调用: {api_name}", {"params": params}, request_id)

def log_api_response(api_name: str, success: bool, response_data: Optional[Any] = None, error: Optional[Exception] = None, request_id: Optional[str] = None):
    """记录API响应"""
    if success:
        unified_logger.info(f"✅ API成功: {api_name}", {"response": response_data}, request_id)
    else:
        unified_logger.error(f"❌ API失败: {api_name}", {"response": response_data}, error, request_id)

def log_tool_call_unified(tool_name: str, params: Optional[Dict[str, Any]] = None, request_id: Optional[str] = None):
    """记录工具调用（统一版本）"""
    unified_logger.info(f"🔧 工具调用: {tool_name}", {"params": params}, request_id)

def log_tool_result_unified(tool_name: str, success: bool, result: Optional[Any] = None, error: Optional[Exception] = None, request_id: Optional[str] = None):
    """记录工具结果（统一版本）"""
    if success:
        unified_logger.info(f"✅ 工具成功: {tool_name}", {"result": result}, request_id)
    else:
        unified_logger.error(f"❌ 工具失败: {tool_name}", {"result": result}, error, request_id)

def log_user_interaction_unified(user_message: str, agent_response: str, request_id: Optional[str] = None):
    """记录用户交互（统一版本）"""
    unified_logger.info("👤 用户消息", {"message": user_message}, request_id)
    unified_logger.info("🤖 助手回复", {"response": agent_response}, request_id)
