"""
轻功体育销售工具集
"""
import logging
from typing import List, Dict, Any
from llama_index.core.tools import FunctionTool

from .base_tools import BaseTools
from .order_tools import OrderTools
from .commerce_tools import CommerceTools
from .coupon_tools import CouponTools
from ..data_loader.qinggong_loader import QinggongDataLoader
from .brand_vector_tools import brand_vector_factory

logger = logging.getLogger(__name__)


class QinggongSalesTools(BaseTools):
    """轻功体育销售工具集"""

    def __init__(self):
        super().__init__()
        self.brand_name = "轻功体育"
        self.data_loader = QinggongDataLoader()
        self.products = self.data_loader.products

        # 初始化千牛API工具（复用原有实现）
        self.order_tools = OrderTools()
        self.commerce_tools = CommerceTools()
        self.coupon_tools = CouponTools()

        # 获取轻功体育专用向量工具
        self.vector_tools = brand_vector_factory.get_brand_tools('qinggong')

    def set_connection_info(self, connection_id: str, customer_id: str, customer_nick: str = None):
        """设置连接信息，传递给千牛API工具"""
        # 设置千牛API工具的连接信息
        self.order_tools.set_connection_info(connection_id, customer_id, customer_nick)
        self.commerce_tools.set_connection_info(connection_id, customer_id, customer_nick)
        self.coupon_tools.set_connection_info(connection_id, customer_id, customer_nick)

    def get_all_tools(self) -> List[FunctionTool]:
        """获取所有轻功体育销售工具"""
        tools = []

        # 向量查询工具（轻功专用）- 包含智能产品搜索功能
        tools.extend(self._get_vector_tools())

        # 订单管理工具
        tools.extend(self._get_order_tools())

        # 商务交易工具
        tools.extend(self._get_commerce_tools())

        return tools



    def _get_vector_tools(self) -> List[FunctionTool]:
        """获取轻功体育向量查询工具"""
        return self.vector_tools.get_all_tools()

    def _get_order_tools(self) -> List[FunctionTool]:
        """获取订单管理工具（简化实现）"""
        return [
            FunctionTool.from_defaults(
                fn=self.get_all_orders,
                name="get_all_orders",
                description="查询客户在轻功体育的订单列表。当客户询问'我的订单'、'订单查询'时使用。"
            ),
            FunctionTool.from_defaults(
                fn=self.get_order_details,
                name="get_order_details",
                description="查询轻功体育订单的详细信息。参数：order_id（订单号）"
            ),
            FunctionTool.from_defaults(
                fn=self.get_order_logistics,
                name="get_order_logistics",
                description="查询轻功体育订单的物流信息。参数：order_id（订单号）"
            )
        ]

    def _get_commerce_tools(self) -> List[FunctionTool]:
        """获取商务交易工具（简化实现）"""
        return [
            FunctionTool.from_defaults(
                fn=self.send_item_card,
                name="send_item_card",
                description="发送轻功体育商品卡片给客户。参数：product_ids（产品ID列表）"
            ),
            FunctionTool.from_defaults(
                fn=self.send_purchase_link,
                name="send_purchase_link",
                description="发送轻功体育商品的购买链接。参数：product_id（产品ID）"
            )
        ]

    def _get_coupon_tools(self) -> List[FunctionTool]:
        """获取优惠券工具（简化实现）"""
        return [
            FunctionTool.from_defaults(
                fn=self.get_shop_coupons,
                name="get_shop_coupons",
                description="获取轻功体育店铺的所有可用优惠券"
            ),
            FunctionTool.from_defaults(
                fn=self.send_coupon,
                name="send_coupon",
                description="发送轻功体育优惠券给客户。参数：coupon_id（优惠券ID）"
            )
        ]






    # 订单管理工具实现（调用千牛API）
    def get_all_orders(self) -> str:
        """获取客户订单列表（调用千牛API）"""
        logger.info("🏃 [轻功体育] 调用千牛API获取订单列表")
        return self.order_tools.get_all_orders()

    def get_order_details(self, order_id: str) -> str:
        """获取订单详情（调用千牛API）"""
        logger.info(f"🏃 [轻功体育] 调用千牛API获取订单详情: {order_id}")
        return self.order_tools.get_order_details(order_id)

    def get_order_logistics(self, order_id: str) -> str:
        """获取物流信息（调用千牛API）"""
        logger.info(f"🏃 [轻功体育] 调用千牛API获取物流信息: {order_id}")
        return self.order_tools.get_order_logistics(order_id)

    # 商务交易工具实现（调用千牛API）
    def send_item_card(self, product_ids: List[str]) -> str:
        """发送商品卡片（调用千牛API）"""
        logger.info(f"🏃 [轻功体育] 调用千牛API发送商品卡片: {product_ids}")
        return self.commerce_tools.send_item_card(product_ids)

    def send_purchase_link(self, product_id: str) -> str:
        """发送购买链接（调用千牛API）"""
        logger.info(f"🏃 [轻功体育] 调用千牛API发送购买链接: {product_id}")
        return self.commerce_tools.send_purchase_link(product_id)

    # 优惠券工具实现（调用千牛API）
    def get_shop_coupons(self) -> str:
        """获取店铺优惠券（调用千牛API）"""
        logger.info("🏃 [轻功体育] 调用千牛API获取店铺优惠券")
        return self.coupon_tools.get_shop_coupons()

    def send_coupon(self, coupon_id: str) -> str:
        """发送优惠券（调用千牛API）"""
        logger.info(f"🏃 [轻功体育] 调用千牛API发送优惠券: {coupon_id}")
        return self.coupon_tools.send_coupon(coupon_id)
