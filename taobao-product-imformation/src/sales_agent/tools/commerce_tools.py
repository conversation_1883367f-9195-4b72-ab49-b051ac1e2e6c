"""
商务工具类 - 提供商品卡片和下单链接相关功能
"""
import logging
import asyncio
import json
from typing import List, Dict, Any, Optional
from .base_tools import BaseTools
from ..services.qianniu_client import qianniu_client

logger = logging.getLogger(__name__)

class CommerceTools(BaseTools):
    """商务工具类 - 提供商品卡片和下单链接相关功能"""
    
    def send_item_card(self, item_ids: List[str]) -> str:
        """发送商品卡片给客户

        Args:
            item_ids: 商品ID列表，必填参数，最多3个，可以是列表、单个ID或JSON字符

        Returns:
            发送结果信息
        """
        logger.info(f"🔧 [工具调用] send_item_card")
        logger.info(f"   📥 输入参数: item_ids='{item_ids}',")

        # 检查必填参数
        if not item_ids:
            logger.error("   ❌ 未提供商品ID")
            return "发送商品卡片失败，请提供商品ID参数"

        # 使用参数提供的用户加密ID或当前客户ID
        customer_id = self.current_customer_id
        connection_id = self.current_connection_id

        # 检查必要参数
        if not connection_id:
            logger.error("   ❌ 未设置千牛连接ID")
            return "无法发送商品卡片，未配置千牛连接"

        if not customer_id:
            logger.error("   ❌ 未设置客户ID")
            return "无法发送商品卡片，未获取到客户信息"

        # 处理不同格式的item_ids输入
        if isinstance(item_ids, str):
            # 如果是JSON字符串，尝试解析
            try:
                import json
                item_ids = json.loads(item_ids)
            except:
                # 如果不是JSON，按逗号分割
                item_ids = [id.strip() for id in item_ids.split(',')]
        elif not isinstance(item_ids, list):
            # 如果是单个值，转换为列表
            item_ids = [item_ids]

        # 过滤空值
        item_ids = [item_id for item_id in item_ids if item_id and str(item_id).strip()]

        # 转换为字符串类型
        item_ids = [str(item_id) for item_id in item_ids]

        # 最多发送3个商品
        item_ids = item_ids[:3]
        logger.info(f"   📦 准备发送商品: {item_ids}")

        try:
            # 使用qianniu_client发送商品卡片
            logger.info(f"   🌐 调用千牛API发送商品卡片")

            # 调用千牛API发送商品卡片
            try:
                # 检查是否已经在事件循环中
                asyncio.get_running_loop()
                # 如果已经在事件循环中，在新线程中运行
                import concurrent.futures

                def run_in_thread():
                    return asyncio.run(qianniu_client.send_item_card(
                        connection_id=connection_id,
                        customer_id=customer_id,
                        item_ids=item_ids,
                        card_type=-1
                    ))

                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_in_thread)
                    response = future.result()
            except RuntimeError:
                # 没有运行的事件循环，可以直接使用 asyncio.run
                response = asyncio.run(qianniu_client.send_item_card(
                    connection_id=connection_id,
                    customer_id=customer_id,
                    item_ids=item_ids,
                    card_type=-1
                ))

            logger.info(f"   📦 千牛API返回结果: {response}")

            if response.send_card:
                logger.info(f"   ✅ 发送商品卡片成功")
                if response.send_url:
                    return f"已为您发送商品卡片，商品链接：{response.send_url}。请在聊天窗口中查看。如有任何问题，请随时咨询我。"
                else:
                    return "已为您发送商品卡片，请在聊天窗口中查看。如有任何问题，请随时咨询我。"
            else:
                logger.error(f"   ❌ 发送商品卡片失败: {response}")
                logger.info(f"   🔄 启动备用方案...")
                # 当千牛API网络异常时，提供备用方案
                fallback_result = self._provide_service_links_fallback(item_ids)
                logger.info(f"   ✅ 备用方案完成")
                return fallback_result

        except Exception as e:
            logger.error(f"   ❌ 发送商品卡片出错: {str(e)}")
            import traceback
            logger.error(f"   ❌ 异常详情: {traceback.format_exc()}")
            # 发生异常时提供备用方案
            return self._provide_service_links_fallback(item_ids)

    def _provide_service_links_fallback(self, item_ids: List[str]) -> str:
        """当千牛API失败时的备用方案 - 直接提供服务链接"""
        logger.info(f"   🔄 使用备用方案提供服务链接，商品IDs: {item_ids}")

        try:
            # 获取服务详情作为备用
            from ..services.product_api_client import product_api_client
            import asyncio

            # 使用asyncio.run来在同步函数中调用异步函数
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                products = loop.run_until_complete(product_api_client.get_all_products())
            finally:
                loop.close()

            if not products:
                logger.warning(f"   ⚠️ 未获取到产品信息")
                return "抱歉，暂时无法发送商品卡片，请稍后再试。您可以直接咨询我们的服务详情。"

            logger.info(f"   📦 获取到 {len(products)} 个产品")

            # 匹配请求的商品ID
            matched_services = []
            for product in products:
                if product.item_id in item_ids:
                    service_url = f"https://item.taobao.com/item.htm?id={product.item_id}"
                    matched_services.append(f"• {product.name}\n  {service_url}")
                    logger.info(f"   ✅ 匹配到服务: {product.name} (ID: {product.item_id})")

            logger.info(f"   📋 匹配到 {len(matched_services)} 个服务")

            if matched_services:
                result = "抱歉，商品卡片发送失败，为您提供直接链接：\n\n" + "\n\n".join(matched_services)
                result += "\n\n点击链接可直接查看服务详情和下单～"
                logger.info(f"   ✅ 备用方案生成成功")
                return result
            else:
                logger.warning(f"   ⚠️ 未找到匹配的服务")
                return "抱歉，暂时无法发送商品卡片，请稍后再试。您可以直接咨询我们的服务详情。"

        except Exception as e:
            logger.error(f"   ❌ 备用方案也失败: {e}")
            return "抱歉，暂时无法发送商品卡片，请稍后再试。您可以直接咨询我们的服务详情。"

    def send_purchase_link(self, item_id: str, sku_id: str = "5208588653629", quantity: int = 1) -> str:
        """发送下单链接给客户

        Args:
            item_id: 商品ID，必填参数
            sku_id: 商品SKU ID，默认值为 "5208588653629"
            quantity: 商品数量，默认为1

        Returns:
            发送结果信息
        """
        logger.info(f"🔧 [工具调用] send_purchase_link")
        logger.info(f"   📥 输入参数: item_id='{item_id}', sku_id='{sku_id}', quantity={quantity}")

        # 检查必填参数
        if not item_id:
            logger.error("   ❌ 未提供商品ID")
            return "发送下单链接失败，请提供商品ID参数"

        # 检查必要参数
        connection_id = self.current_connection_id
        customer_id = self.current_customer_id
        customer_nick = self.current_customer_nick

        if not connection_id:
            logger.error("   ❌ 未设置千牛连接ID")
            return "无法发送下单链接，未配置千牛连接"

        if not customer_id:
            logger.error("   ❌ 未设置客户ID")
            return "无法发送下单链接，未获取到客户信息"

        if not customer_nick:
            logger.error("   ❌ 未设置客户昵称")
            return "无法发送下单链接，未获取到客户信息"

        # 构建商品项 - 确保itemId和skuId是数字类型
        item_props = [{
            "itemId": int(item_id),  # 转换为数字
            "skuId": int(sku_id),    # 转换为数字
            "quantity": quantity,
            "context": {}
        }]

        # 转换为JSON字符串
        item_props_json = json.dumps(item_props)
        logger.info(f"   📦 构建的itemProps: {item_props_json}")

        try:
            # 使用qianniu_client发送下单链接
            logger.info(f"   🌐 调用千牛API发送下单链接")

            # 调用千牛API发送下单链接
            try:
                # 检查是否已经在事件循环中
                asyncio.get_running_loop()
                # 如果已经在事件循环中，在新线程中运行
                import concurrent.futures

                def run_in_thread():
                    return asyncio.run(qianniu_client.invite_order(
                        connection_id=connection_id,
                        customer_id=customer_id,
                        item_props=item_props_json,
                        buyer_nick=customer_nick,
                        biz_domain="taobao",
                        encrypt_type="internal"
                    ))

                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_in_thread)
                    response = future.result()
            except RuntimeError:
                # 没有运行的事件循环，可以直接使用 asyncio.run
                response = asyncio.run(qianniu_client.invite_order(
                    connection_id=connection_id,
                    customer_id=customer_id,
                    item_props=item_props_json,
                    buyer_nick=customer_nick, 
                    biz_domain="taobao",
                    encrypt_type="internal"
                ))

            logger.info(f"   📦 千牛API返回结果: {response}")

            if response.success:
                logger.info(f"   ✅ 发送下单链接成功")
                if response.order_url:
                    return f"已为您生成下单链接：{response.order_url}\n\n商品ID：{item_id}\n数量：{quantity}件\n\n点击链接即可直接下单～"
                else:
                    return f"已为您生成下单链接，请在聊天窗口中查看。\n\n商品ID：{item_id}\n数量：{quantity}件\n\n如有任何问题，请随时咨询我。"
            else:
                logger.error(f"   ❌ 发送下单链接失败: {response}")
                # 当千牛API失败时，提供备用链接
                return self._provide_purchase_link_fallback(item_id, quantity)

        except Exception as e:
            logger.error(f"   ❌ 发送下单链接出错: {str(e)}")
            import traceback
            logger.error(f"   ❌ 异常详情: {traceback.format_exc()}")
            # 发生异常时提供备用链接
            return self._provide_purchase_link_fallback(item_id, quantity)

    def _provide_purchase_link_fallback(self, item_id: str, quantity: int) -> str:
        """当千牛API失败时的备用方案 - 直接提供购买链接"""
        logger.info(f"   🔄 使用备用方案提供购买链接")

        try:
            # 获取服务详情
            from ..services.product_api_client import product_api_client
            import asyncio

            # 使用asyncio.run来在同步函数中调用异步函数
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                products = loop.run_until_complete(product_api_client.get_all_products())
            finally:
                loop.close()

            # 查找匹配的服务
            service_name = "该服务"
            for product in products:
                if product.item_id == item_id:
                    service_name = product.name
                    break

            # 生成直接购买链接
            purchase_url = f"https://item.taobao.com/item.htm?id={item_id}"

            return f"抱歉，下单链接发送失败，为您提供直接购买链接：\n\n{purchase_url}\n\n服务：{service_name}\n数量：{quantity}件\n\n点击链接可直接购买～"

        except Exception as e:
            logger.error(f"   ❌ 备用方案也失败: {e}")
            return f"抱歉，暂时无法生成下单链接，请稍后再试。\n\n您也可以直接访问：https://item.taobao.com/item.htm?id={item_id}"