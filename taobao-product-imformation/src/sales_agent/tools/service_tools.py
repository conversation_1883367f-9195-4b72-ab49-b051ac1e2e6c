"""
服务工具类 - 提供服务相关的工具函数
"""
import logging
import asyncio
import re
import json
from typing import List, Dict, Any
from .base_tools import BaseTools
from ..services.product_api_client import product_api_client

logger = logging.getLogger(__name__)

class ServiceTools(BaseTools):
    """服务工具类 - 提供服务查询和展示功能"""

    def identify_service_link(self, user_message: str) -> str:
        """识别用户发送的服务链接并返回对应服务信息"""
        logger.info(f"🔧 [工具调用] identify_service_link")
        logger.info(f"   📝 检查消息: {user_message}")

        # 检查是否包含服务链接，提取item_id
        import re

        # 匹配淘宝服务链接中的item_id
        patterns = [
            r'item\.taobao\.com/item\.htm\?.*?id=(\d+)',
            r'detail\.tmall\.com/item\.htm\?.*?id=(\d+)',
            r'item_id[=:](\d+)',
            r'id[=:](\d+)'
        ]

        for pattern in patterns:
            match = re.search(pattern, user_message)
            if match:
                item_id = match.group(1)
                logger.info(f"   ✅ 识别到服务ID: {item_id}")

                # 调用get_service_details获取详细信息
                return self.get_service_details(item_id)

        logger.info("   ❌ 未识别到有效的服务链接")
        return "抱歉，我没有识别到有效的服务链接。请发送正确的淘宝服务链接，我来为您介绍服务详情～"

    def get_service_list(self) -> str:
        """获取服务列表 - 通过API获取所有服务"""
        logger.info("🔧 [工具调用开始] get_service_list")
        logger.info("   📥 参数: 无")

        try:
            # 使用asyncio.run来在同步函数中调用异步函数
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                products = loop.run_until_complete(
                    product_api_client.get_all_products()
                )
            finally:
                loop.close()

            if not products:
                result = "未找到服务信息"
                logger.info(f"   📤 返回: {result}")
                logger.info("🏁 [工具调用结束] get_service_list")
                return result

            service_list = []
            for service in products:  # API返回的仍然叫products，但我们理解为services
                service_list.append(f"• [{service.item_id}] {service.name}: {service.description}")

            result = "我们提供的服务包括：\n" + "\n".join(service_list)

            # 日志输出时截断长内容
            result_preview = result[:200] + "..." if len(result) > 200 else result
            logger.info(f"   📤 返回: {result_preview}")
            logger.info(f"   ✅ 成功获取 {len(products)} 个服务")
            logger.info("🏁 [工具调用结束] get_service_list")
            return result

        except Exception as e:
            logger.error(f"   ❌ 获取服务列表失败: {e}")
            logger.info("🏁 [工具调用结束] get_service_list - 失败")
            return "查询失败"


    def get_service_details(self, item_id: str) -> str:
        """获取具体服务详情 - 通过item_id查询服务详细信息"""
        logger.info("🔧 [工具调用开始] get_service_details")
        logger.info(f"   📥 参数: item_id={item_id}")

        try:
            # 使用asyncio.run来在同步函数中调用异步函数
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                # 直接调用API获取完整的description数据
                raw_data = loop.run_until_complete(
                    product_api_client._make_request(
                        "GET",
                        "/api/v2/temp-product/search",
                        params={
                            "query": item_id,
                            "shopId": product_api_client.shop_id
                        }
                    )
                )
            finally:
                loop.close()

            data_list = raw_data.get("data", [])
            if not data_list:
                result = "未找到服务详情"
                logger.info(f"   📤 返回: {result}")
                logger.info("🏁 [工具调用结束] get_service_details")
                return result

            # 获取第一个匹配项的description字段
            description = data_list[0].get("description", {})
            service_name = description.get("basic_info", {}).get("service_name", "未知服务")

            result = f"服务详情：\n{json.dumps(description, ensure_ascii=False, indent=2)}"

            # 日志输出时只显示服务名称和JSON长度
            logger.info(f"   📤 返回: 服务详情JSON (服务: {service_name}, 长度: {len(result)} 字符)")
            logger.info(f"   ✅ 成功获取服务详情: {item_id}")
            logger.info("🏁 [工具调用结束] get_service_details")
            return result

        except Exception as e:
            logger.error(f"   ❌ 获取服务详情失败: {e}")
            logger.info("🏁 [工具调用结束] get_service_details - 失败")
            return "查询失败"

    def get_similar_dialogues(self, user_message: str) -> str:
        """从真实对话中查找相似问题的回答作为参考"""
        logger.info("🔧 [工具调用开始] get_similar_dialogues")
        logger.info(f"   📥 参数: user_message='{user_message[:50]}{'...' if len(user_message) > 50 else ''}'")

        if not self.index:
            logger.error("   ❌ 索引不存在")
            result = "未找到相关对话"
            logger.info(f"   📤 返回: {result}")
            logger.info("🏁 [工具调用结束] get_similar_dialogues - 失败")
            return result

        try:
            from llama_index.core.vector_stores.types import MetadataFilters, MetadataFilter, FilterOperator

            filters = MetadataFilters(
                filters=[
                    MetadataFilter(key="type", value="dialogue", operator=FilterOperator.EQ)
                ]
            )

            query_engine = self.index.as_query_engine(
                similarity_top_k=5,
                filters=filters
            )

            logger.info(f"   🔍 检索相似的真实对话，查询: '{user_message}'")
            logger.info(f"   🔍 查询长度: {len(user_message)} 字符")
            nodes = query_engine.retrieve(user_message)
            logger.info(f"   ✅ 检索完成")

            logger.info(f"   📊 检索结果: 找到 {len(nodes)} 个节点")

            if not nodes:
                logger.info("   ❌ 未找到相似对话")
                # 尝试不使用过滤器检索
                logger.info("   🔄 尝试不使用过滤器检索...")
                query_engine_no_filter = self.index.as_query_engine(similarity_top_k=3)
                nodes_no_filter = query_engine_no_filter.retrieve(user_message)
                logger.info(f"   📊 无过滤器检索结果: 找到 {len(nodes_no_filter)} 个节点")

                if nodes_no_filter:
                    for i, node in enumerate(nodes_no_filter[:3]):
                        node_type = node.metadata.get("type", "未知")
                        logger.info(f"   📄 节点{i+1} 类型: {node_type}")

                return "未找到相似的对话参考"

            references = []
            for i, node in enumerate(nodes[:5], 1):
                # 详细日志记录节点信息
                logger.info(f"   📄 对话节点{i} 详情:")
                logger.info(f"      文本: {node.text[:100]}...")
                logger.info(f"      元数据: {node.metadata}")

                # 客户消息在node.text中，客服回复在metadata中
                customer_msg = node.text  # 客户消息就是节点文本
                service_response = node.metadata.get("service_response", "")
                similarity = f"{node.score:.3f}" if hasattr(node, 'score') else "N/A"

                logger.info(f"      客户消息: {customer_msg[:50]}...")
                logger.info(f"      客服回复: {service_response[:50]}...")

                if customer_msg and service_response:
                    references.append(f"""
参考{i} (相似度: {similarity}):
客户: {customer_msg}
客服: {service_response}
""".strip())

            if references:
                result = "真实对话参考:\n\n" + "\n\n".join(references)
                result_preview = result[:200] + "..." if len(result) > 200 else result
                logger.info(f"   📤 返回: {result_preview}")
                logger.info(f"   ✅ 找到 {len(references)} 条参考对话")
                logger.info("🏁 [工具调用结束] get_similar_dialogues")
                return result
            else:
                result = "对话数据格式不正确，无法提供参考"
                logger.info(f"   📤 返回: {result}")
                logger.info("🏁 [工具调用结束] get_similar_dialogues - 无有效数据")
                return result

        except Exception as e:
            logger.error(f"   ❌ 检索对话时出错: {str(e)}")
            result = f"检索对话时出错: {str(e)}"
            logger.info(f"   📤 返回: {result}")
            logger.info("🏁 [工具调用结束] get_similar_dialogues - 异常")
            return result