"""
订单工具类 - 提供订单管理相关的工具函数
"""
import logging
import asyncio
import json
from typing import List, Dict, Any
from .base_tools import BaseTools
from ..services.qianniu_client import qianniu_client

logger = logging.getLogger(__name__)

class OrderTools(BaseTools):
    """订单工具类 - 提供订单查询和管理功能"""
    
    def get_all_orders(self) -> str:
        """
        获取客户的所有订单列表

        Returns:
            订单列表的格式化字符串
        """
        try:
            logger.info("🔧 [工具调用开始] get_all_orders")
            logger.info(f"   📥 参数: connection_id={self.current_connection_id}, customer_id={self.current_customer_id}")

            # 检查是否有连接ID和客户ID
            if not self.current_connection_id or not self.current_customer_id:
                logger.warning("   ⚠️ 缺少连接ID或客户ID，无法查询真实订单")
                result = "未找到订单信息"
                logger.info(f"   📤 返回: {result}")
                logger.info("🏁 [工具调用结束] get_all_orders - 缺少参数")
                return result

            # 调用千牛API获取真实订单数据
            try:
                # 使用asyncio.run来在同步函数中调用异步函数
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    orders_result = loop.run_until_complete(
                        qianniu_client.query_recent_orders(self.current_connection_id, self.current_customer_id)
                    )
                finally:
                    loop.close()

                if not orders_result.orders:
                    result = "未找到订单"
                    logger.info(f"   📤 返回: {result}")
                    logger.info("🏁 [工具调用结束] get_all_orders - 无订单")
                    return result

                result = "📋 订单列表：\n\n"
                for order in orders_result.orders[:10]:  # 最多显示10个订单
                    # 获取主要商品信息
                    main_item = order.items[0] if order.items else None
                    item_title = main_item.auction_title if main_item else "未知商品"

                    result += f"🛍️ 订单号：{order.biz_order_id}\n"
                    result += f"📦 商品：{item_title}\n"
                    result += f"💰 金额：¥{order.order_price}\n"
                    result += f"📊 状态：{order.card_type_text}\n"
                    result += f"🕐 下单时间：{order.create_time}\n"
                    if len(order.items) > 1:
                        result += f"📝 共{len(order.items)}件商品\n"
                    result += "─" * 30 + "\n"

                # 日志输出时截断长内容
                result_preview = result[:300] + "..." if len(result) > 300 else result
                logger.info(f"   📤 返回: {result_preview}")
                logger.info(f"   ✅ 成功获取 {len(orders_result.orders[:10])} 个订单")
                logger.info("🏁 [工具调用结束] get_all_orders")
                return result

            except Exception as api_error:
                logger.error(f"   ❌ 千牛API调用失败: {api_error}")
                result = "查询失败"
                logger.info(f"   📤 返回: {result}")
                logger.info("🏁 [工具调用结束] get_all_orders - API失败")
                return result

        except Exception as e:
            logger.error(f"   ❌ 获取订单列表失败: {e}")
            result = "查询失败"
            logger.info(f"   📤 返回: {result}")
            logger.info("🏁 [工具调用结束] get_all_orders - 异常")
            return result

    
    def get_order_details(self, order_id: str) -> str:
        """
        获取订单详细信息

        Args:
            order_id: 订单号

        Returns:
            订单详情的格式化字符串
        """
        try:
            logger.info(f"🔧 [工具调用] get_order_details")
            logger.info(f"   🔍 查询订单详情，订单号: {order_id}")

            # 检查是否有连接ID和客户ID
            if not self.current_connection_id or not self.current_customer_id:
                logger.warning("   ⚠️ 缺少连接ID或客户ID，无法查询真实订单")
                return "未找到订单详情"

            # 使用订单解密接口获取详细信息
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    decrypt_result = loop.run_until_complete(
                        qianniu_client.decrypt_order(self.current_connection_id, order_id)
                    )
                finally:
                    loop.close()

                # 解析订单解密结果
                if not decrypt_result.get('success', False):
                    result = f"订单解密失败：{decrypt_result.get('error', '未知错误')}"
                    logger.info(f"   📤 返回: {result}")
                    logger.info("🏁 [工具调用结束] get_order_details - 解密失败")
                    return result

                # 从解密结果中提取订单数据
                try:
                    # 根据调试日志显示的实际数据结构：data.data.result
                    level1_data = decrypt_result.get('data', {})
                    level2_data = level1_data.get('data', {})
                    order_data = level2_data.get('result', {})

                    logger.info(f"   🔍 数据解析调试:")
                    logger.info(f"      level1_data keys: {list(level1_data.keys()) if level1_data else 'None'}")
                    logger.info(f"      level2_data keys: {list(level2_data.keys()) if level2_data else 'None'}")
                    logger.info(f"      order_data keys: {list(order_data.keys()) if order_data else 'None'}")

                    if not order_data:
                        logger.warning(f"   ⚠️ 无法从result字段获取订单数据")

                except (KeyError, TypeError) as e:
                    logger.error(f"   ❌ 订单数据解析失败: {e}")
                    result = f"订单数据格式异常，无法解析订单详情"
                    logger.info(f"   📤 返回: {result}")
                    logger.info("🏁 [工具调用结束] get_order_details - 数据格式异常")
                    return result
                # 构建订单详情（使用解密接口返回的原始数据）
                result = f"📋 订单详情\n\n"
                result += f"🛍️ 订单号：{order_id}\n"

                # 提取关键信息并格式化显示
                if order_data:
                    # 收货人信息
                    name = order_data.get('name', '未知')
                    mobile = order_data.get('mobile', '未知')
                    full_address = order_data.get('fullAddress', '未知')

                    result += f"👤 收货人：{name}\n"
                    result += f"� 手机号：{mobile}\n"
                    result += f"📍 收货地址：{full_address}\n"

                    # 订单状态
                    order_status = order_data.get('orderStatus', 0)
                    status_text = {
                        0: "待付款",
                        1: "待发货",
                        2: "待收货",
                        3: "待评价",
                        4: "交易成功",
                        5: "交易关闭",
                        6: "交易完成"
                    }.get(order_status, f"状态码{order_status}")

                    result += f"📊 订单状态：{status_text}\n"

                    # 隐私保护信息
                    privacy_protection = order_data.get('privacyProtection', False)
                    if privacy_protection:
                        result += f"🔒 隐私保护：已启用\n"

                    # 解密天数
                    decrypt_days = order_data.get('decryptDaysAfterOrderEnd', 0)
                    if decrypt_days > 0:
                        result += f"⏰ 解密期限：订单结束后{decrypt_days}天\n"
                else:
                    result += "⚠️ 无法获取详细订单信息\n"

                # 日志输出时截断长内容
                result_preview = result[:300] + "..." if len(result) > 300 else result
                logger.info(f"   📤 返回: {result_preview}")
                logger.info(f"   ✅ 成功获取订单详情: {order_id}")
                logger.info("🏁 [工具调用结束] get_order_details")
                return result

            except Exception as api_error:
                logger.error(f"   ❌ 千牛API调用失败: {api_error}")
                result = "查询失败"
                logger.info(f"   📤 返回: {result}")
                logger.info("🏁 [工具调用结束] get_order_details - API失败")
                return result

        except Exception as e:
            logger.error(f"   ❌ 获取订单详情失败: {e}")
            result = "查询失败"
            logger.info(f"   📤 返回: {result}")
            logger.info("🏁 [工具调用结束] get_order_details - 异常")
            return result

    def get_order_logistics(self, order_id: str) -> str:
        """
        获取订单物流状态

        Args:
            order_id: 订单号

        Returns:
            物流状态的格式化字符串
        """
        try:
            logger.info(f"🔧 [工具调用] get_order_logistics")
            logger.info(f"   🚚 查询物流状态，订单号: {order_id}")

            # 检查是否有连接ID和客户ID
            if not self.current_connection_id or not self.current_customer_id:
                logger.warning("   ⚠️ 缺少连接ID或客户ID，无法查询真实物流")
                return "未找到物流信息"

            # 调用千牛API获取真实物流信息
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    logistics_data = loop.run_until_complete(
                        qianniu_client.get_logistics_info(self.current_connection_id, order_id)
                    )
                finally:
                    loop.close()

                logistics = logistics_data.get('logistics', {})

                if not logistics:
                    result = f"未找到订单号为 {order_id} 的物流信息。"
                    logger.info(f"   📤 返回: {result}")
                    logger.info("🏁 [工具调用结束] get_order_logistics - 无物流信息")
                    return result

                result = f"🚚 物流信息\n\n"
                result += f"📦 订单号：{order_id}\n"
                result += f"📊 当前状态：{logistics.get('status', '未知状态')}\n"

                # 显示物流轨迹
                events = logistics.get('events', [])
                if events:
                    result += "\n🛤️ 物流轨迹：\n"
                    # 按时间倒序显示（最新的在前）
                    for event in reversed(events):
                        time_str = event.get('time', 'N/A')
                        status_str = event.get('status', 'N/A')
                        desc_str = event.get('description', '')
                        if desc_str and desc_str != status_str:
                            result += f"• {time_str} - {status_str}：{desc_str}\n"
                        else:
                            result += f"• {time_str} - {status_str}\n"
                else:
                    result += "\n暂无详细物流轨迹信息\n"

                # 日志输出时截断长内容
                result_preview = result[:200] + "..." if len(result) > 200 else result
                logger.info(f"   📤 返回: {result_preview}")
                logger.info(f"   ✅ 成功获取物流信息，状态: {logistics.get('status', '未知')}")
                logger.info("🏁 [工具调用结束] get_order_logistics")
                return result

            except Exception as api_error:
                logger.error(f"   ❌ 千牛API调用失败: {api_error}")
                result = "查询失败"
                logger.info(f"   📤 返回: {result}")
                logger.info("🏁 [工具调用结束] get_order_logistics - API失败")
                return result

        except Exception as e:
            logger.error(f"   ❌ 获取物流状态失败: {e}")
            result = "查询失败"
            logger.info(f"   📤 返回: {result}")
            logger.info("🏁 [工具调用结束] get_order_logistics - 异常")
            return result