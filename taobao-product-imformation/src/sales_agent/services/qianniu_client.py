"""
千牛API客户端 - 完整封装所有千牛接口，返回结构化数据
"""
import logging
import requests
import asyncio
from typing import Dict, List, Optional, Any, Union
from ..config.qianniu_config import qianniu_config
from ..models.qianniu_models import (
    QianniuApiResponse, ShopItemsResult, CustomerInfo, OrdersResult,
    ItemRecordResult, BuyerSearchResult, LogisticsInfo, CouponsResult,
    CustomerServiceResult, DispatchGroupsResult, InviteOrderResult,
    SendItemCardResult, SendCouponResult, QianniuDataParser
)
from ..utils.logger import format_json_for_log, log_json_data

logger = logging.getLogger(__name__)


class QianniuClient:
    """千牛API客户端 - 完整封装所有千牛接口"""

    def __init__(self):
        self.config = qianniu_config
        self.base_url = self.config.base_url
        self.timeout = self.config.timeout
        self.headers = self.config.get_headers()

    async def _make_request(self, method: str, endpoint: str, params: Dict = None, data: Dict = None) -> Dict:
        """
        统一的HTTP请求方法，支持重试和错误处理
        处理我们的HTTP封装返回格式

        Args:
            method: HTTP方法 (GET, POST, PUT, DELETE)
            endpoint: API端点
            params: URL参数
            data: 请求体数据

        Returns:
            处理后的API响应数据
        """
        # 检查是否启用真实API
        if not self.config.enable_real_api:
            logger.warning(f"🚫 [千牛API] 真实API已禁用，跳过请求: {method} {endpoint}")
            raise Exception("千牛API已禁用")

        url = f"{self.base_url}{endpoint}"

        # 记录请求日志
        if self.config.log_requests:
            logger.info(f"🌐 [千牛API] {method} {url}")
            if params:
                logger.info(f"   📋 参数: {params}")
            if data and self.config.log_responses:
                logger.info(f"   📦 数据: {data}")

        # 重试机制
        last_exception = None
        for attempt in range(self.config.retry_times + 1):
            try:
                # 使用 requests 进行同步请求，然后在异步上下文中运行
                def make_sync_request():
                    # 发送请求
                    if method.upper() == 'GET':
                        response = requests.get(url, params=params, headers=self.headers, timeout=self.timeout)
                    elif method.upper() == 'POST':
                        response = requests.post(url, params=params, json=data, headers=self.headers, timeout=self.timeout)
                    elif method.upper() == 'PUT':
                        response = requests.put(url, params=params, json=data, headers=self.headers, timeout=self.timeout)
                    elif method.upper() == 'DELETE':
                        response = requests.delete(url, params=params, headers=self.headers, timeout=self.timeout)
                    else:
                        raise ValueError(f"不支持的HTTP方法: {method}")

                    return response

                # 在线程池中运行同步请求
                response = await asyncio.to_thread(make_sync_request)

                # 检查响应状态
                if self.config.should_retry(status_code=response.status_code):
                    if attempt < self.config.retry_times:
                        logger.warning(f"   ⚠️ 状态码 {response.status_code}，第 {attempt + 1} 次重试...")
                        await asyncio.sleep(self.config.retry_delay * (attempt + 1))
                        continue

                response.raise_for_status()
                result = response.json()

                logger.info(f"   📦 qianniu request result: {result}")
                # 处理我们的HTTP封装返回格式
                # 格式: { success: true, data: {...}, message: "...", timestamp: "..." }
                if isinstance(result, dict) and 'success' in result:
                    if result.get('success'):
                        # 成功响应，提取data部分
                        api_data = result.get('data', {})

                        # 记录成功日志
                        if self.config.log_requests:
                            logger.info(f"   ✅ 请求成功: {response.status_code}")
                            if self.config.log_responses:
                                logger.info(f"   📥 响应: {api_data}")

                        return api_data
                    else:
                        # 业务失败
                        error_msg = result.get('message', '业务处理失败')
                        logger.error(f"   ❌ 业务失败: {error_msg}")
                        raise Exception(f"千牛API业务失败: {error_msg}")
                else:
                    # 直接返回原始格式（兼容性处理）
                    if self.config.log_requests:
                        logger.info(f"   ✅ 请求成功: {response.status_code}")
                        if self.config.log_responses:
                            logger.info(f"   📥 响应: {result}")

                    return result

            except requests.RequestException as e:
                last_exception = e
                if self.config.should_retry(exception=e) and attempt < self.config.retry_times:
                    logger.warning(f"   ⚠️ HTTP错误，第 {attempt + 1} 次重试: {e}")
                    await asyncio.sleep(self.config.retry_delay * (attempt + 1))
                    continue
                else:
                    logger.error(f"   ❌ HTTP请求失败: {e}")
                    break

            except Exception as e:
                last_exception = e
                if self.config.should_retry(exception=e) and attempt < self.config.retry_times:
                    logger.warning(f"   ⚠️ 请求异常，第 {attempt + 1} 次重试: {e}")
                    await asyncio.sleep(self.config.retry_delay * (attempt + 1))
                    continue
                else:
                    logger.error(f"   ❌ 请求处理失败: {e}")
                    break

        # 所有重试都失败了
        error_msg = f"千牛API请求失败 (重试 {self.config.retry_times} 次): {str(last_exception)}"
        raise Exception(error_msg)

    # ==================== 订单相关接口 ====================

    async def query_recent_orders(self, connection_id: str, customer_id: str, order_status: str = "") -> OrdersResult:
        """
        查询近期订单（3个月内）

        Args:
            connection_id: 千牛连接ID
            customer_id: 客户ID（加密ID）
            order_status: 订单状态筛选

        Returns:
            结构化的订单数据
        """
        try:
            raw_data = await self._make_request(
                'POST',
                '/api/v1/qianniu-api/query-recent-orders',
                data={
                    "connectionId": connection_id,
                    "customerId": customer_id,
                    "orderStatus": order_status
                }
            )

            # 解析并返回结构化数据
            result = QianniuDataParser.parse_orders(raw_data)
            return result

        except Exception as e:
            logger.error(f"查询近期订单失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            # 返回空结果
            return OrdersResult(orders=[], total=0)

    async def query_history_orders(self, connection_id: str, customer_id: str,
                                  page_num: int = 1, page_size: int = 10) -> OrdersResult:
        """
        查询历史订单

        Args:
            connection_id: 千牛连接ID
            customer_id: 客户ID（加密ID）
            page_num: 页码
            page_size: 每页数量

        Returns:
            结构化的历史订单数据
        """
        try:
            raw_data = await self._make_request(
                'POST',
                '/api/v1/qianniu-api/query-history-orders',
                data={
                    "connectionId": connection_id,
                    "customerId": customer_id,
                    "pageNum": page_num,
                    "pageSize": page_size
                }
            )

            # 解析并返回结构化数据
            return QianniuDataParser.parse_orders(raw_data)

        except Exception as e:
            logger.error(f"查询历史订单失败: {e}")
            # 返回空结果
            return OrdersResult(orders=[], total=0)

    async def create_order(self, connection_id: str, customer_id: str, order_data: Dict) -> Dict:
        """
        创建订单

        Args:
            connection_id: 千牛连接ID
            customer_id: 客户ID
            order_data: 订单数据

        Returns:
            创建结果
        """
        return await self._make_request(
            'POST',
            '/api/orders',
            params={
                "connectionId": connection_id,
                "customerId": customer_id
            },
            data=order_data
        )

    async def update_order(self, connection_id: str, customer_id: str, order_id: str, update_data: Dict) -> Dict:
        """
        更新订单

        Args:
            connection_id: 千牛连接ID
            customer_id: 客户ID
            order_id: 订单ID
            update_data: 更新数据

        Returns:
            更新结果
        """
        return await self._make_request(
            'PUT',
            f'/api/orders/{order_id}',
            params={
                "connectionId": connection_id,
                "customerId": customer_id
            },
            data=update_data
        )

    async def cancel_order(self, connection_id: str, customer_id: str, order_id: str, reason: str = "") -> Dict:
        """
        取消订单

        Args:
            connection_id: 千牛连接ID
            customer_id: 客户ID
            order_id: 订单ID
            reason: 取消原因

        Returns:
            取消结果
        """
        return await self._make_request(
            'POST',
            f'/api/orders/{order_id}/cancel',
            params={
                "connectionId": connection_id,
                "customerId": customer_id
            },
            data={"reason": reason}
        )

    # ==================== 物流相关接口 ====================

    async def get_logistics_info(self, connection_id: str, order_id: str) -> Dict:
        """
        获取物流信息

        Args:
            connection_id: 千牛连接ID
            order_id: 订单ID

        Returns:
            解析后的物流信息数据
        """
        try:
            raw_data = await self._make_request(
                'POST',
                '/api/v1/qianniu-api/query-order-logistics',
                data={
                    "connectionId": connection_id,
                    "bizOrderId": order_id
                }
            )

            # 使用解析器处理数据
            logistics_info = QianniuDataParser.parse_logistics_info(raw_data, order_id)

            # 构造标准化的物流信息
            result = {
                'logistics': {
                    'orderId': logistics_info.order_id,
                    'status': logistics_info.status or '未知状态',
                    'events': []
                }
            }

            # 如果有物流信息列表，提取物流轨迹
            if logistics_info.logistics_info_list:
                for logistics in logistics_info.logistics_info_list:
                    # 获取物流详情列表
                    logistics_details = logistics.get('logisticsDetailList', [])

                    # 添加物流轨迹事件
                    for detail in logistics_details:
                        event = {
                            'time': detail.get('time', ''),
                            'status': detail.get('title', ''),
                            'description': detail.get('desc', ''),
                            'location': ''  # 物流API中没有具体位置信息
                        }
                        result['logistics']['events'].append(event)

            # 添加收货人信息
            if logistics_info.receiver:
                result['logistics']['receiver'] = {
                    'name': logistics_info.receiver.name,
                    'mobile': logistics_info.receiver.mobile,
                    'address': logistics_info.receiver.full_address
                }

            # 添加订单详情
            if logistics_info.order_detail_list:
                result['logistics']['orderDetails'] = []
                for order_detail in logistics_info.order_detail_list:
                    items = []
                    for item in order_detail.item_detail_info_list:
                        items.append({
                            'title': item.item_title,
                            'amount': item.buy_amount,
                            'price': item.actual_pay_amount
                        })

                    result['logistics']['orderDetails'].append({
                        'mainOrderId': order_detail.main_biz_order_id,
                        'price': order_detail.price,
                        'items': items
                    })

            return result

        except Exception as e:
            logger.error(f"获取物流信息失败: {e}")
            return {'logistics': {}}

    async def get_logistics_tracking(self, connection_id: str, tracking_number: str, company: str = "") -> Dict:
        """
        获取物流跟踪信息

        Args:
            connection_id: 千牛连接ID
            tracking_number: 运单号
            company: 物流公司

        Returns:
            物流跟踪数据
        """
        return await self._make_request(
            'GET',
            '/api/logistics/tracking',
            params={
                "connectionId": connection_id,
                "trackingNumber": tracking_number,
                "company": company
            }
        )

    # ==================== 商品相关接口 ====================

    async def search_shop_items(self, connection_id: str, customer_id: str, page: int = 1, page_size: int = 8,
                               keyword: str = "", sort_key: str = "sold", desc: bool = True) -> ShopItemsResult:
        """
        搜索店铺商品

        Args:
            connection_id: 千牛连接ID
            customer_id: 客户ID（加密ID）
            page: 页码
            page_size: 每页数量
            keyword: 搜索关键词
            sort_key: 排序字段
            desc: 是否降序

        Returns:
            结构化的商品列表数据
        """
        try:
            raw_data = await self._make_request(
                'POST',
                '/api/v1/qianniu-api/search-shop-items',
                data={
                    "connectionId": connection_id,
                    "customerId": customer_id,
                    "pageSize": page_size,
                    "pageNo": page,
                    "keyWord": keyword,
                    "sortKey": sort_key,
                    "desc": desc,
                    "type": 0,
                    "queryGift": False
                }
            )

            # 解析并返回结构化数据
            return QianniuDataParser.parse_shop_items(raw_data)

        except Exception as e:
            logger.error(f"搜索店铺商品失败: {e}")
            # 返回空结果
            return ShopItemsResult(items=[], page=page, page_size=page_size, total_count=0)

    async def get_item_record(self, connection_id: str, customer_id: str) -> Dict:
        """
        查询商品记录

        Args:
            connection_id: 千牛连接ID
            customer_id: 客户ID

        Returns:
            商品记录数据
        """
        return await self._make_request(
            'POST',
            '/api/v1/qianniu-api/query-item-record',
            data={
                "connectionId": connection_id,
                "customerId": customer_id
            }
        )



    # ==================== 客户相关接口 ====================

    async def query_customer_info(self, connection_id: str, customer_id: str) -> CustomerInfo:
        """
        查询客户信息

        Args:
            connection_id: 千牛连接ID
            customer_id: 客户ID（加密ID）

        Returns:
            结构化的客户信息数据
        """
        try:
            raw_data = await self._make_request(
                'POST',
                '/api/v1/qianniu-api/query-customer-info',
                data={
                    "connectionId": connection_id,
                    "customerId": customer_id
                }
            )

            # 解析并返回结构化数据
            return QianniuDataParser.parse_customer_info(raw_data)

        except Exception as e:
            logger.error(f"查询客户信息失败: {e}")
            # 返回默认客户信息
            return CustomerInfo(
                buyer_credit_level=0,
                buyer_credit_score=0,
                send_good_rate="0%",
                is_new_customer=True,
                is_shop_fans=False,
                has_membership=False,
                vip_level=0
            )

    async def search_buyer_id(self, connection_id: str, search_query: str) -> List[BuyerSearchResult]:
        """
        搜索买家ID

        Args:
            connection_id: 千牛连接ID
            search_query: 搜索关键词（买家昵称等）

        Returns:
            买家搜索结果列表
        """
        try:
            raw_data = await self._make_request(
                'POST',
                '/api/v1/qianniu-api/search-buyer-id',
                data={
                    "connectionId": connection_id,
                    "searchQuery": search_query
                }
            )

            # 解析搜索结果
            results = []
            data_list = raw_data.get('data', [])
            for item in data_list:
                result = BuyerSearchResult(
                    account_id=str(item.get('accountId', '')),
                    encrypt_account_id=item.get('encryptAccountId', ''),
                    nick=item.get('nick', ''),
                    display_nick=item.get('displayNick', ''),
                    account_type=item.get('accountType', 0),
                    search_type=item.get('searchType', ''),
                    account_roles=item.get('accountRoles', [])
                )
                results.append(result)

            return results

        except Exception as e:
            logger.error(f"搜索买家ID失败: {e}")
            return []

    # ==================== 业务操作接口 ====================

    async def invite_order(self, connection_id: str, customer_id: str, item_props: str,
                          buyer_nick: str, biz_domain: str = "taobao", encrypt_type: str = "internal") -> InviteOrderResult:
        """
        邀请下单
invi
        Args:
            connection_id: 千牛连接ID
            customer_id: 客户ID（加密ID）
            item_props: 商品属性JSON字符串
            buyer_nick: 买家昵称
            biz_domain: 业务域
            encrypt_type: 加密类型invi

        Returns:
            邀请下单结果
        """
        try:
            logger.info(f"邀请下单参数: connectionId={connection_id}, customerId={customer_id}, itemProps={item_props}, buyerNick={buyer_nick}")
  
            raw_data = await self._make_request(
                'POST',
                '/api/v1/qianniu-api/invite-order',
                data={
                    "connectionId": connection_id,
                    "customerId": customer_id,
                    "itemProps": item_props,
                    "buyerNick": buyer_nick,
                    "bizDomain": biz_domain,
                    "encrypType": encrypt_type  # 注意这里是encrypType，不是encryptType
                }
            )

            # 解析结果 - 千牛API返回的是嵌套结构
            logger.info(f"   📦 原始API响应: {raw_data}")

            # 千牛API返回结构: {success: bool, data: {success: bool, data: {...}}}
            outer_success = raw_data.get('success', False)
            if outer_success:
                outer_data = raw_data.get('data', {})
                inner_success = outer_data.get('success', False)

                if inner_success:
                    # 成功情况下的数据解析
                    inner_data = outer_data.get('data', {})
                    order_url = inner_data.get('orderUrl', '')

                    logger.info(f"   ✅ 千牛API成功: orderUrl={order_url}")

                    return InviteOrderResult(
                        success=True,
                        order_url=order_url,
                        message="邀请下单成功"
                    )
                else:
                    # 千牛SDK内部失败（如网络异常）
                    error_msg = outer_data.get('error', '未知错误')
                    logger.warning(f"   ⚠️ 千牛SDK内部错误: {error_msg}")
                    return InviteOrderResult(success=False, message=f"邀请下单失败: {error_msg}")
            else:
                # API调用失败
                logger.error(f"   ❌ API调用失败")
                return InviteOrderResult(success=False, message="邀请下单失败")

        except Exception as e:
            logger.error(f"邀请下单失败: {e}")
            return InviteOrderResult(success=False, message=f"邀请下单失败: {str(e)}")

    async def send_item_card(self, connection_id: str, customer_id: str, item_ids: List[str], card_type: int = -1) -> SendItemCardResult:
        """
        发送商品卡片

        Args:
            connection_id: 千牛连接ID
            customer_id: 客户ID（加密ID）
            item_ids: 商品ID列表
            card_type: 卡片类型

        Returns:
            发送商品卡片结果
        """
        try:
            # 将item_ids数组转换为字符串格式，这是千牛API期望的格式
            if isinstance(item_ids, list):
                batch_item_ids_str = str(item_ids).replace("'", "")  # [123, 456] -> "[123, 456]"
            else:
                batch_item_ids_str = str(item_ids)

            logger.info(f"发送商品卡片参数: connectionId={connection_id}, customerId={customer_id}, batchItemIds={batch_item_ids_str}")

            raw_data = await self._make_request(
                'POST',
                '/api/v1/qianniu-api/send-item-card',
                data={
                    "connectionId": connection_id,
                    "customerId": customer_id,
                    "batchItemIds": batch_item_ids_str,  # 使用字符串格式
                    "type": card_type
                }
            )

            # 解析结果 - 千牛API返回的是嵌套结构
            logger.info(f"   📦 原始API响应: {raw_data}")

            # 千牛API返回结构: {success: bool, data: {success: bool, data: {sendCard: bool, sendUrl: str}}}
            outer_success = raw_data.get('success', False)
            if outer_success:
                outer_data = raw_data.get('data', {})
                inner_success = outer_data.get('success', False)

                if inner_success:
                    # 成功情况下的数据解析
                    inner_data = outer_data.get('data', {})
                    send_card = inner_data.get('sendCard', False)
                    send_url = inner_data.get('sendUrl', '')

                    logger.info(f"   ✅ 千牛API成功: sendCard={send_card}, sendUrl={send_url}")

                    return SendItemCardResult(
                        send_card=send_card,
                        send_url=send_url
                    )
                else:
                    # 千牛SDK内部失败（如网络异常）
                    error_msg = outer_data.get('error', '未知错误')
                    logger.warning(f"   ⚠️ 千牛SDK内部错误: {error_msg}")
                    return SendItemCardResult(send_card=False, send_url='')
            else:
                # API调用失败
                logger.error(f"   ❌ API调用失败")
                return SendItemCardResult(send_card=False, send_url='')

        except Exception as e:
            logger.error(f"发送商品卡片失败: {e}")
            return SendItemCardResult(send_card=False, send_url='')

    async def get_customers(self, connection_id: str) -> Dict:
        """
        获取客户列表

        Args:
            connection_id: 千牛连接ID

        Returns:
            客户列表数据
        """
        return await self._make_request(
            'GET',
            '/api/v1/qianniu-api/customers',
            params={"connectionId": connection_id}
        )

    async def search_buyer_id(self, connection_id: str, search_query: str) -> Dict:
        """
        搜索买家ID

        Args:
            connection_id: 千牛连接ID
            search_query: 搜索关键词

        Returns:
            搜索结果
        """
        return await self._make_request(
            'POST',
            '/api/v1/qianniu-api/search-buyer-id',
            data={
                "connectionId": connection_id,
                "searchQuery": search_query
            }
        )

    # ==================== 优惠券相关接口 ====================

    async def get_shop_coupons(self, connection_id: str) -> Dict:
        """
        查询店铺优惠券

        Args:
            connection_id: 千牛连接ID

        Returns:
            优惠券列表数据
        """
        return await self._make_request(
            'POST',
            '/api/v1/qianniu-api/query-shop-coupons',
            data={"connectionId": connection_id}
        )

    async def send_coupon(self, connection_id: str, customer_id: str, name: str,
                         activity_id: str, description: str = "") -> Dict:
        """
        发送优惠券

        Args:
            connection_id: 千牛连接ID
            customer_id: 客户ID
            name: 优惠券名称
            activity_id: 活动ID
            description: 描述

        Returns:
            发送结果
        """
        return await self._make_request(
            'POST',
            '/api/v1/qianniu-api/send-coupon',
            data={
                "connectionId": connection_id,
                "customerId": customer_id,
                "name": name,
                "activityId": activity_id,
                "description": description
            }
        )

    # ==================== 客服操作相关接口 ====================

    async def set_suspend(self, connection_id: str, account_id: str, is_suspend: bool = True, source: int = 1) -> Dict:
        """
        挂起/恢复会话

        Args:
            connection_id: 千牛连接ID
            account_id: 账号ID
            is_suspend: 是否挂起
            source: 来源

        Returns:
            操作结果
        """
        return await self._make_request(
            'POST',
            '/api/v1/qianniu-api/set-suspend',
            data={
                "connectionId": connection_id,
                "accountId": account_id,
                "isSuspend": is_suspend,
                "source": source
            }
        )

    async def forward_to_person(self, connection_id: str, buyer_id: str, to_id: str, reason: str,
                               app_cid: str, buyer_domain: str = "cntaobao", login_domain: str = "cntaobao") -> Dict:
        """
        转接到个人客服

        Args:
            connection_id: 千牛连接ID
            buyer_id: 买家ID
            to_id: 目标客服ID
            reason: 转接原因
            app_cid: 应用CID
            buyer_domain: 买家域
            login_domain: 登录域

        Returns:
            转接结果
        """
        return await self._make_request(
            'POST',
            '/api/v1/qianniu-api/forward-to-person',
            data={
                "connectionId": connection_id,
                "buyerId": buyer_id,
                "toId": to_id,
                "reason": reason,
                "appCid": app_cid,
                "buyerDomain": buyer_domain,
                "loginDomain": login_domain
            }
        )

    async def forward_to_group(self, connection_id: str, buyer_id: str, to_id: str, group_id: str,
                              reason: str, app_cid: str, forward_type: int = 2, charset: str = "utf-8",
                              except_users: str = "", buyer_domain: str = "cntaobao",
                              login_domain: str = "cntaobao") -> Dict:
        """
        转接到客服分组

        Args:
            connection_id: 千牛连接ID
            buyer_id: 买家ID
            to_id: 目标ID
            group_id: 分组ID
            reason: 转接原因
            app_cid: 应用CID
            forward_type: 转接类型
            charset: 字符集
            except_users: 排除用户
            buyer_domain: 买家域
            login_domain: 登录域

        Returns:
            转接结果
        """
        return await self._make_request(
            'POST',
            '/api/v1/qianniu-api/forward-to-group',
            data={
                "connectionId": connection_id,
                "buyerId": buyer_id,
                "toId": to_id,
                "groupId": group_id,
                "reason": reason,
                "appCid": app_cid,
                "forwardType": forward_type,
                "charset": charset,
                "exceptUsers": except_users,
                "buyerDomain": buyer_domain,
                "loginDomain": login_domain
            }
        )

    async def get_shop_customer_service(self, connection_id: str, page_size: int = 100) -> Dict:
        """
        获取店铺客服

        Args:
            connection_id: 千牛连接ID
            page_size: 每页数量

        Returns:
            客服列表数据
        """
        return await self._make_request(
            'POST',
            '/api/v1/qianniu-api/get-shop-customer-service',
            data={
                "connectionId": connection_id,
                "pageSize": page_size
            }
        )

    async def get_dispatch_groups(self, connection_id: str, login_domain: str = "cntaobao") -> Dict:
        """
        获取客服分组列表

        Args:
            connection_id: 千牛连接ID
            login_domain: 登录域

        Returns:
            分组列表数据
        """
        return await self._make_request(
            'POST',
            '/api/v1/qianniu-api/get-dispatch-groups',
            data={
                "connectionId": connection_id,
                "loginDomain": login_domain
            }
        )

    # ==================== 连接管理相关接口 ====================

    async def get_connections(self) -> Dict:
        """
        获取可用连接

        Returns:
            连接列表数据
        """
        return await self._make_request(
            'GET',
            '/api/v1/qianniu-api/connections'
        )

    async def test_connection(self, connection_id: str) -> Dict:
        """
        测试连接状态

        Args:
            connection_id: 千牛连接ID

        Returns:
            连接状态
        """
        return await self._make_request(
            'GET',
            f'/api/v1/qianniu-api/connections/{connection_id}/test'
        )

    # ==================== 订单管理相关接口（基于测试文件） ====================



    async def decrypt_order(self, connection_id: str, tid: str, biz_type: str = "qianniu",
                           query_by_tid: bool = True) -> Dict:
        """
        订单解密

        Args:
            connection_id: 千牛连接ID
            tid: 交易ID
            biz_type: 业务类型
            query_by_tid: 是否按TID查询

        Returns:
            解密结果
        """
        return await self._make_request(
            'POST',
            '/api/v1/qianniu-api/decrypt-order',
            data={
                "connectionId": connection_id,
                "tid": tid,
                "bizType": biz_type,
                "queryByTid": query_by_tid
            }
        )

    # ==================== 便捷方法 ====================

    async def health_check(self) -> Dict:
        """
        健康检查

        Returns:
            健康状态
        """
        return await self._make_request('GET', '/health')

    # ==================== 批量操作方法 ====================

    async def batch_get_orders(self, connection_id: str, customer_ids: List[str]) -> Dict:
        """
        批量获取多个客户的订单

        Args:
            connection_id: 千牛连接ID
            customer_ids: 客户ID列表

        Returns:
            批量订单数据
        """
        results = {}
        for customer_id in customer_ids:
            try:
                orders = await self.query_recent_orders(connection_id, customer_id)
                results[customer_id] = orders
            except Exception as e:
                logger.error(f"获取客户 {customer_id} 订单失败: {e}")
                results[customer_id] = {"error": str(e)}

        return {"batch_results": results}

    async def batch_send_coupons(self, connection_id: str, customer_ids: List[str],
                                name: str, activity_id: str, description: str = "") -> Dict:
        """
        批量发送优惠券

        Args:
            connection_id: 千牛连接ID
            customer_ids: 客户ID列表
            name: 优惠券名称
            activity_id: 活动ID
            description: 描述

        Returns:
            批量发送结果
        """
        results = {}
        for customer_id in customer_ids:
            try:
                result = await self.send_coupon(connection_id, customer_id, name, activity_id, description)
                results[customer_id] = result
            except Exception as e:
                logger.error(f"向客户 {customer_id} 发送优惠券失败: {e}")
                results[customer_id] = {"error": str(e)}

        return {"batch_results": results}


# 全局客户端实例
qianniu_client = QianniuClient()
