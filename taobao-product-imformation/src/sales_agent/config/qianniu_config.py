"""
千牛API配置文件
"""
import os
from typing import Dict, Any


class QianniuConfig:
    """千牛API配置类"""

    def __init__(self):
        # 基础配置 - 只有服务地址使用环境变量
        self.base_url = os.getenv('QIANNIU_API_BASE_URL', 'http://localhost:3002')

        # 其他配置直接在代码中设置默认值
        self.timeout = 60
        self.retry_times = 3
        self.retry_delay = 1.0

        # 功能开关
        self.enable_real_api = True
        self.enable_fallback = True
        self.enable_cache = False

        # 缓存配置
        self.cache_ttl = 300  # 5分钟
        self.cache_max_size = 1000

        # 日志配置
        self.log_level = 'INFO'
        self.log_requests = True
        self.log_responses = False

        # 限流配置
        self.rate_limit_enabled = False
        self.rate_limit_requests = 100
        self.rate_limit_window = 60  # 60秒

        # API端点配置
        self.endpoints = {
            # 订单相关
            'orders': '/api/v1/qianniu-api/query-recent-orders',
            'order_detail': '/api/v1/qianniu-api/decrypt-order',
            'order_history': '/api/v1/qianniu-api/query-history-orders',
            'invite_order': '/api/v1/qianniu-api/invite-order',

            # 物流相关
            'logistics': '/api/v1/qianniu-api/query-order-logistics',

            # 商品相关
            'shop_items': '/api/v1/qianniu-api/search-shop-items',
            'item_record': '/api/v1/qianniu-api/query-item-record',
            'send_item_card': '/api/v1/qianniu-api/send-item-card',

            # 客户相关
            'customer_info': '/api/v1/qianniu-api/query-customer-info',
            'customers': '/api/v1/qianniu-api/customers',
            'search_buyer': '/api/v1/qianniu-api/search-buyer-id',

            # 优惠券相关
            'shop_coupons': '/api/v1/qianniu-api/query-shop-coupons',
            'send_coupon': '/api/v1/qianniu-api/send-coupon',

            # 客服操作
            'set_suspend': '/api/v1/qianniu-api/set-suspend',
            'forward_person': '/api/v1/qianniu-api/forward-to-person',
            'forward_group': '/api/v1/qianniu-api/forward-to-group',
            'customer_service': '/api/v1/qianniu-api/get-shop-customer-service',
            'dispatch_groups': '/api/v1/qianniu-api/get-dispatch-groups',

            # 连接管理
            'connections': '/api/v1/qianniu-api/connections',

            # 健康检查
            'health': '/health'
        }

        # 默认参数配置
        self.default_params = {
            'page_size': 20,
            'timeout': self.timeout,
            'charset': 'utf-8',
            'biz_domain': 'taobao',
            'encrypt_type': 'internal',
            'buyer_domain': 'cntaobao',
            'login_domain': 'cntaobao'
        }

        # 错误重试配置
        self.retry_status_codes = [500, 502, 503, 504, 408, 429]
        self.retry_exceptions = ['ConnectionError', 'TimeoutError', 'HTTPError']

    def get_endpoint(self, name: str) -> str:
        """
        获取API端点

        Args:
            name: 端点名称

        Returns:
            完整的API端点URL
        """
        endpoint = self.endpoints.get(name)
        if not endpoint:
            raise ValueError(f"未知的API端点: {name}")

        return f"{self.base_url}{endpoint}"

    def get_headers(self) -> Dict[str, str]:
        """
        获取请求头

        Returns:
            请求头字典
        """
        return {
            'Content-Type': 'application/json',
            'User-Agent': 'SalesAgent/1.0'
        }

    def should_retry(self, status_code: int = None, exception: Exception = None) -> bool:
        """
        判断是否应该重试

        Args:
            status_code: HTTP状态码
            exception: 异常对象

        Returns:
            是否应该重试
        """
        if status_code and status_code in self.retry_status_codes:
            return True

        if exception:
            exception_name = exception.__class__.__name__
            if exception_name in self.retry_exceptions:
                return True

        return False

    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式

        Returns:
            配置字典
        """
        return {
            'base_url': self.base_url,
            'timeout': self.timeout,
            'retry_times': self.retry_times,
            'retry_delay': self.retry_delay,
            'enable_real_api': self.enable_real_api,
            'enable_fallback': self.enable_fallback,
            'enable_cache': self.enable_cache,
            'cache_ttl': self.cache_ttl,
            'log_level': self.log_level,
            'log_requests': self.log_requests,
            'log_responses': self.log_responses,
            'rate_limit_enabled': self.rate_limit_enabled,
            'endpoints_count': len(self.endpoints)
        }


# 全局配置实例
qianniu_config = QianniuConfig()
