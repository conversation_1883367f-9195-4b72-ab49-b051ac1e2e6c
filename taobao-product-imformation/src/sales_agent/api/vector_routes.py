"""
向量存储API路由
"""
import logging
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional, Dict, Any

from ..services.vector_store_service import VectorStoreService

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/vector", tags=["vector"])

# 全局向量存储服务实例
vector_service = VectorStoreService()


class VectorSearchRequest(BaseModel):
    """向量搜索请求模型"""
    query: str
    limit: Optional[int] = 5
    shop_id: Optional[str] = None
    category_code: Optional[str] = None
    chart_type: Optional[str] = None


class IntelligentQARequest(BaseModel):
    """智能问答请求模型"""
    question: str
    shop_id: Optional[str] = None


@router.get("/health")
async def health_check():
    """向量存储健康检查"""
    try:
        health = vector_service.health_check()
        return {
            "success": health['status'] == 'healthy',
            "data": {
                "status": health['status'],
                "collections": health['collections'],
                "message": health['message']
            }
        }
    except Exception as e:
        logger.error(f"❌ 向量存储健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")


@router.post("/sync")
async def sync_vector_data(incremental: bool = True):
    """同步向量数据

    Args:
        incremental: 是否增量同步，True=只同步新数据，False=全量同步
    """
    try:
        sync_type = "增量" if incremental else "全量"
        logger.info(f"🚀 开始{sync_type}同步向量数据...")
        result = vector_service.sync_all_data(incremental=incremental)

        return {
            "success": result['success'],
            "data": result.get('data', {}),
            "message": result['message']
        }
    except Exception as e:
        logger.error(f"❌ 同步向量数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"同步向量数据失败: {str(e)}")


@router.post("/initialize")
async def initialize_vector_store():
    """初始化向量存储"""
    try:
        logger.info("🚀 开始初始化向量存储...")
        vector_service.initialize()

        return {
            "success": True,
            "data": {"timestamp": "2024-01-01T00:00:00Z"},
            "message": "向量存储初始化完成"
        }
    except Exception as e:
        logger.error(f"❌ 初始化向量存储失败: {e}")
        raise HTTPException(status_code=500, detail=f"初始化向量存储失败: {str(e)}")


@router.post("/search/products")
async def search_products(request: VectorSearchRequest):
    """搜索产品"""
    try:
        if not vector_service.client:
            vector_service.initialize()

        filters = {'shop_id': request.shop_id} if request.shop_id else None
        results = vector_service.search_products(request.query, request.limit, filters)

        return {
            "success": True,
            "data": [
                {
                    "id": result.id,
                    "content": result.content,
                    "metadata": result.metadata,
                    "score": result.score
                }
                for result in results
            ],
            "message": f"找到 {len(results)} 个相关产品"
        }
    except Exception as e:
        logger.error(f"❌ 搜索产品失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索产品失败: {str(e)}")


@router.post("/search/qa-knowledge")
async def search_qa_knowledge(request: VectorSearchRequest):
    """搜索问答知识库"""
    try:
        if not vector_service.client:
            vector_service.initialize()

        filters = {}
        if request.category_code:
            filters['category_code'] = request.category_code
        if request.shop_id:
            filters['shop_id'] = request.shop_id

        results = vector_service.search_qa_knowledge(
            request.query,
            request.limit,
            filters if filters else None
        )

        return {
            "success": True,
            "data": [
                {
                    "id": result.id,
                    "content": result.content,
                    "metadata": result.metadata,
                    "score": result.score
                }
                for result in results
            ],
            "message": f"找到 {len(results)} 个相关问答"
        }
    except Exception as e:
        logger.error(f"❌ 搜索问答知识库失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索问答知识库失败: {str(e)}")





@router.post("/intelligent-qa")
async def intelligent_qa(request: IntelligentQARequest):
    """智能问答 - 综合搜索"""
    try:
        if not vector_service.client:
            vector_service.initialize()

        result = vector_service.intelligent_qa(request.question, request.shop_id)

        return {
            "success": result['success'],
            "data": result['data'],
            "message": result['message'],
            "suggestion": result.get('suggestion', '')
        }
    except Exception as e:
        logger.error(f"❌ 智能问答失败: {e}")
        raise HTTPException(status_code=500, detail=f"智能问答失败: {str(e)}")


@router.post("/import-qa-excel")
async def import_qa_from_excel(excel_path: str, clear_existing: bool = True):
    """从Excel文件导入问答知识库"""
    try:
        if not vector_service.client:
            vector_service.initialize()

        count = vector_service.import_qa_from_excel(excel_path, clear_existing)

        return {
            "success": True,
            "data": {"imported_count": count},
            "message": f"成功从Excel导入 {count} 条问答记录"
        }
    except Exception as e:
        logger.error(f"❌ Excel导入失败: {e}")
        raise HTTPException(status_code=500, detail=f"Excel导入失败: {str(e)}")


@router.get("/collections/status")
async def get_collections_status():
    """获取集合状态"""
    try:
        if not vector_service.client:
            return {
                "success": False,
                "data": {},
                "message": "向量存储未初始化"
            }

        collections_status = {}
        for key, collection in vector_service.collections.items():
            try:
                count = collection.count()
                collections_status[key] = {
                    "count": count,
                    "status": "healthy"
                }
            except Exception as e:
                collections_status[key] = {
                    "count": 0,
                    "status": "error",
                    "error": str(e)
                }

        return {
            "success": True,
            "data": collections_status,
            "message": "集合状态获取成功"
        }
    except Exception as e:
        logger.error(f"❌ 获取集合状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取集合状态失败: {str(e)}")


@router.delete("/collections/{collection_name}")
async def clear_collection(collection_name: str):
    """清空指定集合"""
    try:
        if not vector_service.client:
            vector_service.initialize()

        if collection_name not in vector_service.collections:
            raise HTTPException(status_code=404, detail=f"集合 {collection_name} 不存在")

        collection = vector_service.collections[collection_name]
        collection.delete()

        return {
            "success": True,
            "data": {"collection": collection_name},
            "message": f"集合 {collection_name} 已清空"
        }
    except Exception as e:
        logger.error(f"❌ 清空集合失败: {e}")
        raise HTTPException(status_code=500, detail=f"清空集合失败: {str(e)}")


@router.get("/statistics")
async def get_vector_statistics():
    """获取向量数据统计信息"""
    try:
        if not vector_service.client:
            vector_service.initialize()

        result = vector_service.get_vector_statistics()
        return result
    except Exception as e:
        logger.error(f"❌ 获取向量统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取向量统计失败: {str(e)}")


@router.get("/search/{collection_name}")
async def search_collection_vectors(collection_name: str, query: str, limit: int = 10):
    """搜索指定集合中的相似向量"""
    try:
        if not vector_service.client:
            vector_service.initialize()

        if not query:
            raise HTTPException(status_code=400, detail="查询参数不能为空")

        result = vector_service.search_similar_vectors(collection_name, query, limit)
        return result
    except Exception as e:
        logger.error(f"❌ 搜索向量失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索向量失败: {str(e)}")


# 注释掉启动时的自动初始化，改为按需初始化
# @router.on_event("startup")
# async def startup_event():
#     """启动时初始化向量存储"""
#     try:
#         logger.info("🚀 启动时初始化向量存储...")
#         vector_service.initialize()
#         logger.info("✅ 向量存储初始化完成")
#     except Exception as e:
#         logger.error(f"❌ 启动时初始化向量存储失败: {e}")
#         # 不抛出异常，允许服务继续启动
