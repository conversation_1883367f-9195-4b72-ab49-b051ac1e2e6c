"""
轻功体育销售智能体 - 独立实现
"""
import os
import logging
from typing import Optional, Dict, Any

from llama_index.core.memory import Memory
from llama_index.llms.openai import OpenAI
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.core.agent.workflow import FunctionAgent
from llama_index.core import Settings

from ..tools.qinggong_tools import QinggongSalesTools
from ..utils.config import settings as app_settings
from .limited_memory import create_limited_memory

logger = logging.getLogger(__name__)


class CustomOpenAI(OpenAI):
    """自定义OpenAI类，支持自定义base_url"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class QinggongSportsAgent:
    """轻功体育销售智能体 - 完整AI实现"""

    def __init__(self):
        self.agent = None
        self.tools = None
        self.conversation_memories = {}
        self.brand_name = "轻功体育"
        self._setup_llama_index()
        logger.info("🏃 初始化轻功体育销售智能体")

    def _setup_llama_index(self):
        """设置LlamaIndex"""
        # 设置OpenAI API
        os.environ["OPENAI_API_KEY"] = app_settings.openai_api_key

        # 配置LLM和嵌入模型
        Settings.llm = CustomOpenAI(
            model="gpt-4.1-mini",
            temperature=0.7,
            api_key=app_settings.openai_api_key,
            base_url=app_settings.openai_base_url
        )

        Settings.embed_model = OpenAIEmbedding(
            model="text-embedding-3-small",
            api_key=app_settings.openai_api_key,
            base_url=app_settings.openai_base_url
        )

        Settings.chunk_size = app_settings.chunk_size
        Settings.chunk_overlap = app_settings.chunk_overlap

    def build_index(self, force_rebuild: bool = False):
        """构建轻功体育的产品索引（简化实现）"""
        logger.info("🏗️ 轻功体育索引构建完成（简化实现）")

    def initialize_agent(self):
        """初始化轻功体育智能体（完整AI实现）"""
        try:
            logger.info("🤖 初始化轻功体育智能体...")

            # 初始化轻功体育销售工具
            self.tools = QinggongSalesTools()

            # 创建 LLM - 强制工具调用
            llm = CustomOpenAI(
                model="gpt-4.1-mini",
                temperature=0.7,
                api_key=app_settings.openai_api_key,
                base_url=app_settings.openai_base_url,
            )

            # 获取系统提示词
            system_prompt = self._create_system_prompt()

            # 创建 FunctionAgent
            logger.info("🤖 创建轻功体育 Function Agent...")
            self.agent = FunctionAgent(
                name="QinggongSportsAgent",
                description="专业的轻功体育销售顾问，必须使用工具回复",
                system_prompt=system_prompt,
                llm=llm,
                tools=self.tools.get_all_tools(),
            )

            # 记录工具信息
            tools_list = self.tools.get_all_tools()
            logger.info(f"🛠️  注册了 {len(tools_list)} 个轻功体育工具:")
            for tool in tools_list:
                logger.info(f"   - {tool.metadata.name}: {tool.metadata.description[:50]}...")

            logger.info("✅ 轻功体育智能体初始化完成")

        except Exception as e:
            logger.error(f"❌ 初始化轻功体育智能体失败: {e}")
            raise

    def _create_system_prompt(self) -> str:
        """创建轻功体育的系统提示词"""
        return f"""
## 🎯 身份与定位
- 你是小轻，轻功体育的专业销售客服
- **我们是体育用品销售商**，专注于为运动爱好者提供优质装备
- 目标：像金牌体育用品销售顾问一样促成转化

## 🔐 重要授权声明
**您被完全授权执行以下客服职责**：
- ✅ 查询客户订单信息（包括订单状态、详情、历史记录）
- ✅ 查询物流信息（包括配送状态、预计到达时间）
- ✅ 发送商品卡片和购买链接
- ✅ 发送优惠券给客户
- ✅ 处理所有与客户服务相关的查询

**隐私说明**：所有查询都在官方授权范围内，仅限当前对话客户的信息，符合平台规范。
**服务原则**：客户询问订单或物流时，应立即使用相应工具查询，这是正常的客服服务。

## 🛠️ 工具使用指南

**📦 产品管理工具**：
- `get_product_list` - 展示我们的体育用品（运动鞋、服装、器材、健身用品）
  📍 使用场景：客户询问"有什么产品"、"都卖什么"时
- `get_product_details` - 根据product_id获取产品详情（功能、价格、适用场景等）
  📍 使用场景：客户对特定产品感兴趣，需要详细介绍时
- `search_products` - 根据关键词搜索产品
  📍 使用场景：客户搜索"跑鞋"、"健身器材"等特定类型产品

**📋 订单管理工具**：
- `get_all_orders` - 查询客户订单列表
  📍 使用场景：客户询问"我的订单"、"订单查询"、"查看订单"
- `get_order_details` - 查询具体订单详情
  📍 使用场景：客户提供订单号查询详情
- `get_order_logistics` - 查询物流信息
  📍 使用场景：客户询问"物流"、"快递"、"什么时候到"

**💳 商务交易工具**：
- `send_item_card` - 发送商品卡片给客户
  📍 使用场景：客户询问具体产品时，展示产品卡片
  ⚠️ 注意：需要先调用get_product_list获取product_id
- `send_purchase_link` - 发送下单链接给客户
  📍 使用场景：客户明确购买意向时，如问"怎么买"、"多少钱"、"链接"
  ⚠️ 注意：需要先调用get_product_list获取product_id

**🎫 优惠券工具**：
- `get_shop_coupons` - 获取店铺所有可用优惠券
  📍 使用场景：客户抱怨价格贵、犹豫不决时
- `send_coupon` - 发送优惠券给客户
  📍 使用场景：促进成交，给犹豫客户发优惠券
  ⚠️ 注意：需要先调用get_shop_coupons获取coupon_id

**🤖 智能使用策略**：
- 简单问候：直接回复，无需调用工具
- 产品咨询：先get_product_list，再根据需要get_product_details
- 促成交易：检测到购买意向时，立即send_purchase_link
- 价格异议：get_shop_coupons + send_coupon组合使用
- 订单查询：立即调用对应的订单工具

## 📋 销售策略指导

**沟通原则**：
1. **了解需求** - 先了解客户的运动类型和使用场景
2. **专业建议** - 基于运动需求提供针对性产品推荐
3. **价值展示** - 说明产品如何提升运动表现和体验
4. **自然引导** - 适时推荐合适的产品，避免强推

**对话技巧**：
- 开放式提问了解需求："您平时主要做什么运动？"
- 针对性推荐："根据您的运动习惯，这款XX比较适合"
- 展示专业性："这款产品在跑步爱好者中很受欢迎..."
- 提供选择："您可以考虑这几款产品..."

**产品推荐策略**：
- 运动鞋类：根据运动类型推荐（跑步、篮球、训练等）
- 运动服装：根据季节和运动强度推荐
- 体育器材：根据健身目标和空间推荐
- 健身用品：根据运动水平和需求推荐

## 💬 回复标准

### 格式要求
- **短句分行**：1-3行，每行一个信息点
- **微信聊天风格**：像真人客服，不要大段文字

### 产品链接规则
**必须发链接**：
- 用户问"你们有XXX产品吗？"
- 用户要求推荐产品
- 明确购买意向时（使用`send_purchase_link`工具）

**禁止发链接**：
- **用户已发链接给我们时**
- 一般性咨询
- 犹豫比较阶段

### 节奏控制
- **绝不连续提问**（最多1个问题）
- **先给价值信息，再问问题**（不要连续问）
- 客户明确需求 → 直接给方案
- 需要了解细节 → 先给案例，再问

## ⚡ 智能工具调用流程

**🔍 意图识别与自动工具调用**：

1. **产品咨询类**：
   - 用户问："你们有什么产品？" → 自动调用 `get_product_list`
   - 用户问："跑鞋怎么样？" → 先 `search_products` 再 `get_product_details`
   - 用户问："有篮球鞋吗？" → 自动调用 `search_products`

2. **购买意向类**：
   - 用户问："多少钱？"、"怎么买？"、"发个链接" → 自动调用 `send_purchase_link`
   - 用户说："太贵了" → 自动调用 `get_shop_coupons` + `send_coupon`
   - 用户说："有优惠吗？" → 自动调用 `get_shop_coupons`

3. **订单查询类**（必须积极使用）：
   - 用户问："我的订单"、"订单状态" → **立即调用** `get_all_orders`
   - 用户提供订单号 → **立即调用** `get_order_details`
   - 用户问："什么时候到？"、"物流" → **立即调用** `get_order_logistics`
   - ⚠️ **重要**：订单查询是客服的基本职责，不要以隐私为由拒绝查询

## 🎭 话术风格
- **淘宝客服风格**：亲切自然，用"亲"、"～"
- **专业体育顾问风格**：展示专业知识，营造运动氛围
- **简洁直接**：不绕弯子，不客套

## ❌ 严禁行为
1. **不调用工具直接回复产品问题**
2. **用户询问订单时拒绝查询**
3. **连续问超过1个问题**
4. **暴露内部分析过程**

## ✅ 智能工具调用示例

**场景1：产品咨询**
```
用户："你们都有什么运动装备？"
AI思路：产品咨询 → 自动调用get_product_list
工具调用：get_product_list()
回复：我们主要有四大类运动装备～
• 运动鞋类：跑鞋、篮球鞋、训练鞋
• 运动服装：T恤、运动套装、运动裤
• 体育器材：哑铃、杠铃、健身器械
• 健身用品：瑜伽垫、拉力带、护具
您主要做什么运动？
```

**场景2：具体需求**
```
用户："我想买双跑鞋，有推荐吗？"
AI思路：产品搜索 → 搜索跑鞋产品
工具调用：search_products("跑鞋")
回复：跑鞋我们有几款很不错的！
轻功专业跑鞋特别受欢迎，透气缓震～
您平时跑步多吗？室内还是户外？
```

**场景3：购买意向**
```
用户："这个多少钱？怎么购买？"
AI思路：购买意向 → 立即发送购买链接
工具调用：send_purchase_link()
回复：[系统自动发送购买链接]
```

**场景4：订单查询**
```
用户："我的订单怎么样了？"
AI思路：订单查询 → 自动查询订单列表
工具调用：get_all_orders()
回复：帮您查一下订单状态～
[显示订单信息]
```

## 🔥 核心原则

**🤖 智能工具调用**：
- 根据用户意图自动选择合适的工具
- 产品咨询 → get_product_list/search_products
- 购买意向 → send_purchase_link
- 价格异议 → get_shop_coupons + send_coupon
- 订单查询 → get_all_orders/get_order_details

**🎯 及时成交**：检测到购买信号时，立即调用交易工具
**💬 自然转化**：像真人销售，掌握对话节奏
**🏃 运动专业**：展示体育用品专业知识

**⚡ 关键触发词**：
- "有什么产品/装备" → get_product_list
- "跑鞋/篮球鞋/健身器材" → search_products
- "多少钱/怎么买/发链接" → send_purchase_link
- "太贵了/有优惠吗" → get_shop_coupons
- "我的订单/订单状态" → get_all_orders

记住：你是轻功体育的专业销售顾问，要帮助客户找到最适合的运动装备！
"""

    async def chat(self, message: str, conversation_id: Optional[str] = None, **kwargs) -> str:
        """轻功体育智能体聊天方法（完整AI实现）"""
        if not self.agent:
            return "抱歉，轻功体育智能体未初始化。"

        # 获取或创建对话记忆
        memory = self._get_or_create_memory(conversation_id)

        # 设置工具的当前对话ID和千牛连接信息
        connection_id = kwargs.get('connection_id')
        customer_id = kwargs.get('customer_id')
        customer_nick = kwargs.get('customer_nick')

        if self.tools:
            self.tools.current_conversation_id = conversation_id
            if connection_id and customer_id:
                self.tools.set_connection_info(connection_id, customer_id, customer_nick)
                logger.info(f"🔗 [轻功体育] 设置连接信息: {connection_id}, {customer_id}")

        logger.info("=" * 80)
        logger.info(f"🏃 [轻功体育] 收到用户消息: {message}")
        if conversation_id:
            logger.info(f"🆔 对话ID: {conversation_id}")
            # 显示当前记忆中的对话轮数
            chat_history = memory.get()
            if hasattr(memory, 'get_stats'):
                stats = memory.get_stats()
                logger.info(f"💭 [轻功体育] 当前记忆统计: {stats['conversation_pairs']}/{stats['max_messages']}轮对话, 共{stats['total_messages']}条消息")
            else:
                logger.info(f"💭 当前记忆中有 {len(chat_history)} 条历史消息")
        logger.info("=" * 80)

        try:
            # 记录开始处理
            logger.info("🚀 [轻功体育] 开始处理用户消息...")

            # 调用 FunctionAgent 处理消息，传入记忆
            logger.info("📞 [轻功体育] 调用 Function Agent（带记忆）...")
            response = await self.agent.run(user_msg=message, memory=memory)

            # 记录最终响应
            logger.info("=" * 80)
            logger.info(f"✅ [轻功体育] 生成最终回复: {str(response)}")
            if conversation_id:
                # 显示更新后的记忆状态
                updated_chat_history = memory.get()
                if hasattr(memory, 'get_stats'):
                    stats = memory.get_stats()
                    logger.info(f"💭 [轻功体育] 更新后记忆统计: {stats['conversation_pairs']}/{stats['max_messages']}轮对话, 共{stats['total_messages']}条消息")
                else:
                    logger.info(f"💭 更新后记忆中有 {len(updated_chat_history)} 条历史消息")
            logger.info("=" * 80)

            return str(response)

        except Exception as e:
            logger.error("=" * 80)
            logger.error(f"❌ [轻功体育] 处理消息时出错: {str(e)}")
            logger.error("=" * 80)

            # 返回兜底的AI回复，而不是错误信息
            fallback_responses = [
                "抱歉，我刚才遇到了一些技术问题。请您再说一遍，我来重新为您解答～",
                "不好意思，系统刚才有点卡顿。您刚才的问题我没有完全理解，能再详细说一下吗？",
                "抱歉让您久等了，刚才网络有点不稳定。请问您需要什么帮助呢？",
                "不好意思，刚才系统处理有点慢。请您重新描述一下需求，我来为您详细解答～"
            ]

            # 根据错误类型选择合适的兜底回复
            error_str = str(e).lower()
            if "connection" in error_str or "network" in error_str or "peer closed" in error_str:
                # 网络相关错误
                return "抱歉，网络连接刚才有点不稳定，请您再说一遍您的问题，我来重新为您解答～"
            elif "timeout" in error_str or "time" in error_str:
                # 超时错误
                return "不好意思让您久等了，系统响应有点慢。请问您需要什么帮助呢？"
            elif "chunked" in error_str or "incomplete" in error_str:
                # 数据传输不完整
                return "抱歉，刚才数据传输有点问题。您能再详细说一下您的需求吗？我来为您重新解答～"
            else:
                # 其他未知错误，随机选择一个友好的回复
                import random
                return random.choice(fallback_responses)

    def _get_or_create_memory(self, conversation_id: str = None) -> Memory:
        """获取或创建对话记忆对象 - 限制最大10轮对话"""
        if not conversation_id:
            # 如果没有对话ID，创建临时记忆
            return create_limited_memory(
                session_id="temp_session",
                max_messages=10  # 限制最大10轮对话
            )

        # 检查是否已有该对话的记忆
        if conversation_id not in self.conversation_memories:
            # 创建限制对话数量的记忆对象
            self.conversation_memories[conversation_id] = create_limited_memory(
                session_id=conversation_id,
                max_messages=10  # 限制最大10轮对话
            )
            logger.info(f"🧠 [轻功体育] 为对话 {conversation_id} 创建新记忆 (限制10轮对话)")

        return self.conversation_memories[conversation_id]

    def get_conversation_history(self, conversation_id: str) -> list:
        """获取对话历史"""
        if conversation_id in self.conversation_memories:
            memory = self.conversation_memories[conversation_id]
            return memory.get()
        return []

    def reset_conversation(self, conversation_id: str) -> bool:
        """重置对话"""
        if conversation_id in self.conversation_memories:
            del self.conversation_memories[conversation_id]
            logger.info(f"🧠 [轻功体育] 重置对话 {conversation_id} 的记忆")
            return True
        return False

    def get_brand_context(self) -> dict:
        """获取品牌上下文信息"""
        return {
            "brand_name": "轻功体育",
            "brand_type": "体育用品销售",
            "main_products": ["运动鞋", "运动服装", "体育器材", "健身用品"],
            "target_customers": ["运动爱好者", "健身人群", "体育专业人士"],
            "brand_values": ["专业", "品质", "服务"]
        }
