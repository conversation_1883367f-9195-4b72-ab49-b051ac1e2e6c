"""
限制对话数量的记忆管理类
"""
import logging
from typing import List, Optional
from llama_index.core.memory import Memory
from llama_index.core.base.llms.types import ChatMessage, MessageRole

logger = logging.getLogger(__name__)


class LimitedChatMemory:
    """限制对话数量的记忆包装类"""

    def __init__(self,
                 session_id: str = "default",
                 max_messages: int = 10,
                 token_limit: int = 30000,
                 chat_history_token_ratio: float = 0.7):
        """
        初始化限制对话数量的记忆

        Args:
            session_id: 会话ID
            max_messages: 最大消息数量（用户+助手消息对数）
            token_limit: token限制
            chat_history_token_ratio: 聊天历史token比例
        """
        # 使用标准的Memory.from_defaults创建内部记忆
        self._memory = Memory.from_defaults(
            session_id=session_id,
            token_limit=token_limit,
            chat_history_token_ratio=chat_history_token_ratio
        )
        self.max_messages = max_messages
        self.session_id = session_id

        logger.info(f"🧠 创建限制记忆: session_id={session_id}, max_messages={max_messages}")

    def get(self, initial_token_count: int = 0, **kwargs) -> List[ChatMessage]:
        """获取聊天历史"""
        # 先从内部记忆获取消息
        messages = self._memory.get(initial_token_count, **kwargs)
        # 应用消息数量限制
        return self._apply_message_limit(messages)

    def get_all(self) -> List[ChatMessage]:
        """获取所有聊天历史"""
        messages = self._memory.get_all()
        return self._apply_message_limit(messages)

    def put(self, message: ChatMessage) -> None:
        """添加消息到记忆中"""
        # 直接添加到内部记忆
        self._memory.put(message)

        # 获取当前所有消息并应用限制
        all_messages = self._memory.get_all()
        limited_messages = self._apply_message_limit(all_messages)

        # 如果消息被限制了，重新设置内部记忆
        if len(limited_messages) < len(all_messages):
            self._memory.set(limited_messages)
            removed_count = len(all_messages) - len(limited_messages)
            logger.info(f"🗑️ 记忆清理: 移除了 {removed_count} 条旧消息，保留最新 {len(limited_messages)} 条消息")

        logger.debug(f"💭 添加消息到记忆: role={message.role}, 当前消息数={len(limited_messages)}")

    async def aput(self, message: ChatMessage) -> None:
        """异步添加消息到记忆中"""
        # 如果内部记忆有异步方法，使用异步方法
        if hasattr(self._memory, 'aput'):
            await self._memory.aput(message)
        else:
            # 否则使用同步方法
            self._memory.put(message)

        # 获取当前所有消息并应用限制
        all_messages = self._memory.get_all()
        limited_messages = self._apply_message_limit(all_messages)

        # 如果消息被限制了，重新设置内部记忆
        if len(limited_messages) < len(all_messages):
            self._memory.set(limited_messages)
            removed_count = len(all_messages) - len(limited_messages)
            logger.info(f"🗑️ 记忆清理: 移除了 {removed_count} 条旧消息，保留最新 {len(limited_messages)} 条消息")

        logger.debug(f"💭 异步添加消息到记忆: role={message.role}, 当前消息数={len(limited_messages)}")

    def _apply_message_limit(self, messages: List[ChatMessage]) -> List[ChatMessage]:
        """应用消息数量限制"""
        if not messages:
            return messages

        # 计算对话轮数（用户+助手为一轮）
        conversation_pairs = 0
        temp_messages = []

        # 从最新消息开始倒序处理
        for message in reversed(messages):
            temp_messages.insert(0, message)

            if message.role == MessageRole.USER:
                conversation_pairs += 1

                # 如果超过最大对话轮数，停止添加更多消息
                if conversation_pairs > self.max_messages:
                    break

        return temp_messages

    def set(self, messages: List[ChatMessage]) -> None:
        """设置聊天历史"""
        # 应用消息限制后设置到内部记忆
        limited_messages = self._apply_message_limit(messages)
        self._memory.set(limited_messages)
        logger.debug(f"💭 设置记忆: 消息数={len(limited_messages)}")

    def reset(self) -> None:
        """重置记忆"""
        old_messages = self._memory.get_all()
        old_count = len(old_messages)
        self._memory.reset()
        logger.info(f"🔄 重置记忆: 清除了 {old_count} 条消息")

    async def areset(self) -> None:
        """异步重置记忆"""
        old_messages = self._memory.get_all()
        old_count = len(old_messages)

        if hasattr(self._memory, 'areset'):
            await self._memory.areset()
        else:
            self._memory.reset()

        logger.info(f"🔄 异步重置记忆: 清除了 {old_count} 条消息")

    def to_string(self) -> str:
        """转换为字符串表示"""
        messages = self._memory.get_all()
        if not messages:
            return "空记忆"

        result = []
        for i, message in enumerate(messages):
            role_name = "用户" if message.role == MessageRole.USER else "助手"
            result.append(f"{i+1}. {role_name}: {message.content[:50]}...")

        return f"记忆({len(messages)}条消息):\n" + "\n".join(result)

    def get_conversation_count(self) -> int:
        """获取对话轮数"""
        messages = self._memory.get_all()
        user_messages = [msg for msg in messages if msg.role == MessageRole.USER]
        return len(user_messages)

    def get_stats(self) -> dict:
        """获取记忆统计信息"""
        messages = self._memory.get_all()
        user_count = len([msg for msg in messages if msg.role == MessageRole.USER])
        assistant_count = len([msg for msg in messages if msg.role == MessageRole.ASSISTANT])

        return {
            "session_id": self.session_id,
            "total_messages": len(messages),
            "user_messages": user_count,
            "assistant_messages": assistant_count,
            "conversation_pairs": user_count,
            "max_messages": self.max_messages,
            "is_at_limit": user_count >= self.max_messages
        }

    # 添加其他可能需要的属性和方法，确保兼容性
    @property
    def token_limit(self):
        """获取token限制"""
        return getattr(self._memory, 'token_limit', 30000)

    @property
    def chat_history_token_ratio(self):
        """获取聊天历史token比例"""
        return getattr(self._memory, 'chat_history_token_ratio', 0.7)

    def __getattr__(self, name):
        """代理其他方法到内部记忆对象"""
        if hasattr(self._memory, name):
            return getattr(self._memory, name)
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")


def create_limited_memory(session_id: str = "default", max_messages: int = 10) -> LimitedChatMemory:
    """
    创建限制对话数量的记忆对象

    Args:
        session_id: 会话ID
        max_messages: 最大对话轮数（默认10轮）

    Returns:
        LimitedChatMemory: 限制记忆对象
    """
    return LimitedChatMemory(
        session_id=session_id,
        max_messages=max_messages,
        token_limit=30000,
        chat_history_token_ratio=0.7
    )
