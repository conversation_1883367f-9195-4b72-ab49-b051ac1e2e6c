# 千牛智能客服API集成说明

本文档说明如何使用集成到系统中的千牛智能客服API功能。

## 功能概述

系统已集成了千牛智能客服的各种API接口，包括：

- 邀请下单
- 订单查询（近三个月、历史订单）
- 商品推荐和卡片发送
- 物流信息查询
- 客户信息查询
- 优惠券管理
- 客服转接
- 店铺商品搜索
- 等等...

## 架构说明

```
HTTP请求 -> 后端API -> WebSocket通信 -> 千牛注入脚本 -> 千牛API -> 返回结果
```

1. **HTTP测试接口**: 提供RESTful API用于测试和调用千牛功能
2. **WebSocket通信**: 后端与千牛客户端之间的实时通信
3. **注入脚本**: 在千牛页面中注入的JavaScript代码，负责实际的API调用
4. **千牛API**: 千牛平台提供的原生API接口

## 使用方法

### 1. 确保千牛客户端连接

首先确保千牛客户端已经通过WebSocket连接到系统：

```bash
# 检查可用连接
curl http://localhost:3000/api/v1/qianniu-api/connections
```

### 2. 调用API接口

所有API调用都需要提供 `connectionId` 参数，用于指定要使用的千牛连接。

#### 示例：查询店铺优惠券

```bash
curl -X POST http://localhost:3000/api/v1/qianniu-api/query-shop-coupons \
  -H "Content-Type: application/json" \
  -d '{"connectionId": "your-connection-id"}'
```

#### 示例：查询客户信息

```bash
curl -X POST http://localhost:3000/api/v1/qianniu-api/query-customer-info \
  -H "Content-Type: application/json" \
  -d '{
    "connectionId": "your-connection-id",
    "encryptId": "RAzN8BQgcJTmZi38Xpj5igvrzDYFz"
  }'
```

#### 示例：获取客户列表

```bash
# 获取指定连接的客户列表（分页）
curl "http://localhost:3000/api/v1/qianniu-api/customers?connectionId=your-connection-id&page=1&limit=20"

# 搜索客户
curl "http://localhost:3000/api/v1/qianniu-api/customers?connectionId=your-connection-id&search=张三&page=1&limit=10"
```

#### 示例：获取客户详情

```bash
curl http://localhost:3000/api/v1/qianniu-api/customers/customer-id-here
```

#### 示例：邀请下单

```bash
curl -X POST http://localhost:3000/api/v1/qianniu-api/invite-order \
  -H "Content-Type: application/json" \
  -d '{
    "connectionId": "your-connection-id",
    "encryptId": "1597305140",
    "items": "[{\"itemId\":************,\"skuId\":5191249981449,\"quantity\":1,\"context\":{}}]",
    "changePrice": true,
    "changeType": 1,
    "channelFee": 2.22
  }'
```

### 3. 可用的API接口

#### 客户管理接口

| API名称 | 端点 | 方法 | 描述 |
|---------|------|------|------|
| 获取客户列表 | `/customers` | GET | 获取指定连接的客户列表，支持分页和搜索 |
| 获取客户详情 | `/customers/:id` | GET | 获取单个客户的详细信息和千牛账号信息 |

#### 千牛智能客服接口

| API名称 | 端点 | 方法 | 描述 |
|---------|------|------|------|
| 邀请下单 | `/invite-order` | POST | 向客户发送下单邀请 |
| 查询近期订单 | `/query-recent-orders` | POST | 查询客户近三个月订单 |
| 查询历史订单 | `/query-history-orders` | POST | 查询客户历史订单 |
| 发送商品卡片 | `/send-item-card` | POST | 向客户发送商品推荐卡片 |
| 查询物流信息 | `/query-order-logistics` | POST | 查询订单物流状态 |
| 查询商品记录 | `/query-item-record` | POST | 查询客户商品浏览记录 |
| 查询客户信息 | `/query-customer-info` | POST | 获取客户详细信息 |
| 查询店铺优惠券 | `/query-shop-coupons` | POST | 获取店铺可用优惠券 |
| 发送优惠券 | `/send-coupon` | POST | 向客户发送优惠券 |
| 查询买家ID | `/search-buyer-id` | POST | 根据昵称查询买家ID |
| 设置挂起状态 | `/set-suspend` | POST | 设置客服挂起状态 |
| 订单解密 | `/decrypt-order` | POST | 解密订单信息 |
| 搜索店铺商品 | `/search-shop-items` | POST | 搜索店铺商品 |
| 获取店铺客服 | `/get-shop-customer-service` | POST | 获取店铺客服列表 |
| 转接到个人 | `/forward-to-person` | POST | 将会话转接给指定客服 |
| 转接到分组 | `/forward-to-group` | POST | 将会话转接给客服分组 |
| 通用API调用 | `/call-api` | POST | 调用任意千牛API |

### 4. 运行测试

使用提供的测试脚本验证功能：

```bash
# 安装依赖（如果需要）
npm install undici

# 运行测试脚本
node test-qianniu-api.js
```

## 响应格式

### HTTP接口响应格式

所有HTTP API调用都返回统一的响应格式：

```json
{
  "success": true,
  "message": "API调用完成",
  "data": {
    "requestId": "test_1234567890_abc123",
    "success": true,
    "data": {
      // 千牛API返回的实际数据
    },
    "method": "mtop.taobao.qianniu.cs.user.shop.coupon.query",
    "api": "mtop.taobao.qianniu.cs.user.shop.coupon.query",
    "ret": ["SUCCESS::调用成功"],
    "version": "1.0"
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### 千牛SDK原生响应格式

基于真实测试用例，千牛SDK的原生响应格式为：

```json
{
  "api": "mtop.taobao.qianniu.cs.user.query",
  "data": {
    // 具体的业务数据
  },
  "ret": ["SUCCESS::调用成功"],
  "v": "1.0"
}
```

我们的接口会保留这个原生格式，同时添加统一的包装层。

## 错误处理

如果API调用失败，会返回错误信息：

```json
{
  "success": false,
  "message": "API调用失败",
  "error": "连接不存在: invalid-connection-id",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## 注意事项

1. **连接状态**: 确保千牛客户端已连接并且WebSocket连接正常
2. **参数格式**: 严格按照千牛API要求的格式传递参数
3. **权限限制**: 某些API可能需要特定的权限或配置
4. **频率限制**: 避免过于频繁的API调用，建议添加适当的延迟
5. **错误处理**: 妥善处理API调用失败的情况

## 开发集成

如果需要在自己的代码中集成这些API，可以参考以下方式：

### JavaScript/TypeScript

```javascript
async function callQianniuApi(apiType, params, connectionId) {
  const response = await fetch(`/api/v1/qianniu-api/${apiType}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      connectionId,
      ...params
    })
  });

  return await response.json();
}

// 使用示例
const result = await callQianniuApi('query-customer-info', {
  encryptId: 'RAzN8BQgcJTmZi38Xpj5igvrzDYFz'
}, 'your-connection-id');
```

### Python

```python
import requests

def call_qianniu_api(api_type, params, connection_id):
    url = f"http://localhost:3000/api/v1/qianniu-api/{api_type}"
    data = {"connectionId": connection_id, **params}

    response = requests.post(url, json=data)
    return response.json()

# 使用示例
result = call_qianniu_api('query-customer-info', {
    'encryptId': 'RAzN8BQgcJTmZi38Xpj5igvrzDYFz'
}, 'your-connection-id')
```

## 故障排除

### 常见问题

1. **连接不存在**: 检查千牛客户端是否正常连接
2. **API调用超时**: 检查网络连接和千牛客户端状态
3. **参数错误**: 确认参数格式和必需字段
4. **权限不足**: 确认千牛账号具有相应的API调用权限

### 调试方法

1. 查看服务器日志获取详细错误信息
2. 使用浏览器开发者工具检查WebSocket连接状态
3. 在千牛客户端控制台查看注入脚本的执行情况

## 更新日志

- **v1.1.0**: 基于真实SDK测试用例优化
  - 优化了API调用格式，完全符合千牛SDK标准
  - 改进了返回值处理，保留原生响应格式
  - 添加了更多API接口（搜索买家ID、订单解密等）
  - 增强了错误处理机制
  - 更新了测试用例，使用真实的测试数据

- **v1.0.0**: 初始版本，支持基础的千牛API调用功能
  - 集成了15+个常用的千牛智能客服API
  - 提供完整的HTTP测试接口
  - 支持WebSocket实时通信
  - 包含自动化测试脚本

## 优化特性

### 🎯 基于真实SDK测试用例
- 所有API调用格式都基于真实的千牛SDK测试用例
- 参数格式和返回值结构完全符合千牛官方标准
- 使用真实的测试数据进行验证

### 🔧 统一的调用方式
- 统一使用 `imsdk.invoke('application.invokeMTopChannelService', {...})` 调用
- 保持与千牛客户端原生调用方式一致
- 支持所有千牛MTOP接口

### 📊 完整的响应格式
- 保留千牛SDK原生响应格式（api, data, ret, v）
- 添加统一的HTTP包装层
- 提供详细的成功/失败状态信息
